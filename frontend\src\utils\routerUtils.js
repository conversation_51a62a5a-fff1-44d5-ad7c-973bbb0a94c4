/**
 * 路由工具函數
 * 處理導航、重定向和路由相關的常見問題
 */

import router from '@/router'

/**
 * 檢查兩個路由是否相同
 * @param {Object} route1 - 第一個路由
 * @param {Object} route2 - 第二個路由
 * @returns {boolean} 是否相同
 */
export function isSameRoute(route1, route2) {
  if (!route1 || !route2) return false
  
  // 比較路徑
  if (route1.path !== route2.path) return false
  
  // 比較查詢參數
  const query1 = route1.query || {}
  const query2 = route2.query || {}
  
  const keys1 = Object.keys(query1).sort()
  const keys2 = Object.keys(query2).sort()
  
  if (keys1.length !== keys2.length) return false
  
  for (const key of keys1) {
    if (query1[key] !== query2[key]) return false
  }
  
  return true
}

/**
 * 安全的路由導航，避免重複導航錯誤
 * @param {string|Object} to - 目標路由
 * @param {Object} options - 導航選項
 * @returns {Promise} 導航 Promise
 */
export async function safeNavigate(to, options = {}) {
  try {
    const currentRoute = router.currentRoute.value
    const targetRoute = typeof to === 'string' ? { path: to } : to
    
    // 檢查是否要導航到相同位置
    if (isSameRoute(currentRoute, targetRoute)) {
      console.debug('跳過重複導航:', to)
      return Promise.resolve()
    }
    
    // 執行導航
    if (options.replace) {
      return await router.replace(to)
    } else {
      return await router.push(to)
    }
  } catch (error) {
    // 忽略重複導航錯誤
    if (error.message && error.message.includes('Avoided redundant navigation')) {
      console.debug('忽略重複導航錯誤:', error.message)
      return Promise.resolve()
    }
    
    // 重新拋出其他錯誤
    console.error('導航失敗:', error)
    throw error
  }
}

/**
 * 安全的重定向，帶有循環檢測
 * @param {string} targetPath - 目標路徑
 * @param {string} fallbackPath - 回退路徑
 * @param {Object} options - 選項
 * @returns {Promise} 導航 Promise
 */
export async function safeRedirect(targetPath, fallbackPath = '/', options = {}) {
  const currentRoute = router.currentRoute.value
  
  // 檢查循環重定向
  if (currentRoute.query?.redirect === targetPath) {
    console.warn(`檢測到循環重定向，使用回退路徑: ${fallbackPath}`)
    return safeNavigate(fallbackPath, { replace: true })
  }
  
  // 避免重定向到當前位置
  if (currentRoute.path === targetPath) {
    console.debug(`已在目標路徑: ${targetPath}`)
    return Promise.resolve()
  }
  
  return safeNavigate(targetPath, { replace: true, ...options })
}

/**
 * 帶有重定向參數的登入導航
 * @param {string} redirectPath - 登入後要重定向的路徑
 * @returns {Promise} 導航 Promise
 */
export async function navigateToLogin(redirectPath = null) {
  const loginRoute = {
    path: '/login',
    query: redirectPath ? { redirect: redirectPath } : {}
  }
  
  return safeNavigate(loginRoute, { replace: true })
}

/**
 * 登入後的重定向處理
 * @param {Object} route - 當前路由
 * @param {string} defaultPath - 預設重定向路徑
 * @returns {Promise} 導航 Promise
 */
export async function handlePostLoginRedirect(route, defaultPath = '/chat') {
  const redirectPath = route.query?.redirect
  
  if (redirectPath) {
    // 驗證重定向路徑的安全性
    if (isValidRedirectPath(redirectPath)) {
      return safeRedirect(redirectPath)
    } else {
      console.warn('不安全的重定向路徑:', redirectPath)
    }
  }
  
  return safeRedirect(defaultPath)
}

/**
 * 驗證重定向路徑是否安全
 * @param {string} path - 要驗證的路徑
 * @returns {boolean} 是否安全
 */
export function isValidRedirectPath(path) {
  if (!path || typeof path !== 'string') return false
  
  // 不允許外部 URL
  if (path.startsWith('http://') || path.startsWith('https://')) return false
  
  // 不允許 javascript: 協議
  if (path.toLowerCase().startsWith('javascript:')) return false
  
  // 必須以 / 開頭
  if (!path.startsWith('/')) return false
  
  // 不允許某些敏感路徑
  const forbiddenPaths = ['/admin/dangerous', '/system/config']
  if (forbiddenPaths.some(forbidden => path.startsWith(forbidden))) return false
  
  return true
}

/**
 * 獲取當前路由的完整路徑（包含查詢參數）
 * @returns {string} 完整路徑
 */
export function getCurrentFullPath() {
  return router.currentRoute.value.fullPath
}

/**
 * 檢查當前是否在指定路徑
 * @param {string} path - 要檢查的路徑
 * @returns {boolean} 是否在指定路徑
 */
export function isCurrentPath(path) {
  return router.currentRoute.value.path === path
}

/**
 * 路由導航的 Promise 包裝器，提供更好的錯誤處理
 * @param {Function} navigationFn - 導航函數
 * @returns {Promise} 包裝後的 Promise
 */
export function wrapNavigation(navigationFn) {
  return new Promise((resolve, reject) => {
    try {
      const result = navigationFn()
      
      if (result && typeof result.then === 'function') {
        result
          .then(resolve)
          .catch(error => {
            if (error.message && error.message.includes('Avoided redundant navigation')) {
              console.debug('忽略重複導航:', error.message)
              resolve()
            } else {
              reject(error)
            }
          })
      } else {
        resolve(result)
      }
    } catch (error) {
      if (error.message && error.message.includes('Avoided redundant navigation')) {
        console.debug('忽略重複導航:', error.message)
        resolve()
      } else {
        reject(error)
      }
    }
  })
}

/**
 * 批量導航錯誤處理器
 * @param {Array} navigationPromises - 導航 Promise 陣列
 * @returns {Promise} 處理結果
 */
export async function handleBatchNavigation(navigationPromises) {
  const results = await Promise.allSettled(navigationPromises)
  
  const errors = results
    .filter(result => result.status === 'rejected')
    .map(result => result.reason)
    .filter(error => !error.message?.includes('Avoided redundant navigation'))
  
  if (errors.length > 0) {
    console.error('批量導航中發生錯誤:', errors)
    throw new Error(`批量導航失敗: ${errors.length} 個錯誤`)
  }
  
  return results.map(result => result.value)
}

export default {
  safeNavigate,
  safeRedirect,
  navigateToLogin,
  handlePostLoginRedirect,
  isValidRedirectPath,
  getCurrentFullPath,
  isCurrentPath,
  isSameRoute,
  wrapNavigation,
  handleBatchNavigation
}
