"""
BMP 關鍵字和過濾規則配置
定義 BPM 相關關鍵字和可配置的過濾參數
"""

import os
from typing import Dict, List, Any, Set
from dataclasses import dataclass, field

@dataclass
class FilterConfig:
    """過濾器配置類"""
    min_confidence_threshold: float = 0.3
    rejection_threshold: float = 0.5
    strict_mode: bool = False
    enable_semantic_analysis: bool = True
    
    # 權重配置
    weight_exact_match: float = 1.0
    weight_partial_match: float = 0.7
    weight_context_match: float = 0.5
    
    # 長度配置
    min_text_length: int = 2
    max_text_length: int = 10000
    
    # 統計配置
    enable_statistics: bool = True
    log_detailed_analysis: bool = False

class BmpKeywordConfig:
    """BMP 關鍵字配置類"""
    
    def __init__(self):
        # 核心 BPM 關鍵字
        self.core_keywords = {
            "流程管理": [
                "business process", "business processes", "流程管理", "業務流程",
                "workflow", "工作流程", "作業流程", "bpm", "business process management",
                "process", "processes", "流程", "程序", "procedure", "procedures"
            ],
            "組織管理": [
                "organization", "organizational", "組織", "組織架構", "hierarchy", "階層",
                "department", "departments", "部門", "division", "divisions", "單位",
                "team", "teams", "團隊", "group", "groups", "群組",
                "management", "manage", "管理", "governance", "治理"
            ],
            "專案管理": [
                "project", "projects", "專案", "task", "tasks", "任務",
                "activity", "activities", "活動", "milestone", "milestones", "里程碑",
                "deliverable", "deliverables", "交付項目", "planning", "計畫", "規劃"
            ],
            "審核流程": [
                "approval", "approvals", "核准", "核准流程", "簽核", "審核",
                "review", "reviews", "審查", "檢視", "evaluate", "evaluation",
                "authorize", "authorization", "授權", "validate", "validation", "驗證"
            ],
            "政策規範": [
                "policy", "policies", "政策", "rule", "rules", "規則",
                "regulation", "regulations", "法規", "compliance", "合規",
                "standard", "standards", "標準", "guideline", "guidelines", "指導原則"
            ],
            "效率改善": [
                "efficiency", "effective", "效率", "效能", "optimization", "優化",
                "improvement", "改善", "streamline", "精簡", "automate", "automation", "自動化",
                "performance", "績效", "productivity", "生產力"
            ]
        }
        
        # 相關關鍵字（較低權重）
        self.related_keywords = {
            "決策支援": [
                "decision", "decisions", "決策", "analysis", "analyses", "分析",
                "metrics", "指標", "kpi", "key performance indicator", "關鍵績效指標",
                "dashboard", "儀表板", "report", "reporting", "報告"
            ],
            "協作溝通": [
                "collaboration", "collaborate", "協作", "communication", "溝通",
                "coordination", "協調", "stakeholder", "stakeholders", "利害關係人",
                "meeting", "meetings", "會議", "discussion", "討論"
            ],
            "文件管理": [
                "document", "documents", "文件", "documentation", "文檔",
                "record", "records", "記錄", "form", "forms", "表單",
                "template", "templates", "範本", "archive", "檔案"
            ],
            "系統工具": [
                "system", "systems", "系統", "tool", "tools", "工具",
                "software", "軟體", "application", "applications", "應用程式",
                "platform", "platforms", "平台", "interface", "介面"
            ],
            "監控追蹤": [
                "monitor", "monitoring", "監控", "track", "tracking", "追蹤",
                "control", "控制", "oversight", "監督", "audit", "稽核",
                "compliance", "合規", "quality", "品質"
            ]
        }
        
        # 拒絕關鍵字（明確非 BPM 內容）
        self.rejection_keywords = {
            "技術支援": [
                "password", "密碼", "login", "登入", "reset", "重設",
                "technical support", "技術支援", "system error", "系統錯誤",
                "bug", "error", "錯誤", "troubleshoot", "故障排除"
            ],
            "個人事務": [
                "personal", "個人", "private", "私人", "family", "家庭",
                "vacation", "假期", "holiday", "休假", "sick leave", "病假",
                "birthday", "生日", "wedding", "婚禮"
            ],
            "娛樂內容": [
                "weather", "天氣", "news", "新聞", "sports", "運動",
                "entertainment", "娛樂", "movie", "電影", "music", "音樂",
                "game", "遊戲", "social media", "社群媒體", "celebrity", "名人"
            ],
            "金融投資": [
                "investment", "投資", "stock", "股票", "trading", "交易",
                "cryptocurrency", "加密貨幣", "bitcoin", "比特幣",
                "forex", "外匯", "gambling", "賭博"
            ],
            "醫療健康": [
                "medical", "醫療", "health", "健康", "doctor", "醫生",
                "medicine", "藥物", "treatment", "治療", "symptom", "症狀",
                "disease", "疾病", "hospital", "醫院"
            ],
            "法律諮詢": [
                "legal advice", "法律建議", "lawsuit", "訴訟", "court", "法院",
                "lawyer", "律師", "attorney", "律師", "litigation", "訴訟"
            ]
        }
        
        # 警告關鍵字（需要特別注意）
        self.warning_keywords = {
            "機密資訊": [
                "confidential", "機密", "secret", "秘密", "classified", "機密資料",
                "restricted", "限制", "sensitive", "敏感"
            ],
            "認證資訊": [
                "password", "密碼", "credential", "憑證", "token", "令牌",
                "api key", "api金鑰", "access key", "存取金鑰"
            ],
            "財務敏感": [
                "salary", "薪資", "bonus", "獎金", "budget", "預算",
                "cost", "成本", "revenue", "營收", "profit", "利潤",
                "financial", "財務", "accounting", "會計"
            ],
            "人事敏感": [
                "hire", "雇用", "fire", "解僱", "layoff", "裁員",
                "promotion", "升遷", "demotion", "降職", "resignation", "辭職",
                "performance review", "績效評核"
            ]
        }
        
        # 主題分類模式
        self.topic_patterns = {
            "流程優化": {
                "patterns": [
                    r'\b(優化|optimize|improve|改善|enhance|提升)\b',
                    r'\b(流程|process|workflow|procedure)\b',
                    r'\b(效率|efficiency|效能|performance)\b'
                ],
                "weight": 1.0,
                "description": "業務流程優化相關"
            },
            "組織管理": {
                "patterns": [
                    r'\b(組織|organization|架構|structure)\b',
                    r'\b(部門|department|team|團隊|group)\b',
                    r'\b(管理|management|coordinate|協調)\b'
                ],
                "weight": 0.9,
                "description": "組織架構和管理相關"
            },
            "專案管理": {
                "patterns": [
                    r'\b(專案|project|計畫|plan|planning)\b',
                    r'\b(任務|task|activity|milestone|里程碑)\b',
                    r'\b(進度|progress|schedule|排程|timeline)\b'
                ],
                "weight": 0.9,
                "description": "專案管理和執行相關"
            },
            "政策制定": {
                "patterns": [
                    r'\b(政策|policy|規則|rule|regulation)\b',
                    r'\b(合規|compliance|標準|standard)\b',
                    r'\b(指導|guideline|程序|procedure)\b'
                ],
                "weight": 0.8,
                "description": "政策制定和合規相關"
            },
            "系統整合": {
                "patterns": [
                    r'\b(系統|system|整合|integration)\b',
                    r'\b(自動化|automation|automatic)\b',
                    r'\b(工具|tool|software|軟體|platform)\b'
                ],
                "weight": 0.7,
                "description": "系統整合和自動化相關"
            },
            "績效分析": {
                "patterns": [
                    r'\b(績效|performance|分析|analysis)\b',
                    r'\b(指標|metric|kpi|measurement)\b',
                    r'\b(報告|report|dashboard|儀表板)\b'
                ],
                "weight": 0.8,
                "description": "績效分析和報告相關"
            }
        }
        
        # 載入環境變數配置
        self._load_custom_config()
    
    def _load_custom_config(self):
        """載入自定義配置"""
        
        # 載入自定義核心關鍵字
        custom_core = os.getenv("BMP_CUSTOM_CORE_KEYWORDS")
        if custom_core:
            try:
                import json
                custom_keywords = json.loads(custom_core)
                for category, keywords in custom_keywords.items():
                    if category in self.core_keywords:
                        self.core_keywords[category].extend(keywords)
                    else:
                        self.core_keywords[category] = keywords
            except json.JSONDecodeError:
                # 如果是簡單的逗號分隔字串
                keywords = [kw.strip() for kw in custom_core.split(",")]
                self.core_keywords.setdefault("自定義", []).extend(keywords)
        
        # 載入自定義拒絕關鍵字
        custom_rejection = os.getenv("BMP_CUSTOM_REJECTION_KEYWORDS")
        if custom_rejection:
            keywords = [kw.strip() for kw in custom_rejection.split(",")]
            self.rejection_keywords.setdefault("自定義拒絕", []).extend(keywords)
        
        # 載入自定義警告關鍵字
        custom_warning = os.getenv("BMP_CUSTOM_WARNING_KEYWORDS")
        if custom_warning:
            keywords = [kw.strip() for kw in custom_warning.split(",")]
            self.warning_keywords.setdefault("自定義警告", []).extend(keywords)
    
    def get_all_core_keywords(self) -> Set[str]:
        """獲取所有核心關鍵字"""
        all_keywords = set()
        for keywords in self.core_keywords.values():
            all_keywords.update(keywords)
        return all_keywords
    
    def get_all_related_keywords(self) -> Set[str]:
        """獲取所有相關關鍵字"""
        all_keywords = set()
        for keywords in self.related_keywords.values():
            all_keywords.update(keywords)
        return all_keywords
    
    def get_all_rejection_keywords(self) -> Set[str]:
        """獲取所有拒絕關鍵字"""
        all_keywords = set()
        for keywords in self.rejection_keywords.values():
            all_keywords.update(keywords)
        return all_keywords
    
    def get_all_warning_keywords(self) -> Set[str]:
        """獲取所有警告關鍵字"""
        all_keywords = set()
        for keywords in self.warning_keywords.values():
            all_keywords.update(keywords)
        return all_keywords
    
    def get_category_keywords(self, category: str) -> List[str]:
        """獲取特定分類的關鍵字"""
        return (self.core_keywords.get(category, []) + 
                self.related_keywords.get(category, []))
    
    def add_keywords(self, category: str, keywords: List[str], keyword_type: str = "core"):
        """動態添加關鍵字"""
        if keyword_type == "core":
            self.core_keywords.setdefault(category, []).extend(keywords)
        elif keyword_type == "related":
            self.related_keywords.setdefault(category, []).extend(keywords)
        elif keyword_type == "rejection":
            self.rejection_keywords.setdefault(category, []).extend(keywords)
        elif keyword_type == "warning":
            self.warning_keywords.setdefault(category, []).extend(keywords)
    
    def remove_keywords(self, category: str, keywords: List[str], keyword_type: str = "core"):
        """動態移除關鍵字"""
        target_dict = None
        if keyword_type == "core":
            target_dict = self.core_keywords
        elif keyword_type == "related":
            target_dict = self.related_keywords
        elif keyword_type == "rejection":
            target_dict = self.rejection_keywords
        elif keyword_type == "warning":
            target_dict = self.warning_keywords
        
        if target_dict and category in target_dict:
            for keyword in keywords:
                if keyword in target_dict[category]:
                    target_dict[category].remove(keyword)
    
    def get_keyword_stats(self) -> Dict[str, Any]:
        """獲取關鍵字統計"""
        return {
            "core_keywords": {
                "categories": len(self.core_keywords),
                "total_keywords": sum(len(kws) for kws in self.core_keywords.values()),
                "categories_detail": {cat: len(kws) for cat, kws in self.core_keywords.items()}
            },
            "related_keywords": {
                "categories": len(self.related_keywords),
                "total_keywords": sum(len(kws) for kws in self.related_keywords.values()),
                "categories_detail": {cat: len(kws) for cat, kws in self.related_keywords.items()}
            },
            "rejection_keywords": {
                "categories": len(self.rejection_keywords),
                "total_keywords": sum(len(kws) for kws in self.rejection_keywords.values()),
                "categories_detail": {cat: len(kws) for cat, kws in self.rejection_keywords.items()}
            },
            "warning_keywords": {
                "categories": len(self.warning_keywords),
                "total_keywords": sum(len(kws) for kws in self.warning_keywords.values()),
                "categories_detail": {cat: len(kws) for cat, kws in self.warning_keywords.items()}
            },
            "topic_patterns": {
                "topics": len(self.topic_patterns),
                "topics_detail": list(self.topic_patterns.keys())
            }
        }

# 建立全域配置實例
keyword_config = BmpKeywordConfig()
filter_config = FilterConfig()