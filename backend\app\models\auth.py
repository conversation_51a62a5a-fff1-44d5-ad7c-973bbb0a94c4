"""
認證相關資料模型
定義 Azure AD 認證、JWT token 和認證流程的資料結構
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum

class AuthStatus(str, Enum):
    """認證狀態列舉"""
    SUCCESS = "success"
    FAILED = "failed"
    EXPIRED = "expired"
    INVALID = "invalid"
    FORBIDDEN = "forbidden"

class TokenType(str, Enum):
    """Token 類型列舉"""
    ACCESS_TOKEN = "access_token"
    REFRESH_TOKEN = "refresh_token"
    ID_TOKEN = "id_token"

class AuthRequest(BaseModel):
    """認證請求模型"""
    
    token: str = Field(..., description="Azure AD access token")
    token_type: TokenType = Field(default=TokenType.ACCESS_TOKEN, description="Token 類型")
    client_info: Optional[Dict[str, Any]] = Field(default=None, description="客戶端資訊")
    
    class Config:
        schema_extra = {
            "example": {
                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
                "token_type": "access_token",
                "client_info": {
                    "user_agent": "Mozilla/5.0...",
                    "ip_address": "*************"
                }
            }
        }

class AuthResponse(BaseModel):
    """認證回應模型"""
    
    status: AuthStatus = Field(..., description="認證狀態")
    message: str = Field(..., description="認證訊息")
    user_id: Optional[str] = Field(default=None, description="用戶 ID")
    session_id: Optional[str] = Field(default=None, description="會話 ID")
    expires_at: Optional[datetime] = Field(default=None, description="會話過期時間")
    permissions: Optional[List[str]] = Field(default=[], description="用戶權限列表")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "success",
                "message": "認證成功",
                "user_id": "12345678-1234-1234-1234-123456789abc",
                "session_id": "sess_abcd1234efgh5678",
                "expires_at": "2024-01-01T12:00:00Z",
                "permissions": ["chat", "view_history"]
            }
        }

class TokenValidationRequest(BaseModel):
    """Token 驗證請求模型"""
    
    token: str = Field(..., description="要驗證的 token")
    validate_expiry: bool = Field(default=True, description="是否檢查過期時間")
    validate_audience: bool = Field(default=True, description="是否檢查受眾")
    
    class Config:
        schema_extra = {
            "example": {
                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
                "validate_expiry": True,
                "validate_audience": True
            }
        }

class TokenValidationResponse(BaseModel):
    """Token 驗證回應模型"""
    
    is_valid: bool = Field(..., description="Token 是否有效")
    payload: Optional[Dict[str, Any]] = Field(default=None, description="Token payload")
    error: Optional[str] = Field(default=None, description="驗證錯誤訊息")
    expires_at: Optional[datetime] = Field(default=None, description="Token 過期時間")
    
    class Config:
        schema_extra = {
            "example": {
                "is_valid": True,
                "payload": {
                    "oid": "12345678-1234-1234-1234-123456789abc",
                    "upn": "<EMAIL>",
                    "name": "John Doe"
                },
                "error": None,
                "expires_at": "2024-01-01T12:00:00Z"
            }
        }

class SessionRequest(BaseModel):
    """會話建立請求模型"""
    
    user_id: str = Field(..., description="用戶 ID")
    employee_id: str = Field(..., description="員工 ID")
    client_info: Optional[Dict[str, Any]] = Field(default=None, description="客戶端資訊")
    ttl_minutes: Optional[int] = Field(default=30, description="會話存活時間（分鐘）")
    
    @validator('ttl_minutes')
    def validate_ttl(cls, v):
        if v is not None and (v < 5 or v > 1440):  # 5分鐘到24小時
            raise ValueError('TTL 必須在 5-1440 分鐘之間')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "12345678-1234-1234-1234-123456789abc",
                "employee_id": "EMP001",
                "client_info": {
                    "user_agent": "Mozilla/5.0...",
                    "ip_address": "*************"
                },
                "ttl_minutes": 30
            }
        }

class SessionResponse(BaseModel):
    """會話建立回應模型"""
    
    session_id: str = Field(..., description="會話 ID")
    created_at: datetime = Field(..., description="建立時間")
    expires_at: datetime = Field(..., description="過期時間")
    user_id: str = Field(..., description="用戶 ID")
    
    class Config:
        schema_extra = {
            "example": {
                "session_id": "sess_abcd1234efgh5678",
                "created_at": "2024-01-01T10:00:00Z",
                "expires_at": "2024-01-01T10:30:00Z",
                "user_id": "12345678-1234-1234-1234-123456789abc"
            }
        }

class SessionInfo(BaseModel):
    """會話資訊模型"""
    
    session_id: str = Field(..., description="會話 ID")
    user_id: str = Field(..., description="用戶 ID")
    employee_id: str = Field(..., description="員工 ID")
    created_at: datetime = Field(..., description="建立時間")
    expires_at: datetime = Field(..., description="過期時間")
    last_activity: datetime = Field(..., description="最後活動時間")
    is_active: bool = Field(..., description="會話是否活躍")
    client_info: Optional[Dict[str, Any]] = Field(default=None, description="客戶端資訊")
    
    @property
    def is_expired(self) -> bool:
        """檢查會話是否過期"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def remaining_minutes(self) -> int:
        """剩餘分鐘數"""
        if self.is_expired:
            return 0
        delta = self.expires_at - datetime.utcnow()
        return int(delta.total_seconds() / 60)
    
    class Config:
        schema_extra = {
            "example": {
                "session_id": "sess_abcd1234efgh5678",
                "user_id": "12345678-1234-1234-1234-123456789abc",
                "employee_id": "EMP001",
                "created_at": "2024-01-01T10:00:00Z",
                "expires_at": "2024-01-01T10:30:00Z",
                "last_activity": "2024-01-01T10:15:00Z",
                "is_active": True,
                "client_info": {
                    "user_agent": "Mozilla/5.0...",
                    "ip_address": "*************"
                }
            }
        }

class LogoutRequest(BaseModel):
    """登出請求模型"""
    
    session_id: Optional[str] = Field(default=None, description="要登出的會話 ID")
    all_sessions: bool = Field(default=False, description="是否登出所有會話")
    
    class Config:
        schema_extra = {
            "example": {
                "session_id": "sess_abcd1234efgh5678",
                "all_sessions": False
            }
        }

class LogoutResponse(BaseModel):
    """登出回應模型"""
    
    status: str = Field(..., description="登出狀態")
    message: str = Field(..., description="登出訊息")
    sessions_terminated: int = Field(..., description="終止的會話數量")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "success",
                "message": "成功登出",
                "sessions_terminated": 1
            }
        }

class PermissionCheck(BaseModel):
    """權限檢查模型"""
    
    user_id: str = Field(..., description="用戶 ID")
    permission: str = Field(..., description="要檢查的權限")
    resource: Optional[str] = Field(default=None, description="資源識別碼")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "12345678-1234-1234-1234-123456789abc",
                "permission": "chat.send_message",
                "resource": "bpm_chatbot"
            }
        }

class PermissionResult(BaseModel):
    """權限檢查結果模型"""
    
    has_permission: bool = Field(..., description="是否有權限")
    permission: str = Field(..., description="檢查的權限")
    reason: Optional[str] = Field(default=None, description="拒絕原因")
    
    class Config:
        schema_extra = {
            "example": {
                "has_permission": True,
                "permission": "chat.send_message",
                "reason": None
            }
        }

class AuthErrorResponse(BaseModel):
    """認證錯誤回應模型"""
    
    error_code: str = Field(..., description="錯誤代碼")
    error_message: str = Field(..., description="錯誤訊息")
    error_description: Optional[str] = Field(default=None, description="詳細錯誤描述")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="錯誤時間")
    
    class Config:
        schema_extra = {
            "example": {
                "error_code": "INVALID_TOKEN",
                "error_message": "Token 無效或已過期",
                "error_description": "提供的 Azure AD token 無法通過驗證",
                "timestamp": "2024-01-01T10:00:00Z"
            }
        }