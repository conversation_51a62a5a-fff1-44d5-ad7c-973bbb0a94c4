"""
聊天服務單元測試
測試 ChatService 的所有核心功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from datetime import datetime, timedelta
import uuid

from app.services.chat_service import ChatService, ChatServiceError
from app.models.message import Message, MessageRole, MessageStatus, MessageCreateRequest
from app.models.session import Session, SessionStatus
from app.models.model import ModelType, ModelRequest


class TestChatService:
    """聊天服務測試類"""

    @pytest.fixture
    def chat_service(self, mock_redis, mock_openai_client, mock_claude_client, mock_gemini_client):
        """創建聊天服務實例"""
        with patch('app.services.chat_service.get_redis_client', return_value=mock_redis), \
             patch('app.services.chat_service.get_session_service') as mock_session_service, \
             patch('app.services.chat_service.get_openai_client', return_value=mock_openai_client), \
             patch('app.services.chat_service.get_claude_client', return_value=mock_claude_client), \
             patch('app.services.chat_service.get_gemini_client', return_value=mock_gemini_client):
            
            mock_session_service.return_value = AsyncMock()
            service = ChatService()
            return service

    @pytest.mark.asyncio
    async def test_create_message_success(self, chat_service, sample_session, mock_redis):
        """測試成功創建訊息"""
        # 準備測試資料
        request = MessageCreateRequest(
            session_id=sample_session.session_id,
            content="測試訊息內容",
            role=MessageRole.USER
        )
        
        # 模擬 Redis 回應
        mock_redis.get.return_value = sample_session.model_dump_json()
        mock_redis.set.return_value = True
        
        # 執行測試
        result = await chat_service.create_message(request)
        
        # 驗證結果
        assert result is not None
        assert result.content == "測試訊息內容"
        assert result.role == MessageRole.USER
        assert result.session_id == sample_session.session_id
        assert result.status == MessageStatus.COMPLETED
        
        # 驗證 Redis 呼叫
        mock_redis.get.assert_called()
        mock_redis.set.assert_called()

    @pytest.mark.asyncio
    async def test_create_message_invalid_session(self, chat_service):
        """測試無效會話的訊息創建"""
        request = MessageCreateRequest(
            session_id="invalid_session_id",
            content="測試訊息",
            role=MessageRole.USER
        )
        
        with pytest.raises(ChatServiceError) as exc_info:
            await chat_service.create_message(request)
        
        assert "會話不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_message_content_too_long(self, chat_service, sample_session, mock_redis):
        """測試內容過長的訊息創建"""
        # 準備超長內容
        long_content = "x" * 20000  # 超過預設限制 10000
        
        request = MessageCreateRequest(
            session_id=sample_session.session_id,
            content=long_content,
            role=MessageRole.USER
        )
        
        mock_redis.get.return_value = sample_session.model_dump_json()
        
        with pytest.raises(ChatServiceError) as exc_info:
            await chat_service.create_message(request)
        
        assert "訊息內容過長" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_process_message_with_bmp_filter(self, chat_service, sample_message, mock_bmp_filter):
        """測試使用 BPM 過濾器處理訊息"""
        with patch('app.services.chat_service.bmp_filter', mock_bmp_filter):
            mock_bmp_filter.is_bmp_related.return_value = True
            
            result = await chat_service.process_message(sample_message)
            
            assert result is not None
            mock_bmp_filter.is_bmp_related.assert_called_with(sample_message.content)

    @pytest.mark.asyncio
    async def test_process_message_non_bmp_filtered_out(self, chat_service, sample_message, mock_bmp_filter):
        """測試非 BPM 相關訊息被過濾"""
        with patch('app.services.chat_service.bmp_filter', mock_bmp_filter):
            mock_bmp_filter.is_bmp_related.return_value = False
            
            with pytest.raises(ChatServiceError) as exc_info:
                await chat_service.process_message(sample_message)
            
            assert "非 BPM 相關問題" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_route_to_model_chatgpt(self, chat_service, mock_openai_client):
        """測試路由到 ChatGPT 模型"""
        request = ModelRequest(
            model_type=ModelType.CHATGPT,
            messages=[{"role": "user", "content": "測試訊息"}],
            temperature=0.7,
            max_tokens=1000
        )
        
        result = await chat_service.route_to_model(request)
        
        assert result is not None
        assert "ChatGPT" in result["content"]
        mock_openai_client.chat_completion.assert_called_once()

    @pytest.mark.asyncio
    async def test_route_to_model_claude(self, chat_service, mock_claude_client):
        """測試路由到 Claude 模型"""
        request = ModelRequest(
            model_type=ModelType.CLAUDE,
            messages=[{"role": "user", "content": "測試訊息"}],
            temperature=0.7,
            max_tokens=1000
        )
        
        result = await chat_service.route_to_model(request)
        
        assert result is not None
        assert "Claude" in result["content"]
        mock_claude_client.chat_completion.assert_called_once()

    @pytest.mark.asyncio
    async def test_route_to_model_unavailable(self, chat_service):
        """測試路由到不可用的模型"""
        request = ModelRequest(
            model_type=ModelType.GEMINI,  # 在 fixture 中設定為不健康
            messages=[{"role": "user", "content": "測試訊息"}],
            temperature=0.7,
            max_tokens=1000
        )
        
        with pytest.raises(ChatServiceError) as exc_info:
            await chat_service.route_to_model(request)
        
        assert "模型不可用" in str(exc_info.value) or "模型健康檢查失敗" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_message_history(self, chat_service, sample_session, mock_redis):
        """測試獲取訊息歷史"""
        # 準備測試資料
        mock_messages = [
            {
                "message_id": str(uuid.uuid4()),
                "content": "訊息1",
                "role": "user",
                "timestamp": datetime.now().isoformat()
            },
            {
                "message_id": str(uuid.uuid4()),
                "content": "回應1",
                "role": "assistant",
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        mock_redis.lrange.return_value = [str(msg).encode() for msg in mock_messages]
        
        result = await chat_service.get_message_history(
            session_id=sample_session.session_id,
            limit=10
        )
        
        assert len(result) <= 10
        mock_redis.lrange.assert_called()

    @pytest.mark.asyncio
    async def test_delete_message(self, chat_service, sample_message, mock_redis):
        """測試刪除訊息"""
        mock_redis.get.return_value = sample_message.model_dump_json()
        mock_redis.delete.return_value = 1
        
        result = await chat_service.delete_message(sample_message.message_id)
        
        assert result is True
        mock_redis.delete.assert_called()

    @pytest.mark.asyncio
    async def test_delete_nonexistent_message(self, chat_service, mock_redis):
        """測試刪除不存在的訊息"""
        mock_redis.get.return_value = None
        
        with pytest.raises(ChatServiceError) as exc_info:
            await chat_service.delete_message("nonexistent_id")
        
        assert "訊息不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_search_messages(self, chat_service, sample_session, mock_redis):
        """測試訊息搜尋功能"""
        # 準備測試資料
        search_results = [
            {
                "message_id": str(uuid.uuid4()),
                "content": "包含關鍵字的訊息",
                "role": "user",
                "timestamp": datetime.now().isoformat(),
                "relevance_score": 0.95
            }
        ]
        
        mock_redis.ft.return_value.search.return_value = search_results
        
        result = await chat_service.search_messages(
            session_id=sample_session.session_id,
            query="關鍵字",
            limit=5
        )
        
        assert len(result) <= 5
        assert all("關鍵字" in msg.get("content", "") for msg in result)

    @pytest.mark.asyncio
    async def test_get_chat_statistics(self, chat_service, sample_session, mock_redis):
        """測試獲取聊天統計資料"""
        # 模擬統計資料
        mock_stats = {
            "total_messages": 150,
            "user_messages": 75,
            "assistant_messages": 75,
            "average_response_time": 1500,
            "success_rate": 0.98,
            "active_sessions": 5
        }
        
        mock_redis.hgetall.return_value = {
            k.encode(): str(v).encode() for k, v in mock_stats.items()
        }
        
        result = await chat_service.get_chat_statistics(sample_session.session_id)
        
        assert result["total_messages"] == 150
        assert result["success_rate"] == 0.98
        mock_redis.hgetall.assert_called()

    @pytest.mark.asyncio
    async def test_export_chat_history(self, chat_service, sample_session):
        """測試匯出聊天記錄"""
        with patch.object(chat_service, 'get_message_history') as mock_get_history:
            mock_get_history.return_value = [
                {
                    "message_id": str(uuid.uuid4()),
                    "content": "測試訊息",
                    "role": "user",
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            result = await chat_service.export_chat_history(
                session_id=sample_session.session_id,
                format="json"
            )
            
            assert result is not None
            assert "測試訊息" in result
            mock_get_history.assert_called()

    @pytest.mark.asyncio
    async def test_cleanup_expired_sessions(self, chat_service, mock_redis):
        """測試清理過期會話"""
        # 模擬過期會話
        expired_sessions = ["session1", "session2", "session3"]
        mock_redis.keys.return_value = [s.encode() for s in expired_sessions]
        mock_redis.ttl.return_value = -1  # 表示已過期
        mock_redis.delete.return_value = len(expired_sessions)
        
        result = await chat_service.cleanup_expired_sessions()
        
        assert result == len(expired_sessions)
        assert mock_redis.delete.call_count == len(expired_sessions)

    @pytest.mark.asyncio
    async def test_handle_streaming_response(self, chat_service, mock_openai_client):
        """測試處理串流回應"""
        # 模擬串流回應
        async def mock_stream():
            yield {"content": "第一"}
            yield {"content": "部分"}
            yield {"content": "回應"}
        
        mock_openai_client.stream_completion = AsyncMock(return_value=mock_stream())
        
        responses = []
        async for chunk in chat_service.stream_chat_response("測試訊息", ModelType.CHATGPT):
            responses.append(chunk["content"])
        
        assert len(responses) == 3
        assert "".join(responses) == "第一部分回應"

    @pytest.mark.asyncio
    async def test_error_handling_service_unavailable(self, chat_service, mock_openai_client):
        """測試服務不可用時的錯誤處理"""
        mock_openai_client.chat_completion.side_effect = Exception("服務暫時不可用")
        
        request = ModelRequest(
            model_type=ModelType.CHATGPT,
            messages=[{"role": "user", "content": "測試訊息"}]
        )
        
        with pytest.raises(ChatServiceError) as exc_info:
            await chat_service.route_to_model(request)
        
        assert "服務暫時不可用" in str(exc_info.value) or "模型處理失敗" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_concurrent_message_processing(self, chat_service, sample_session):
        """測試併發訊息處理"""
        requests = [
            MessageCreateRequest(
                session_id=sample_session.session_id,
                content=f"併發訊息 {i}",
                role=MessageRole.USER
            )
            for i in range(5)
        ]
        
        # 使用 asyncio.gather 模擬併發請求
        with patch.object(chat_service, 'create_message') as mock_create:
            mock_create.return_value = Mock()
            
            tasks = [chat_service.create_message(req) for req in requests]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            assert len(results) == 5
            assert mock_create.call_count == 5

    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_performance_large_message_history(self, chat_service, sample_session):
        """測試大量訊息歷史的性能"""
        # 這是一個執行較慢的測試，測試大量資料的處理性能
        large_limit = 10000
        
        with patch.object(chat_service, '_fetch_messages_from_redis') as mock_fetch:
            mock_fetch.return_value = [{"id": i} for i in range(large_limit)]
            
            start_time = datetime.now()
            result = await chat_service.get_message_history(
                session_id=sample_session.session_id,
                limit=large_limit
            )
            end_time = datetime.now()
            
            # 驗證性能要求（應在合理時間內完成）
            processing_time = (end_time - start_time).total_seconds()
            assert processing_time < 5.0  # 5秒內完成
            assert len(result) <= large_limit


class TestChatServiceError:
    """聊天服務錯誤測試類"""

    def test_chat_service_error_creation(self):
        """測試錯誤物件創建"""
        error = ChatServiceError("測試錯誤", "TEST_ERROR")
        
        assert str(error) == "測試錯誤"
        assert error.error_code == "TEST_ERROR"

    def test_chat_service_error_without_code(self):
        """測試無錯誤代碼的錯誤物件創建"""
        error = ChatServiceError("測試錯誤")
        
        assert str(error) == "測試錯誤"
        assert error.error_code is None