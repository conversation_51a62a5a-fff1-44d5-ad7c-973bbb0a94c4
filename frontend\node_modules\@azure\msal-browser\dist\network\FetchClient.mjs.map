{"version": 3, "file": "FetchClient.mjs", "sources": ["../../src/network/FetchClient.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.getRequestFailed", "BrowserAuthErrorCodes.noNetworkConnectivity", "BrowserAuthErrorCodes.failedToParseResponse", "BrowserAuthErrorCodes.postRequestFailed", "BrowserAuthErrorCodes.failedToBuildHeaders", "BrowserAuthErrorCodes.failedToParseHeaders"], "mappings": ";;;;;;;AAAA;;;AAGG;AAcH;;AAEG;MACU,WAAW,CAAA;AACpB;;;;;AAKG;AACH,IAAA,MAAM,mBAAmB,CACrB,GAAW,EACX,OAA+B,EAAA;AAE/B,QAAA,IAAI,QAAkB,CAAC;QACvB,IAAI,eAAe,GAA2B,EAAE,CAAC;QACjD,IAAI,cAAc,GAAG,CAAC,CAAC;AACvB,QAAA,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI;AACA,YAAA,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBACxB,MAAM,EAAE,iBAAiB,CAAC,GAAG;AAC7B,gBAAA,OAAO,EAAE,UAAU;AACtB,aAAA,CAAC,CAAC;AACN,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,sBAAsB,CACxB,MAAM,CAAC,SAAS,CAAC,MAAM;kBACjBA,gBAAsC;AACxC,kBAAEC,qBAA2C,CACpD,CAAC;AACL,SAAA;AAED,QAAA,eAAe,GAAG,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI;AACA,YAAA,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;YACjC,OAAO;AACH,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAM;AAClC,gBAAA,MAAM,EAAE,cAAc;aACzB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,kBAAkB,CACpB,sBAAsB,CAClBC,qBAA2C,CAC9C,EACD,cAAc,EACd,eAAe,CAClB,CAAC;AACL,SAAA;KACJ;AAED;;;;;AAKG;AACH,IAAA,MAAM,oBAAoB,CACtB,GAAW,EACX,OAA+B,EAAA;QAE/B,MAAM,OAAO,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,CAAC;AAChD,QAAA,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;AAE5C,QAAA,IAAI,QAAkB,CAAC;QACvB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,eAAe,GAA2B,EAAE,CAAC;QACjD,IAAI;AACA,YAAA,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBACxB,MAAM,EAAE,iBAAiB,CAAC,IAAI;AAC9B,gBAAA,OAAO,EAAE,UAAU;AACnB,gBAAA,IAAI,EAAE,OAAO;AAChB,aAAA,CAAC,CAAC;AACN,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,sBAAsB,CACxB,MAAM,CAAC,SAAS,CAAC,MAAM;kBACjBC,iBAAuC;AACzC,kBAAEF,qBAA2C,CACpD,CAAC;AACL,SAAA;AAED,QAAA,eAAe,GAAG,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI;AACA,YAAA,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;YACjC,OAAO;AACH,gBAAA,OAAO,EAAE,eAAe;AACxB,gBAAA,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAM;AAClC,gBAAA,MAAM,EAAE,cAAc;aACzB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,kBAAkB,CACpB,sBAAsB,CAClBC,qBAA2C,CAC9C,EACD,cAAc,EACd,eAAe,CAClB,CAAC;AACL,SAAA;KACJ;AACJ,CAAA;AAED;;;AAGG;AACH,SAAS,eAAe,CAAC,OAA+B,EAAA;IACpD,IAAI;AACA,QAAA,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;AAC/B,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AACD,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC;AACvC,QAAA,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;AACpD,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC/B,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,OAAO,CAAC;AAClB,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,sBAAsB,CACxBE,oBAA0C,CAC7C,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;AAIG;AACH,SAAS,aAAa,CAAC,OAAgB,EAAA;IACnC,IAAI;QACA,MAAM,UAAU,GAA2B,EAAE,CAAC;QAC9C,OAAO,CAAC,OAAO,CAAC,CAAC,KAAa,EAAE,GAAW,KAAI;AAC3C,YAAA,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC5B,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,UAAU,CAAC;AACrB,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,sBAAsB,CACxBC,oBAA0C,CAC7C,CAAC;AACL,KAAA;AACL;;;;"}