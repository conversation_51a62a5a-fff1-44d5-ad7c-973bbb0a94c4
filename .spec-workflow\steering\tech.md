# Technical Steering

## Architecture Overview

### System Architecture
```mermaid
graph TB
    subgraph "Frontend (Vue.js 3)"
        A1[AAD Login]
        A2[Chat Interface]
        A3[Model Selector]
    end
    
    subgraph "Backend (Python 3.12 FastAPI)"
        B1[Auth Middleware]
        B2[Chat Controller]
        B3[Model Router]
        B4[BPM Filter]
    end
    
    subgraph "Data Layer"
        C1[Redis Cache]
        C2[Session Store]
    end
    
    subgraph "External Services"
        D1[Azure AD]
        D2[AWS Lambda MCP]
        D3[OpenAI API]
        D4[Claude API]
        D5[Gemini API]
        D6[LightRAG Server]
    end
    
    A1 --> D1
    A2 --> B2
    A3 --> B3
    B1 --> D1
    B2 --> B4
    B3 --> D3
    B3 --> D4
    B3 --> D5
    B2 --> D2
    D2 --> D6
    B2 --> C1
    B1 --> C2
```

### Technology Stack

#### Frontend Stack
- **Framework**: Vue.js 3.4+ with Composition API
- **Language**: JavaScript (ES2022+) with JSDoc type annotations
- **Build Tool**: Vite 5.0+
- **State Management**: Pinia 2.0+
- **Router**: Vue Router 4.0+
- **UI Components**: 自訂元件 + Headless UI
- **Styling**: CSS3 + CSS Variables
- **Authentication**: @azure/msal-browser 3.0+

#### Backend Stack
- **Runtime**: Python 3.12+
- **Framework**: FastAPI 0.100+
- **ASGI Server**: Uvicorn
- **Validation**: Pydantic v2
- **HTTP Client**: httpx
- **Cache/Database**: Redis 7.0+
- **Authentication**: python-jose, cryptography
- **Testing**: pytest, pytest-asyncio

#### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Cloud Services**: AWS Lambda (MCP Server)
- **Cache/Session**: Redis (可使用 AWS ElastiCache)
- **Authentication**: Azure Active Directory
- **Monitoring**: 內建 FastAPI metrics + 自訂監控

### Integration Patterns

#### Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant AAD as Azure AD
    participant B as Backend
    participant R as Redis
    
    U->>F: Access App
    F->>AAD: Redirect to Login
    AAD->>U: Login Form
    U->>AAD: Credentials
    AAD->>F: Access Token
    F->>B: API Request + Token
    B->>AAD: Validate Token
    AAD->>B: User Info
    B->>R: Store Session
    B->>F: Response + Session
```

#### Chat Flow with Model Selection
```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend
    participant BF as BMP Filter
    participant MR as Model Router
    participant AI as AI Provider
    participant MCP as AWS Lambda MCP
    participant LR as LightRAG
    
    F->>B: Chat Message + Model Choice
    B->>BF: Check BMP Relevance
    BF->>B: Is BMP Related
    alt BMP Related
        B->>MR: Route to Selected Model
        MR->>AI: Query AI Model
        B->>MCP: Query LightRAG
        MCP->>LR: Process Query
        LR->>MCP: Knowledge Response
        MCP->>B: Formatted Response
        AI->>MR: AI Response
        MR->>B: Combined Response
        B->>F: Final Answer
    else Not BMP Related
        B->>F: Rejection Message
    end
```

### Data Flow Design

#### Session Management
- **Session Creation**: AAD 驗證成功後建立 Redis session
- **Session Validation**: 每個 API 請求驗證 session 有效性
- **Session Renewal**: 接近過期時自動更新
- **Session Cleanup**: 定期清理過期 session

#### Message Flow
- **Input Validation**: 前後端雙重驗證
- **BMP Filtering**: 關鍵字 + 語意分析
- **Model Routing**: 根據使用者選擇和可用性路由
- **Response Caching**: 常見問題快取機制

## Development Standards

### Coding Conventions

#### Python (Backend)
```python
# 檔案結構
app/
├── __init__.py
├── main.py                 # FastAPI 應用程式入口
├── config/
│   ├── __init__.py
│   ├── settings.py         # 設定管理
│   └── bmp_keywords.py     # BMP 關鍵字配置
├── controllers/
│   ├── __init__.py
│   ├── auth_controller.py  # 認證相關 API
│   ├── chat_controller.py  # 聊天功能 API
│   └── model_controller.py # 模型管理 API
├── services/
│   ├── __init__.py
│   ├── auth_service.py     # 認證業務邏輯
│   ├── chat_service.py     # 聊天核心邏輯
│   ├── bmp_filter.py       # BMP 過濾服務
│   ├── model_router.py     # 模型路由服務
│   └── redis_client.py     # Redis 操作
├── clients/
│   ├── __init__.py
│   ├── openai_client.py    # OpenAI 客戶端
│   ├── claude_client.py    # Claude 客戶端
│   ├── gemini_client.py    # Gemini 客戶端
│   └── mcp_client.py       # AWS Lambda MCP 客戶端
├── models/
│   ├── __init__.py
│   ├── auth.py             # 認證資料模型
│   ├── message.py          # 訊息資料模型
│   ├── session.py          # 會話資料模型
│   └── model.py            # AI 模型資料模型
├── middleware/
│   ├── __init__.py
│   ├── auth.py             # 認證中介軟體
│   ├── cors.py             # CORS 設定
│   └── logging.py          # 日誌中介軟體
└── utils/
    ├── __init__.py
    ├── logger.py           # 日誌工具
    ├── exceptions.py       # 自訂例外
    └── helpers.py          # 輔助函數

# 命名規範
class ChatService:          # PascalCase for classes
    async def process_message(self, message: str) -> ChatResponse:  # snake_case for functions
        """Process chat message with BMP filtering."""
        pass

# 類型提示 (必要)
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

# 錯誤處理模式
try:
    result = await service.process()
except ServiceException as e:
    logger.error(f"Service error: {e}")
    raise HTTPException(status_code=400, detail=str(e))
```

#### JavaScript (Frontend)
```javascript
// 檔案結構
src/
├── main.js                 // 應用程式入口
├── App.vue                 // 根元件
├── components/
│   ├── auth/
│   │   └── LoginComponent.vue
│   ├── chat/
│   │   ├── ChatContainer.vue
│   │   ├── MessageList.vue
│   │   ├── MessageInput.vue
│   │   └── Message.vue
│   ├── ui/
│   │   └── ModelSelector.vue
│   └── layout/
│       └── Header.vue
├── views/
│   ├── LoginView.vue
│   └── ChatView.vue
├── stores/
│   ├── authStore.js        // 認證狀態管理
│   └── chatStore.js        // 聊天狀態管理
├── services/
│   ├── authService.js      // 認證服務
│   ├── apiClient.js        // HTTP 客戶端
│   ├── chatApi.js          // 聊天 API
│   └── modelApi.js         // 模型 API
├── config/
│   └── settings.js         // 前端配置管理
├── utils/
│   ├── constants.js        // 常數定義
│   ├── helpers.js          // 輔助函數
│   └── errorHandler.js     // 錯誤處理
├── assets/
│   └── styles/
│       ├── variables.css   // CSS 變數
│       ├── theme.css       // 主題樣式
│       └── global.css      // 全域樣式
└── router/
    └── index.js            // 路由設定

// 命名規範和 JSDoc 類型註解
/**
 * 聊天訊息資料結構
 * @typedef {Object} ChatMessage
 * @property {string} id - 訊息唯一識別碼
 * @property {string} content - 訊息內容
 * @property {string} type - 訊息類型 ('user'|'assistant'|'system')
 * @property {string} timestamp - 時間戳記
 */

const chatStore = useChatStore()  // camelCase for variables

/**
 * 發送聊天訊息
 * @param {string} message - 訊息內容
 * @returns {Promise<ChatResponse>} API 回應
 */
const sendMessage = async (message) => { /* */ }

// 元件命名
export default defineComponent({
  name: 'ChatContainer',    // PascalCase
  setup() {
    const isLoading = ref(false)  // camelCase for reactive data
    return { isLoading }
  }
})
```

### Testing Requirements

#### Backend Testing
```python
# 測試結構
tests/
├── __init__.py
├── conftest.py             # pytest 配置
├── unit/
│   ├── test_auth_service.py
│   ├── test_chat_service.py
│   └── test_bmp_filter.py
├── integration/
│   ├── test_api_endpoints.py
│   └── test_external_services.py
└── e2e/
    └── test_chat_flow.py

# 測試覆蓋率要求
# - Unit Tests: > 90%
# - Integration Tests: > 80%
# - E2E Tests: 主要使用者流程 100%

# 測試範例
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_chat_endpoint():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post("/api/chat/message", 
                               json={"message": "BPM 簽核流程"})
    assert response.status_code == 200
    assert response.json()["is_bmp_related"] == True
```

#### Frontend Testing  
```javascript
// 測試工具: Vitest + Vue Test Utils
// 檔案: tests/

// 元件測試範例
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import ChatContainer from '@/components/chat/ChatContainer.vue'

describe('ChatContainer', () => {
  it('sends message when Enter is pressed', async () => {
    const wrapper = mount(ChatContainer)
    const input = wrapper.find('input')
    
    await input.setValue('Test message')
    await input.trigger('keydown.enter')
    
    expect(wrapper.emitted('message-sent')).toBeTruthy()
  })
})

// API 服務測試
import { describe, it, expect, vi } from 'vitest'
import { chatApi } from '@/services/chatApi'

describe('chatApi', () => {
  it('sends message with auth token', async () => {
    const mockResponse = { response: 'Test response' }
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })
    
    const result = await chatApi.sendMessage('Test message')
    expect(result).toEqual(mockResponse)
  })
})
```

### Security Guidelines

#### Authentication Security
- **Token Validation**: 每個 API 請求驗證 AAD token
- **Session Security**: Redis session 加密存儲
- **CORS Policy**: 嚴格限制允許的來源
- **Rate Limiting**: API 速率限制防止濫用

#### Data Protection
- **Input Sanitization**: 所有使用者輸入清理和驗證
- **XSS Prevention**: 前端內容渲染安全處理
- **SQL Injection**: 使用參數化查詢 (雖然主要用 Redis)
- **Secrets Management**: 敏感資訊環境變數管理

#### API Security
```python
# 認證中介軟體範例
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    try:
        # 驗證 AAD token
        payload = verify_aad_token(token.credentials)
        employee = await get_employee_info(payload['oid'])
        if not employee.is_active:
            raise HTTPException(status_code=403, detail="Employee not active")
        return employee
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")

# 在 API 端點使用
@app.post("/api/chat/message")
async def send_message(
    request: ChatRequest,
    current_user: Employee = Depends(verify_token)
):
    # 處理聊天邏輯
    pass
```

### Performance Standards

#### Backend Performance
- **API Response Time**: < 100ms (不含 AI 模型回應)
- **AI Model Response**: < 3 seconds
- **Database Query**: < 50ms
- **Memory Usage**: < 512MB per instance
- **CPU Usage**: < 70% under normal load

#### Frontend Performance  
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **First Input Delay**: < 100ms
- **Cumulative Layout Shift**: < 0.1

#### Caching Strategy
```python
# Redis 快取策略
# 1. Session Cache: 30 minutes TTL
# 2. Chat History: 7 days TTL  
# 3. BMP Responses: 1 hour TTL (常見問題)
# 4. Model Availability: 5 minutes TTL

import aioredis
from functools import wraps

def cache_response(ttl: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached = await redis.get(cache_key)
            if cached:
                return json.loads(cached)
            
            result = await func(*args, **kwargs)
            await redis.setex(cache_key, ttl, json.dumps(result))
            return result
        return wrapper
    return decorator

@cache_response(ttl=3600)  # 1 hour cache
async def get_bmp_response(question: str) -> str:
    return await mcp_client.query(question)
```

## Patterns & Best Practices

### Error Handling Patterns

#### Hierarchical Error Handling
```python
# 自訂例外階層
class BPMChatException(Exception):
    """Base exception for BPM Chat application"""
    pass

class AuthenticationError(BPMChatException):
    """Authentication related errors"""
    pass

class ValidationError(BPMChatException):
    """Data validation errors"""
    pass

class ExternalServiceError(BPMChatException):
    """External service integration errors"""
    pass

class ModelServiceError(ExternalServiceError):
    """AI model service specific errors"""
    pass

# 全域例外處理器
@app.exception_handler(BPMChatException)
async def bpm_chat_exception_handler(request: Request, exc: BPMChatException):
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.__class__.__name__,
            "message": str(exc),
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### Logging Standards
```python
# 結構化日誌
import structlog

logger = structlog.get_logger()

# 在業務邏輯中使用
async def process_chat_message(message: str, user_id: str):
    logger.info(
        "Processing chat message",
        user_id=user_id,
        message_length=len(message),
        timestamp=datetime.utcnow()
    )
    
    try:
        result = await chat_service.process(message)
        logger.info(
            "Chat message processed successfully",
            user_id=user_id,
            response_length=len(result.response),
            model_used=result.model_used
        )
        return result
    except Exception as e:
        logger.error(
            "Failed to process chat message",
            user_id=user_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise
```

### Configuration Management
```python
# settings.py
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # App Settings
    app_name: str = "BPM Chatbot"
    debug: bool = False
    
    # Database
    redis_url: str
    redis_ttl_session: int = 1800  # 30 minutes
    redis_ttl_chat: int = 604800   # 7 days
    
    # Authentication
    aad_tenant_id: str
    aad_client_id: str
    aad_client_secret: str
    
    # AI Services
    openai_api_key: str
    claude_api_key: str
    gemini_api_key: str
    
    # AWS Lambda MCP
    aws_lambda_function_name: str
    aws_region: str = "us-west-2"
    
    # Performance
    max_concurrent_requests: int = 50
    request_timeout: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = False

settings = Settings()
```

### Documentation Standards
- **API Documentation**: 自動生成 OpenAPI/Swagger 文件
- **Code Comments**: 複雜邏輯必須註解
- **Type Annotations**: Python 使用類型提示，JavaScript 使用 JSDoc 註解
- **README**: 詳細的安裝和使用說明
- **Architecture Docs**: 系統架構圖和設計說明