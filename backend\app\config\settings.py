"""
應用程式配置設定
負責載入和管理所有環境變數和應用程式設定
"""

import os
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """應用程式設定類別"""
    
    # 應用程式基本設定
    app_name: str = "BPM_Chatbot"
    app_version: str = "1.0.0"
    app_environment: str = "development"
    debug: bool = True
    log_level: str = "INFO"
    
    # 伺服器設定
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = True
    
    # 資料庫設定
    redis_url: str = "redis://localhost:6379/0"
    redis_password: Optional[str] = None
    redis_db: int = 0
    redis_max_connections: int = 20
    redis_timeout: int = 5
    
    # JWT 設定
    jwt_secret_key: str = "your-super-secret-jwt-key-here-change-this-in-production"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    
    # Azure AD 設定
    azure_ad_tenant_id: Optional[str] = None
    azure_ad_client_id: Optional[str] = None
    azure_ad_client_secret: Optional[str] = None
    azure_ad_redirect_uri: str = "http://localhost:3000/auth/callback"
    
    # OpenAI 設定
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-3.5-turbo"
    openai_max_tokens: int = 4000
    openai_temperature: float = 0.7
    openai_timeout: int = 30
    
    # Anthropic Claude 設定
    anthropic_api_key: Optional[str] = None
    anthropic_model: str = "claude-3-sonnet-20240229"
    anthropic_max_tokens: int = 4000
    anthropic_temperature: float = 0.7
    anthropic_timeout: int = 30
    
    # Google Gemini 設定
    google_api_key: Optional[str] = None
    google_model: str = "gemini-pro"
    google_max_tokens: int = 2048
    google_temperature: float = 0.7
    google_timeout: int = 30
    
    # 安全性設定
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    cors_credentials: bool = True
    cors_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    cors_headers: List[str] = ["*"]
    
    # 速率限制設定
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    rate_limit_enabled: bool = True
    
    # 監控設定
    health_check_enabled: bool = True
    metrics_enabled: bool = True
    prometheus_enabled: bool = False
    
    # 檔案上傳設定
    max_file_size: int = 10485760  # 10MB
    allowed_file_types: List[str] = ["txt", "pdf", "doc", "docx", "csv"]
    upload_folder: str = "uploads"
    
    # 快取設定
    cache_ttl: int = 300
    cache_max_size: int = 1000
    
    # BPM 過濾器設定
    bmp_filter_enabled: bool = True
    bmp_filter_threshold: float = 0.6
    bmp_filter_strict_mode: bool = False
    
    # 訊息設定
    max_message_length: int = 5000
    max_conversation_length: int = 100
    auto_cleanup_enabled: bool = True
    cleanup_after_days: int = 30
    
    # MCP 設定
    mcp_enabled: bool = False
    mcp_endpoint: Optional[str] = None
    mcp_api_key: Optional[str] = None
    
    # 開發者設定
    testing: bool = False
    mock_external_apis: bool = False
    api_docs_enabled: bool = True
    admin_email: str = "<EMAIL>"
    
    # 生產環境設定
    secure_ssl_redirect: bool = False
    secure_proxy_ssl_header: Optional[str] = None
    secure_browser_xss_filter: bool = False
    secure_content_type_nosniff: bool = False


    @property
    def is_development(self) -> bool:
        """檢查是否為開發環境"""
        return self.app_environment == "development"

    @property
    def is_production(self) -> bool:
        """檢查是否為生產環境"""
        return self.app_environment == "production"

    @property
    def is_testing(self) -> bool:
        """檢查是否為測試環境"""
        return self.app_environment == "testing" or self.testing

    @property
    def database_url(self) -> str:
        """獲取資料庫連接 URL"""
        return self.redis_url

    @property
    def api_docs_url(self) -> Optional[str]:
        """獲取 API 文件 URL"""
        if self.api_docs_enabled:
            return "/docs"
        return None

    def get_ai_model_config(self, provider: str) -> dict:
        """獲取 AI 模型配置"""
        configs = {
            "openai": {
                "api_key": self.openai_api_key,
                "model": self.openai_model,
                "max_tokens": self.openai_max_tokens,
                "temperature": self.openai_temperature,
                "timeout": self.openai_timeout,
            },
            "anthropic": {
                "api_key": self.anthropic_api_key,
                "model": self.anthropic_model,
                "max_tokens": self.anthropic_max_tokens,
                "temperature": self.anthropic_temperature,
                "timeout": self.anthropic_timeout,
            },
            "google": {
                "api_key": self.google_api_key,
                "model": self.google_model,
                "max_tokens": self.google_max_tokens,
                "temperature": self.google_temperature,
                "timeout": self.google_timeout,
            }
        }
        return configs.get(provider, {})

    def get_cors_config(self) -> dict:
        """獲取 CORS 配置"""
        return {
            "allow_origins": self.cors_origins,
            "allow_credentials": self.cors_credentials,
            "allow_methods": self.cors_methods,
            "allow_headers": self.cors_headers,
        }

    def get_rate_limit_config(self) -> dict:
        """獲取速率限制配置"""
        return {
            "enabled": self.rate_limit_enabled,
            "requests": self.rate_limit_requests,
            "window": self.rate_limit_window,
        }

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }


@lru_cache()
def get_settings() -> Settings:
    """獲取應用程式設定（快取版本）"""
    return Settings()