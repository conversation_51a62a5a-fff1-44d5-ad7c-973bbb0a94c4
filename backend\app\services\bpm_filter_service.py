"""
BPM 內容過濾服務模組
檢查用戶輸入是否與 Business Process Management 相關
"""

import os
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json
from enum import Enum

# 設定日誌
logger = logging.getLogger(__name__)

class FilterResult(str, Enum):
    """過濾結果類型"""
    ALLOWED = "allowed"
    REJECTED = "rejected"
    WARNING = "warning"
    ESCALATION = "escalation"

class BpmFilterService:
    """BPM 內容過濾服務類別"""
    
    def __init__(self):
        # 載入 BPM 相關關鍵字
        self.bpm_keywords = self._load_bpm_keywords()
        self.bpm_topics = self._load_bpm_topics()
        self.rejected_keywords = self._load_rejected_keywords()
        self.warning_keywords = self._load_warning_keywords()
        
        # 過濾設定
        self.min_keyword_match_ratio = float(os.getenv("BMP_MIN_MATCH_RATIO", "0.1"))
        self.strict_mode = os.getenv("BMP_STRICT_MODE", "false").lower() == "true"
        self.enable_topic_detection = os.getenv("BMP_ENABLE_TOPIC_DETECTION", "true").lower() == "true"
        
        # 統計計數器
        self.filter_stats = {
            "total_requests": 0,
            "allowed": 0,
            "rejected": 0,
            "warnings": 0,
            "escalations": 0
        }
    
    def _load_bpm_keywords(self) -> List[str]:
        """載入 BPM 相關關鍵字"""
        # 預設 BPM 關鍵字
        default_keywords = [
            # 核心 BPM 術語
            "business process", "business processes", "流程管理", "業務流程",
            "workflow", "工作流程", "作業流程", "bpm", "business process management",
            
            # 流程相關
            "process", "processes", "流程", "程序", "procedure", "procedures",
            "approval", "approvals", "核准", "核准流程", "簽核", "審核",
            "review", "reviews", "審查", "檢視", "evaluate", "evaluation",
            
            # 組織結構
            "organization", "organizational", "組織", "組織架構", "hierarchy", "階層",
            "department", "departments", "部門", "division", "divisions", "單位",
            "team", "teams", "團隊", "group", "groups", "群組",
            
            # 管理術語
            "management", "manage", "管理", "governance", "治理", "control", "控制",
            "monitor", "monitoring", "監控", "track", "tracking", "追蹤",
            "report", "reporting", "報告", "dashboard", "儀表板",
            
            # 效率和改善
            "efficiency", "effective", "效率", "效能", "optimization", "優化",
            "improvement", "改善", "streamline", "精簡", "automate", "automation", "自動化",
            
            # 政策和規範
            "policy", "policies", "政策", "rule", "rules", "規則", "regulation", "regulations", "法規",
            "compliance", "合規", "standard", "standards", "標準", "guideline", "guidelines", "指導原則",
            
            # 專案和任務
            "project", "projects", "專案", "task", "tasks", "任務", "activity", "activities", "活動",
            "milestone", "milestones", "里程碑", "deliverable", "deliverables", "交付項目",
            
            # 決策和分析
            "decision", "decisions", "決策", "analysis", "analyses", "分析", "metrics", "指標",
            "kpi", "key performance indicator", "關鍵績效指標", "performance", "績效",
            
            # 協作和溝通
            "collaboration", "collaborate", "協作", "communication", "溝通", "coordination", "協調",
            "stakeholder", "stakeholders", "利害關係人", "meeting", "meetings", "會議",
            
            # 文件和記錄
            "document", "documents", "文件", "documentation", "文檔", "record", "records", "記錄",
            "form", "forms", "表單", "template", "templates", "範本",
            
            # 系統和工具
            "system", "systems", "系統", "tool", "tools", "工具", "software", "軟體",
            "application", "applications", "應用程式", "platform", "platforms", "平台"
        ]
        
        # 從環境變數載入額外關鍵字
        custom_keywords_str = os.getenv("BMP_CUSTOM_KEYWORDS", "")
        if custom_keywords_str:
            custom_keywords = [kw.strip() for kw in custom_keywords_str.split(",")]
            default_keywords.extend(custom_keywords)
        
        return list(set(default_keywords))  # 去重
    
    def _load_bpm_topics(self) -> List[str]:
        """載入 BPM 主題分類"""
        return [
            "流程設計", "流程優化", "流程監控", "流程分析",
            "組織管理", "人員管理", "角色權限", "部門協調",
            "專案管理", "任務分配", "進度追蹤", "資源配置",
            "政策制定", "規範遵循", "風險管控", "品質管理",
            "系統整合", "自動化", "數位轉型", "技術支援",
            "績效評估", "指標分析", "報告產製", "決策支援"
        ]
    
    def _load_rejected_keywords(self) -> List[str]:
        """載入明確拒絕的關鍵字"""
        default_rejected = [
            # 技術支援 (非 BPM)
            "password", "密碼", "login", "登入", "reset", "重設",
            "technical support", "技術支援", "system error", "系統錯誤",
            "bug", "error", "錯誤", "problem", "問題",
            
            # 個人事務
            "personal", "個人", "private", "私人", "family", "家庭",
            "vacation", "假期", "holiday", "休假", "sick leave", "病假",
            
            # 非業務內容
            "weather", "天氣", "news", "新聞", "sports", "運動",
            "entertainment", "娛樂", "movie", "電影", "music", "音樂",
            "game", "遊戲", "social media", "社群媒體",
            
            # 財務個人諮詢
            "investment", "投資", "stock", "股票", "trading", "交易",
            "cryptocurrency", "加密貨幣", "bitcoin", "比特幣",
            
            # 醫療諮詢
            "medical", "醫療", "health", "健康", "doctor", "醫生",
            "medicine", "藥物", "treatment", "治療",
            
            # 法律諮詢
            "legal advice", "法律建議", "lawsuit", "訴訟", "court", "法院",
            "lawyer", "律師", "contract", "合約"
        ]
        
        # 從環境變數載入額外拒絕關鍵字
        custom_rejected_str = os.getenv("BMP_REJECTED_KEYWORDS", "")
        if custom_rejected_str:
            custom_rejected = [kw.strip() for kw in custom_rejected_str.split(",")]
            default_rejected.extend(custom_rejected)
        
        return list(set(default_rejected))
    
    def _load_warning_keywords(self) -> List[str]:
        """載入需要警告的關鍵字"""
        return [
            # 敏感資訊
            "confidential", "機密", "secret", "秘密", "classified", "機密資料",
            "password", "密碼", "credential", "憑證", "token", "令牌",
            
            # 財務敏感
            "salary", "薪資", "bonus", "獎金", "budget", "預算",
            "cost", "成本", "revenue", "營收", "profit", "利潤",
            
            # 人事敏感
            "hire", "雇用", "fire", "解僱", "layoff", "裁員",
            "promotion", "升遷", "demotion", "降職", "resignation", "辭職"
        ]
    
    def _calculate_keyword_match_ratio(self, text: str, keywords: List[str]) -> float:
        """計算關鍵字匹配比例"""
        text_lower = text.lower()
        total_words = len(text.split())
        
        if total_words == 0:
            return 0.0
        
        matched_keywords = 0
        for keyword in keywords:
            if keyword.lower() in text_lower:
                matched_keywords += 1
        
        return matched_keywords / len(keywords) if keywords else 0.0
    
    def _contains_keywords(self, text: str, keywords: List[str]) -> Tuple[bool, List[str]]:
        """檢查文本是否包含關鍵字"""
        text_lower = text.lower()
        found_keywords = []
        
        for keyword in keywords:
            if keyword.lower() in text_lower:
                found_keywords.append(keyword)
        
        return len(found_keywords) > 0, found_keywords
    
    def _detect_topic(self, text: str) -> Optional[str]:
        """檢測文本主題"""
        if not self.enable_topic_detection:
            return None
        
        text_lower = text.lower()
        topic_scores = {}
        
        # 簡單的主題檢測邏輯
        topic_keywords = {
            "流程管理": ["process", "workflow", "流程", "作業流程"],
            "組織管理": ["organization", "department", "team", "組織", "部門"],
            "專案管理": ["project", "task", "milestone", "專案", "任務"],
            "政策規範": ["policy", "rule", "compliance", "政策", "規則"],
            "系統整合": ["system", "integration", "automation", "系統", "整合"],
            "績效分析": ["performance", "kpi", "metrics", "績效", "指標"]
        }
        
        for topic, keywords in topic_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1
            topic_scores[topic] = score
        
        # 回傳得分最高的主題
        if topic_scores:
            best_topic = max(topic_scores.items(), key=lambda x: x[1])
            if best_topic[1] > 0:
                return best_topic[0]
        
        return None
    
    def filter_content(self, user_input: str, user_id: str = None) -> Dict[str, Any]:
        """
        過濾用戶輸入內容
        
        Args:
            user_input: 用戶輸入的文本
            user_id: 用戶 ID（用於日誌記錄）
        
        Returns:
            過濾結果字典
        """
        self.filter_stats["total_requests"] += 1
        
        result = {
            "result": FilterResult.ALLOWED,
            "message": "內容已通過 BPM 主題檢查",
            "confidence": 0.0,
            "matched_keywords": [],
            "rejected_keywords": [],
            "warning_keywords": [],
            "detected_topic": None,
            "suggestions": [],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            # 基本檢查
            if not user_input or not user_input.strip():
                result["result"] = FilterResult.REJECTED
                result["message"] = "輸入內容不能為空"
                self.filter_stats["rejected"] += 1
                return result
            
            # 檢查是否包含明確拒絕的關鍵字
            has_rejected, rejected_kws = self._contains_keywords(user_input, self.rejected_keywords)
            if has_rejected:
                result["result"] = FilterResult.REJECTED
                result["message"] = f"輸入內容包含非 BPM 相關主題，本系統僅處理 Business Process Management 相關問題"
                result["rejected_keywords"] = rejected_kws
                result["suggestions"] = [
                    "請提出與業務流程管理相關的問題",
                    "例如：流程優化、組織管理、專案協調等主題",
                    "如需其他協助，請聯絡相關部門"
                ]
                self.filter_stats["rejected"] += 1
                logger.info(f"內容被拒絕 - 用戶: {user_id}, 拒絕關鍵字: {rejected_kws}")
                return result
            
            # 檢查警告關鍵字
            has_warning, warning_kws = self._contains_keywords(user_input, self.warning_keywords)
            if has_warning:
                result["result"] = FilterResult.WARNING
                result["message"] = "內容包含敏感資訊，請注意資訊安全"
                result["warning_keywords"] = warning_kws
                result["suggestions"] = [
                    "請避免在聊天中提及機密或敏感資訊",
                    "如需討論敏感主題，請使用適當的安全管道"
                ]
                self.filter_stats["warnings"] += 1
                logger.warning(f"內容包含警告關鍵字 - 用戶: {user_id}, 警告關鍵字: {warning_kws}")
            
            # 檢查 BPM 關鍵字匹配
            has_bpm_kws, bpm_kws = self._contains_keywords(user_input, self.bpm_keywords)
            match_ratio = self._calculate_keyword_match_ratio(user_input, self.bpm_keywords)
            
            result["confidence"] = match_ratio
            result["matched_keywords"] = bpm_kws
            
            # 主題檢測
            detected_topic = self._detect_topic(user_input)
            result["detected_topic"] = detected_topic
            
            # 根據匹配度和模式決定結果
            if self.strict_mode:
                # 嚴格模式：必須有明確的 BPM 關鍵字
                if not has_bpm_kws:
                    if result["result"] != FilterResult.WARNING:
                        result["result"] = FilterResult.REJECTED
                        result["message"] = "在嚴格模式下，輸入內容必須明確包含 BPM 相關關鍵字"
                        result["suggestions"] = [
                            "請在問題中明確提及業務流程管理相關術語",
                            "例如：流程、管理、組織、專案、政策等"
                        ]
                        self.filter_stats["rejected"] += 1
                        return result
            else:
                # 寬鬆模式：基於匹配比例
                if match_ratio < self.min_keyword_match_ratio and not detected_topic:
                    if result["result"] != FilterResult.WARNING:
                        result["result"] = FilterResult.REJECTED
                        result["message"] = f"輸入內容與 BPM 主題相關性較低 (匹配度: {match_ratio:.2%})"
                        result["suggestions"] = [
                            "請提出更具體的業務流程管理問題",
                            "可以詢問關於流程優化、組織協調、專案管理等主題"
                        ]
                        self.filter_stats["rejected"] += 1
                        return result
            
            # 內容通過檢查
            if result["result"] == FilterResult.WARNING:
                # 保持警告狀態
                pass
            else:
                result["result"] = FilterResult.ALLOWED
                result["message"] = f"內容已通過 BPM 主題檢查 (匹配度: {match_ratio:.2%})"
                if detected_topic:
                    result["message"] += f"，檢測到主題: {detected_topic}"
                
                self.filter_stats["allowed"] += 1
            
            # 提供改善建議
            if match_ratio < 0.5:  # 匹配度較低時提供建議
                result["suggestions"] = [
                    "為了獲得更精確的回答，建議在問題中包含更多 BPM 相關術語",
                    "可以具體描述您想了解的流程管理問題"
                ]
            
            logger.debug(f"內容過濾完成 - 用戶: {user_id}, 結果: {result['result']}, 匹配度: {match_ratio:.2%}")
            
        except Exception as e:
            logger.error(f"內容過濾發生錯誤: {e}")
            result["result"] = FilterResult.ESCALATION
            result["message"] = "內容過濾服務暫時無法使用，請稍後再試"
            self.filter_stats["escalations"] += 1
        
        return result
    
    def get_filter_suggestions(self) -> List[str]:
        """獲取過濾建議"""
        return [
            "業務流程管理 (BPM) 相關主題包括：",
            "• 流程設計與優化",
            "• 組織架構與管理",
            "• 專案管理與協調",
            "• 政策制定與合規",
            "• 系統整合與自動化",
            "• 績效分析與改善",
            "",
            "範例問題：",
            "• 如何優化核准流程？",
            "• 部門協調的最佳實務是什麼？",
            "• 專案管理有哪些關鍵要素？",
            "• 如何提高組織效率？"
        ]
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取過濾統計資訊"""
        total = self.filter_stats["total_requests"]
        if total == 0:
            return self.filter_stats
        
        return {
            **self.filter_stats,
            "success_rate": (self.filter_stats["allowed"] + self.filter_stats["warnings"]) / total,
            "rejection_rate": self.filter_stats["rejected"] / total,
            "warning_rate": self.filter_stats["warnings"] / total
        }
    
    def reset_stats(self) -> None:
        """重置統計資訊"""
        self.filter_stats = {
            "total_requests": 0,
            "allowed": 0,
            "rejected": 0,
            "warnings": 0,
            "escalations": 0
        }
        logger.info("BPM 過濾器統計資訊已重置")

# 建立全域服務實例
bpm_filter_service = BmpFilterService()