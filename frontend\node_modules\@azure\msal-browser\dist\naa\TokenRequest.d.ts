export type TokenRequest = {
    platformBrokerId?: string;
    clientId: string;
    authority?: string;
    scope: string;
    correlationId: string;
    claims?: string;
    state?: string;
    reqCnf?: string;
    keyId?: string;
    authenticationScheme?: string;
    shrClaims?: string;
    shrNonce?: string;
    resourceRequestMethod?: string;
    resourceRequestUri?: string;
    extendedExpiryToken?: boolean;
    extraParameters?: Map<string, string>;
};
//# sourceMappingURL=TokenRequest.d.ts.map