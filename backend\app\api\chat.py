"""
聊天 API 路由
處理用戶聊天請求，整合 AI 模型和知識庫
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

# 本地模組
from ..middleware.auth import get_current_user, AuthenticatedUser
from ..services.ai_service import ai_service, AIModel, AIMessage, MessageRole
from ..services.mcp_service import mcp_service
from ..services.bpm_filter_service import bpm_filter_service, FilterResult
from ..services.redis_service import redis_service
from ..services.employee_service import employee_service

# 設定日誌
logger = logging.getLogger(__name__)

# 建立路由器
router = APIRouter(prefix="/chat", tags=["聊天"])

# === 請求和回應模型 ===

class ChatMessage(BaseModel):
    """聊天訊息模型"""
    role: str = Field(..., description="訊息角色: user, assistant, system")
    content: str = Field(..., description="訊息內容")
    timestamp: Optional[str] = Field(default=None, description="時間戳")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")

class ChatRequest(BaseModel):
    """聊天請求模型"""
    message: str = Field(..., min_length=1, max_length=2000, description="用戶訊息")
    model: AIModel = Field(default=AIModel.CHATGPT, description="AI 模型選擇")
    session_id: Optional[str] = Field(default=None, description="會話 ID")
    include_context: bool = Field(default=True, description="是否包含知識庫上下文")
    stream: bool = Field(default=False, description="是否使用串流回應")
    max_tokens: Optional[int] = Field(default=None, ge=1, le=4000, description="最大 token 數")
    temperature: Optional[float] = Field(default=None, ge=0.0, le=2.0, description="回應創意度")

class ChatResponse(BaseModel):
    """聊天回應模型"""
    message: str = Field(..., description="AI 回應內容")
    model: str = Field(..., description="使用的 AI 模型")
    session_id: str = Field(..., description="會話 ID")
    message_id: str = Field(..., description="訊息 ID")
    timestamp: str = Field(..., description="回應時間")
    usage: Optional[Dict[str, Any]] = Field(default={}, description="使用統計")
    context_used: bool = Field(default=False, description="是否使用了知識庫上下文")
    filter_result: Optional[Dict[str, Any]] = Field(default=None, description="內容過濾結果")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")

class ChatHistoryResponse(BaseModel):
    """聊天記錄回應模型"""
    messages: List[ChatMessage] = Field(..., description="聊天訊息列表")
    total: int = Field(..., description="總訊息數")
    session_id: str = Field(..., description="會話 ID")
    user_id: str = Field(..., description="用戶 ID")

class ModelListResponse(BaseModel):
    """模型列表回應模型"""
    models: List[Dict[str, Any]] = Field(..., description="可用模型列表")
    default_model: str = Field(..., description="預設模型")

# === API 端點 ===

@router.post("/send", response_model=ChatResponse)
async def send_message(
    request: ChatRequest,
    background_tasks: BackgroundTasks,
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    發送聊天訊息
    """
    try:
        # 驗證 AI 模型可用性
        available_models = ai_service.get_available_models()
        if request.model not in available_models:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"模型 {request.model.value} 不可用。可用模型: {[m.value for m in available_models]}"
            )
        
        # 內容過濾檢查
        filter_result = bpm_filter_service.filter_content(
            request.message, 
            current_user.object_id
        )
        
        if filter_result["result"] == FilterResult.REJECTED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=filter_result["message"]
            )
        
        # 獲取或建立會話
        session_id = request.session_id
        if not session_id:
            # 建立新會話
            session_data = {
                "client_info": {
                    "user_agent": "BPM Chatbot Web",
                    "model_preference": request.model.value
                }
            }
            session_id = await redis_service.create_session(
                current_user.object_id,
                current_user.object_id,
                session_data
            )
        else:
            # 驗證會話
            session_info = await redis_service.get_session(session_id)
            if not session_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="會話不存在或已過期"
                )
            
            if session_info["user_id"] != current_user.object_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="無權存取此會話"
                )
            
            # 更新會話活動時間
            await redis_service.update_session_activity(session_id)
        
        # 獲取聊天記錄
        chat_history = await redis_service.get_chat_history(session_id, limit=10)
        
        # 建構訊息列表
        messages = []
        for msg in reversed(chat_history):  # 時間順序
            if msg.get("role") in ["user", "assistant"]:
                messages.append(AIMessage(
                    role=MessageRole(msg["role"]),
                    content=msg["content"]
                ))
        
        # 添加當前用戶訊息
        user_message = AIMessage(
            role=MessageRole.USER,
            content=request.message
        )
        messages.append(user_message)
        
        # 知識庫上下文增強
        context_used = False
        enhanced_message = request.message
        
        if request.include_context:
            try:
                context_result = await mcp_service.add_context_to_query(request.message)
                
                if context_result["context_count"] > 0:
                    context_used = True
                    
                    # 建構包含上下文的訊息
                    context_text = "\n\n相關知識庫內容:\n"
                    for i, ctx in enumerate(context_result["contexts"], 1):
                        context_text += f"{i}. {ctx['title']}: {ctx['content']}\n"
                    
                    enhanced_message = f"{request.message}\n{context_text}"
                    
                    # 更新最後一條訊息
                    messages[-1].content = enhanced_message
                    messages[-1].metadata["context_count"] = context_result["context_count"]
                    
            except Exception as e:
                logger.warning(f"知識庫查詢失敗，繼續使用原始訊息: {e}")
        
        # 生成 AI 回應
        ai_response = await ai_service.generate_response(
            messages=messages,
            model=request.model,
            stream=False,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # 儲存用戶訊息
        user_message_data = {
            "role": "user",
            "content": request.message,
            "metadata": {
                "model": request.model.value,
                "filter_result": filter_result,
                "context_used": context_used
            }
        }
        user_message_id = await redis_service.save_chat_message(session_id, user_message_data)
        
        # 儲存 AI 回應
        assistant_message_data = {
            "role": "assistant",
            "content": ai_response.content,
            "metadata": {
                "model": ai_response.model.value,
                "usage": ai_response.usage,
                "response_time_ms": ai_response.response_time_ms,
                "context_used": context_used
            }
        }
        assistant_message_id = await redis_service.save_chat_message(session_id, assistant_message_data)
        
        # 背景任務：更新員工活動
        background_tasks.add_task(
            update_user_activity,
            current_user.object_id,
            "chat_message"
        )
        
        # 建構回應
        response = ChatResponse(
            message=ai_response.content,
            model=ai_response.model.value,
            session_id=session_id,
            message_id=assistant_message_id,
            timestamp=ai_response.timestamp.isoformat(),
            usage=ai_response.usage,
            context_used=context_used,
            filter_result=filter_result if filter_result["result"] != FilterResult.ALLOWED else None,
            metadata={
                "response_time_ms": ai_response.response_time_ms,
                "user_message_id": user_message_id
            }
        )
        
        logger.info(f"聊天回應完成 - 用戶: {current_user.object_id}, 模型: {request.model.value}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天處理失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="聊天服務暫時無法使用"
        )

@router.get("/stream/{session_id}")
async def stream_chat(
    session_id: str,
    message: str,
    model: AIModel = AIModel.CHATGPT,
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    串流聊天回應
    """
    # 驗證會話
    session_info = await redis_service.get_session(session_id)
    if not session_info or session_info["user_id"] != current_user.object_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="會話不存在或無權存取"
        )
    
    # 內容過濾
    filter_result = bpm_filter_service.filter_content(message, current_user.object_id)
    if filter_result["result"] == FilterResult.REJECTED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=filter_result["message"]
        )
    
    # 獲取聊天記錄並建構訊息
    chat_history = await redis_service.get_chat_history(session_id, limit=10)
    messages = []
    for msg in reversed(chat_history):
        if msg.get("role") in ["user", "assistant"]:
            messages.append(AIMessage(
                role=MessageRole(msg["role"]),
                content=msg["content"]
            ))
    
    messages.append(AIMessage(role=MessageRole.USER, content=message))
    
    # 回傳串流回應
    return StreamingResponse(
        ai_service.generate_streaming_response(messages, model),
        media_type="text/plain"
    )

@router.get("/history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(
    session_id: str,
    limit: int = 50,
    offset: int = 0,
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    獲取聊天記錄
    """
    # 驗證會話
    session_info = await redis_service.get_session(session_id)
    if not session_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="會話不存在"
        )
    
    if session_info["user_id"] != current_user.object_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="無權存取此會話"
        )
    
    # 獲取聊天記錄
    messages_data = await redis_service.get_chat_history(session_id, limit, offset)
    
    # 轉換格式
    messages = []
    for msg_data in messages_data:
        message = ChatMessage(
            role=msg_data.get("role", ""),
            content=msg_data.get("content", ""),
            timestamp=msg_data.get("timestamp"),
            metadata=msg_data.get("metadata", {})
        )
        messages.append(message)
    
    return ChatHistoryResponse(
        messages=messages,
        total=len(messages_data),
        session_id=session_id,
        user_id=current_user.object_id
    )

@router.delete("/history/{session_id}")
async def clear_chat_history(
    session_id: str,
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    清除聊天記錄
    """
    # 驗證會話
    session_info = await redis_service.get_session(session_id)
    if not session_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="會話不存在"
        )
    
    if session_info["user_id"] != current_user.object_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="無權操作此會話"
        )
    
    # 清除聊天記錄
    success = await redis_service.delete_chat_history(session_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清除聊天記錄失敗"
        )
    
    return {"message": "聊天記錄已清除", "session_id": session_id}

@router.get("/models", response_model=ModelListResponse)
async def get_available_models(
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    獲取可用的 AI 模型列表
    """
    available_models = ai_service.get_available_models()
    
    models_info = []
    for model in available_models:
        model_info = ai_service.get_model_info(model)
        models_info.append({
            "id": model.value,
            "name": model_info.get("name", model.value),
            "provider": model_info.get("provider", ""),
            "description": model_info.get("description", ""),
            "available": model_info.get("available", False)
        })
    
    return ModelListResponse(
        models=models_info,
        default_model=AIModel.CHATGPT.value
    )

@router.get("/stats")
async def get_chat_stats(
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    獲取聊天統計（管理員功能）
    """
    # 檢查管理員權限
    if not current_user.is_employee:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="權限不足"
        )
    
    return {
        "ai_service": ai_service.get_stats(),
        "mcp_service": mcp_service.get_stats(),
        "filter_service": bpm_filter_service.get_stats(),
        "redis_service": await redis_service.get_stats()
    }

# === 背景任務 ===

async def update_user_activity(user_id: str, activity_type: str):
    """更新用戶活動記錄"""
    try:
        # 這裡可以實作用戶活動記錄邏輯
        logger.debug(f"用戶活動記錄: {user_id} - {activity_type}")
    except Exception as e:
        logger.error(f"更新用戶活動失敗: {e}")

# === 錯誤處理 ===

@router.exception_handler(ValueError)
async def value_error_handler(request, exc):
    """處理值錯誤"""
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=str(exc)
    )

@router.exception_handler(TimeoutError)
async def timeout_error_handler(request, exc):
    """處理超時錯誤"""
    return HTTPException(
        status_code=status.HTTP_408_REQUEST_TIMEOUT,
        detail="請求超時，請稍後再試"
    )