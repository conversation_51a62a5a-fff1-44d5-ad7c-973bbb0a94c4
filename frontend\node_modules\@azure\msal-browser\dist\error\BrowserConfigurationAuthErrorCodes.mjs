/*! @azure/msal-browser v3.30.0 2025-08-05 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const storageNotSupported = "storage_not_supported";
const stubbedPublicClientApplicationCalled = "stubbed_public_client_application_called";
const inMemRedirectUnavailable = "in_mem_redirect_unavailable";

export { inMemRedirectUnavailable, storageNotSupported, stubbedPublicClientApplicationCalled };
//# sourceMappingURL=BrowserConfigurationAuthErrorCodes.mjs.map
