<template>
  <div class="navigation-test">
    <div class="container">
      <h1>導航測試頁面</h1>
      <p>此頁面用於測試路由導航功能和重複導航問題修復</p>

      <!-- 當前路由資訊 -->
      <div class="current-route">
        <h2>當前路由資訊</h2>
        <div class="info-grid">
          <div class="info-item">
            <label>路徑:</label>
            <span>{{ currentRoute.path }}</span>
          </div>
          <div class="info-item">
            <label>完整路徑:</label>
            <span>{{ currentRoute.fullPath }}</span>
          </div>
          <div class="info-item">
            <label>查詢參數:</label>
            <span>{{ JSON.stringify(currentRoute.query) }}</span>
          </div>
        </div>
      </div>

      <!-- 導航測試按鈕 -->
      <div class="test-section">
        <h2>導航測試</h2>
        <div class="button-grid">
          <button @click="testSameLocation" class="test-btn">
            測試相同位置導航
          </button>
          <button @click="testLoginRedirect" class="test-btn">
            測試登入重定向
          </button>
          <button @click="testChatNavigation" class="test-btn">
            測試聊天室導航
          </button>
          <button @click="testMultipleClicks" class="test-btn">
            測試多次點擊
          </button>
          <button @click="testQueryParams" class="test-btn">
            測試查詢參數導航
          </button>
          <button @click="testRapidNavigation" class="test-btn">
            測試快速導航
          </button>
        </div>
      </div>

      <!-- 測試結果 -->
      <div class="test-results" v-if="testResults.length > 0">
        <h2>測試結果</h2>
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            :class="['result-item', result.success ? 'success' : 'error']"
          >
            <div class="result-header">
              <span class="result-title">{{ result.test }}</span>
              <span class="result-status">{{ result.success ? '✅' : '❌' }}</span>
            </div>
            <div class="result-details">
              <div>時間: {{ result.timestamp }}</div>
              <div>訊息: {{ result.message }}</div>
              <div v-if="result.error">錯誤: {{ result.error }}</div>
            </div>
          </div>
        </div>
        <button @click="clearResults" class="clear-btn">清除結果</button>
      </div>

      <!-- 攔截器狀態 -->
      <div class="interceptor-status">
        <h2>導航攔截器狀態</h2>
        <div class="status-grid">
          <div class="status-item">
            <label>待處理導航:</label>
            <span>{{ interceptorStatus.pendingCount }}</span>
          </div>
          <div class="status-item">
            <label>最後導航:</label>
            <span>{{ interceptorStatus.lastNavigation || '無' }}</span>
          </div>
          <div class="status-item">
            <label>導航進行中:</label>
            <span>{{ interceptorStatus.hasNavigationInProgress ? '是' : '否' }}</span>
          </div>
        </div>
        <button @click="refreshInterceptorStatus" class="refresh-btn">
          刷新狀態
        </button>
      </div>

      <!-- 返回按鈕 -->
      <div class="actions">
        <button @click="goBack" class="back-btn">返回</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { safeNavigate } from '@/utils/routerUtils'

const router = useRouter()
const route = useRoute()

// 響應式數據
const testResults = ref([])
const interceptorStatus = ref({
  pendingCount: 0,
  lastNavigation: null,
  hasNavigationInProgress: false
})

// 計算屬性
const currentRoute = computed(() => route)

// 測試函數
const addTestResult = (test, success, message, error = null) => {
  testResults.value.unshift({
    test,
    success,
    message,
    error,
    timestamp: new Date().toLocaleTimeString()
  })
}

const testSameLocation = async () => {
  try {
    await safeNavigate(route.fullPath)
    addTestResult('相同位置導航', true, '成功跳過重複導航')
  } catch (error) {
    addTestResult('相同位置導航', false, '導航失敗', error.message)
  }
}

const testLoginRedirect = async () => {
  try {
    await safeNavigate({
      path: '/login',
      query: { redirect: '/chat' }
    })
    addTestResult('登入重定向', true, '成功導航到登入頁面')
  } catch (error) {
    addTestResult('登入重定向', false, '導航失敗', error.message)
  }
}

const testChatNavigation = async () => {
  try {
    await safeNavigate('/chat')
    addTestResult('聊天室導航', true, '成功導航到聊天室')
  } catch (error) {
    addTestResult('聊天室導航', false, '導航失敗', error.message)
  }
}

const testMultipleClicks = async () => {
  try {
    const promises = []
    for (let i = 0; i < 5; i++) {
      promises.push(safeNavigate('/chat'))
    }
    await Promise.all(promises)
    addTestResult('多次點擊', true, '成功處理多次導航請求')
  } catch (error) {
    addTestResult('多次點擊', false, '導航失敗', error.message)
  }
}

const testQueryParams = async () => {
  try {
    await safeNavigate({
      path: route.path,
      query: { test: Date.now() }
    })
    addTestResult('查詢參數導航', true, '成功添加查詢參數')
  } catch (error) {
    addTestResult('查詢參數導航', false, '導航失敗', error.message)
  }
}

const testRapidNavigation = async () => {
  try {
    await safeNavigate('/chat')
    await safeNavigate('/login')
    await safeNavigate('/chat')
    addTestResult('快速導航', true, '成功完成快速導航序列')
  } catch (error) {
    addTestResult('快速導航', false, '導航失敗', error.message)
  }
}

const clearResults = () => {
  testResults.value = []
}

const refreshInterceptorStatus = () => {
  if (window.navigationInterceptor) {
    const debugInfo = window.navigationInterceptor.getDebugInfo()
    interceptorStatus.value = {
      pendingCount: debugInfo.pendingNavigations.length,
      lastNavigation: debugInfo.lastNavigation?.to?.path || null,
      hasNavigationInProgress: debugInfo.hasNavigationInProgress
    }
  }
}

const goBack = () => {
  router.back()
}

// 生命週期
onMounted(() => {
  refreshInterceptorStatus()
  
  // 定期刷新攔截器狀態
  const interval = setInterval(refreshInterceptorStatus, 1000)
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.navigation-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h1, h2 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.current-route, .test-section, .test-results, .interceptor-status {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
}

.info-grid, .status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.info-item, .status-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.test-btn, .clear-btn, .refresh-btn, .back-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.test-btn {
  background: #007bff;
  color: white;
}

.test-btn:hover {
  background: #0056b3;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.refresh-btn {
  background: #28a745;
  color: white;
}

.back-btn {
  background: #6c757d;
  color: white;
}

.results-list {
  space-y: 12px;
}

.result-item {
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.result-item.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.result-item.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-title {
  font-weight: 600;
}

.result-details {
  font-size: 14px;
  color: #6c757d;
}

.actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}
</style>
