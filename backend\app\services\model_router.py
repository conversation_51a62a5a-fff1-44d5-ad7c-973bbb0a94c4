"""
模型路由服務
實作多模型路由和負載平衡邏輯，建立模型健康檢查機制
"""

import os
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import json
from dataclasses import dataclass, field

# 設定日誌
logger = logging.getLogger(__name__)

class ModelType(str, Enum):
    """支援的模型類型"""
    CHATGPT = "chatgpt"
    CLAUDE = "claude"
    GEMINI = "gemini"

class ModelStatus(str, Enum):
    """模型狀態"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    DISABLED = "disabled"

class LoadBalanceStrategy(str, Enum):
    """負載平衡策略"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    RANDOM = "random"
    HEALTH_BASED = "health_based"

@dataclass
class ModelConfig:
    """模型配置"""
    model_type: ModelType
    enabled: bool = True
    weight: float = 1.0
    max_requests_per_minute: int = 60
    max_concurrent_requests: int = 10
    timeout_seconds: int = 30
    retry_attempts: int = 3
    health_check_interval: int = 300  # 5 minutes
    config: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ModelHealth:
    """模型健康狀態"""
    model_type: ModelType
    status: ModelStatus = ModelStatus.UNKNOWN
    last_check: Optional[datetime] = None
    response_time_ms: float = 0.0
    success_rate: float = 0.0
    error_count: int = 0
    total_requests: int = 0
    concurrent_requests: int = 0
    last_error: Optional[str] = None

@dataclass
class ModelMetrics:
    """模型指標"""
    model_type: ModelType
    requests_count: int = 0
    success_count: int = 0
    error_count: int = 0
    total_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    
    @property
    def average_response_time(self) -> float:
        """平均回應時間"""
        if self.requests_count == 0:
            return 0.0
        return self.total_response_time / self.requests_count
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.requests_count == 0:
            return 0.0
        return self.success_count / self.requests_count

class ModelRouter:
    """模型路由器"""
    
    def __init__(self):
        # 載入配置
        self.load_balance_strategy = LoadBalanceStrategy(
            os.getenv("MODEL_LOAD_BALANCE_STRATEGY", "health_based")
        )
        self.health_check_enabled = os.getenv("MODEL_HEALTH_CHECK_ENABLED", "true").lower() == "true"
        self.failover_enabled = os.getenv("MODEL_FAILOVER_ENABLED", "true").lower() == "true"
        
        # 模型配置
        self.model_configs: Dict[ModelType, ModelConfig] = {}
        self.model_health: Dict[ModelType, ModelHealth] = {}
        self.model_metrics: Dict[ModelType, ModelMetrics] = {}
        
        # 路由狀態
        self.round_robin_index = 0
        self.active_models: List[ModelType] = []
        
        # 初始化模型配置
        self._initialize_model_configs()
        
        # 健康檢查任務
        self.health_check_task: Optional[asyncio.Task] = None
        
        # 客戶端實例（將由各模型客戶端注入）
        self.model_clients: Dict[ModelType, Any] = {}
    
    def _initialize_model_configs(self):
        """初始化模型配置"""
        
        # ChatGPT 配置
        self.model_configs[ModelType.CHATGPT] = ModelConfig(
            model_type=ModelType.CHATGPT,
            enabled=os.getenv("CHATGPT_ENABLED", "true").lower() == "true",
            weight=float(os.getenv("CHATGPT_WEIGHT", "1.0")),
            max_requests_per_minute=int(os.getenv("CHATGPT_MAX_RPM", "60")),
            max_concurrent_requests=int(os.getenv("CHATGPT_MAX_CONCURRENT", "10")),
            timeout_seconds=int(os.getenv("CHATGPT_TIMEOUT", "30")),
            config={
                "api_key": os.getenv("OPENAI_API_KEY"),
                "model": os.getenv("CHATGPT_MODEL", "gpt-4"),
                "max_tokens": int(os.getenv("CHATGPT_MAX_TOKENS", "2000")),
                "temperature": float(os.getenv("CHATGPT_TEMPERATURE", "0.7"))
            }
        )
        
        # Claude 配置
        self.model_configs[ModelType.CLAUDE] = ModelConfig(
            model_type=ModelType.CLAUDE,
            enabled=os.getenv("CLAUDE_ENABLED", "true").lower() == "true",
            weight=float(os.getenv("CLAUDE_WEIGHT", "1.0")),
            max_requests_per_minute=int(os.getenv("CLAUDE_MAX_RPM", "60")),
            max_concurrent_requests=int(os.getenv("CLAUDE_MAX_CONCURRENT", "10")),
            timeout_seconds=int(os.getenv("CLAUDE_TIMEOUT", "30")),
            config={
                "api_key": os.getenv("ANTHROPIC_API_KEY"),
                "model": os.getenv("CLAUDE_MODEL", "claude-3-sonnet-20240229"),
                "max_tokens": int(os.getenv("CLAUDE_MAX_TOKENS", "2000")),
                "temperature": float(os.getenv("CLAUDE_TEMPERATURE", "0.7"))
            }
        )
        
        # Gemini 配置
        self.model_configs[ModelType.GEMINI] = ModelConfig(
            model_type=ModelType.GEMINI,
            enabled=os.getenv("GEMINI_ENABLED", "true").lower() == "true",
            weight=float(os.getenv("GEMINI_WEIGHT", "1.0")),
            max_requests_per_minute=int(os.getenv("GEMINI_MAX_RPM", "60")),
            max_concurrent_requests=int(os.getenv("GEMINI_MAX_CONCURRENT", "10")),
            timeout_seconds=int(os.getenv("GEMINI_TIMEOUT", "30")),
            config={
                "api_key": os.getenv("GOOGLE_API_KEY"),
                "model": os.getenv("GEMINI_MODEL", "gemini-pro"),
                "max_tokens": int(os.getenv("GEMINI_MAX_TOKENS", "2000")),
                "temperature": float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
            }
        )
        
        # 初始化健康狀態和指標
        for model_type in ModelType:
            self.model_health[model_type] = ModelHealth(model_type=model_type)
            self.model_metrics[model_type] = ModelMetrics(model_type=model_type)
        
        # 更新活躍模型列表
        self._update_active_models()
    
    def _update_active_models(self):
        """更新活躍模型列表"""
        self.active_models = [
            model_type for model_type in ModelType
            if self.model_configs[model_type].enabled and
               self.model_health[model_type].status in [ModelStatus.HEALTHY, ModelStatus.DEGRADED]
        ]
        
        logger.info(f"活躍模型更新: {[m.value for m in self.active_models]}")
    
    def register_client(self, model_type: ModelType, client: Any):
        """註冊模型客戶端"""
        self.model_clients[model_type] = client
        logger.info(f"註冊 {model_type.value} 客戶端")
    
    async def start_health_checks(self):
        """啟動健康檢查"""
        if self.health_check_enabled and not self.health_check_task:
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            logger.info("模型健康檢查已啟動")
    
    async def stop_health_checks(self):
        """停止健康檢查"""
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
            self.health_check_task = None
            logger.info("模型健康檢查已停止")
    
    async def _health_check_loop(self):
        """健康檢查循環"""
        while True:
            try:
                await self._perform_health_checks()
                
                # 等待下次檢查
                min_interval = min(
                    config.health_check_interval 
                    for config in self.model_configs.values()
                )
                await asyncio.sleep(min_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康檢查循環錯誤: {e}")
                await asyncio.sleep(60)  # 錯誤時等待 1 分鐘
    
    async def _perform_health_checks(self):
        """執行健康檢查"""
        tasks = []
        
        for model_type in ModelType:
            if self.model_configs[model_type].enabled:
                task = asyncio.create_task(self._check_model_health(model_type))
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            self._update_active_models()
    
    async def _check_model_health(self, model_type: ModelType):
        """檢查單個模型健康狀態"""
        health = self.model_health[model_type]
        client = self.model_clients.get(model_type)
        
        if not client:
            health.status = ModelStatus.UNKNOWN
            health.last_error = "客戶端未註冊"
            return
        
        start_time = datetime.utcnow()
        
        try:
            # 執行健康檢查（簡單的測試請求）
            test_success = await self._test_model_client(client, model_type)
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            health.last_check = end_time
            health.response_time_ms = response_time
            
            if test_success:
                # 根據回應時間和成功率決定狀態
                if response_time < 5000 and health.success_rate > 0.9:
                    health.status = ModelStatus.HEALTHY
                elif response_time < 10000 and health.success_rate > 0.7:
                    health.status = ModelStatus.DEGRADED
                else:
                    health.status = ModelStatus.UNHEALTHY
                
                health.last_error = None
            else:
                health.status = ModelStatus.UNHEALTHY
                health.error_count += 1
            
        except Exception as e:
            health.status = ModelStatus.UNHEALTHY
            health.last_error = str(e)
            health.error_count += 1
            logger.warning(f"{model_type.value} 健康檢查失敗: {e}")
    
    async def _test_model_client(self, client: Any, model_type: ModelType) -> bool:
        """測試模型客戶端"""
        try:
            # 這裡應該呼叫實際的健康檢查方法
            # 暫時返回 True，實際實作時會調用各客戶端的 health_check 方法
            if hasattr(client, 'health_check'):
                return await client.health_check()
            else:
                # 如果沒有健康檢查方法，假設健康
                return True
        except Exception:
            return False
    
    def select_model(
        self, 
        preferred_model: Optional[ModelType] = None,
        exclude_models: Optional[List[ModelType]] = None
    ) -> Optional[ModelType]:
        """選擇模型"""
        
        # 過濾可用模型
        available_models = [
            model for model in self.active_models
            if model not in (exclude_models or [])
        ]
        
        if not available_models:
            logger.warning("沒有可用的模型")
            return None
        
        # 如果指定了偏好模型且可用，優先使用
        if preferred_model and preferred_model in available_models:
            return preferred_model
        
        # 根據策略選擇模型
        if self.load_balance_strategy == LoadBalanceStrategy.ROUND_ROBIN:
            return self._select_round_robin(available_models)
        elif self.load_balance_strategy == LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
            return self._select_weighted_round_robin(available_models)
        elif self.load_balance_strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
            return self._select_least_connections(available_models)
        elif self.load_balance_strategy == LoadBalanceStrategy.RANDOM:
            return self._select_random(available_models)
        elif self.load_balance_strategy == LoadBalanceStrategy.HEALTH_BASED:
            return self._select_health_based(available_models)
        else:
            return available_models[0]  # 預設返回第一個
    
    def _select_round_robin(self, models: List[ModelType]) -> ModelType:
        """輪詢選擇"""
        selected = models[self.round_robin_index % len(models)]
        self.round_robin_index = (self.round_robin_index + 1) % len(models)
        return selected
    
    def _select_weighted_round_robin(self, models: List[ModelType]) -> ModelType:
        """加權輪詢選擇"""
        import random
        
        weights = [self.model_configs[model].weight for model in models]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return models[0]
        
        # 使用加權隨機選擇
        rand_val = random.uniform(0, total_weight)
        current_weight = 0
        
        for i, weight in enumerate(weights):
            current_weight += weight
            if rand_val <= current_weight:
                return models[i]
        
        return models[-1]
    
    def _select_least_connections(self, models: List[ModelType]) -> ModelType:
        """最少連接選擇"""
        return min(
            models,
            key=lambda m: self.model_health[m].concurrent_requests
        )
    
    def _select_random(self, models: List[ModelType]) -> ModelType:
        """隨機選擇"""
        import random
        return random.choice(models)
    
    def _select_health_based(self, models: List[ModelType]) -> ModelType:
        """基於健康狀態選擇"""
        # 計算每個模型的分數（健康狀態 + 效能）
        scores = {}
        
        for model in models:
            health = self.model_health[model]
            metrics = self.model_metrics[model]
            
            # 基礎分數（根據健康狀態）
            if health.status == ModelStatus.HEALTHY:
                base_score = 100
            elif health.status == ModelStatus.DEGRADED:
                base_score = 70
            else:
                base_score = 30
            
            # 效能調整
            performance_score = 0
            if metrics.requests_count > 0:
                # 成功率加權
                performance_score += metrics.success_rate * 20
                
                # 回應時間加權（回應時間越短分數越高）
                if metrics.average_response_time > 0:
                    time_score = max(0, 20 - (metrics.average_response_time / 1000))
                    performance_score += time_score
            
            # 負載調整（並發請求越少分數越高）
            load_score = max(0, 10 - health.concurrent_requests)
            
            scores[model] = base_score + performance_score + load_score
        
        # 選擇分數最高的模型
        return max(scores.items(), key=lambda x: x[1])[0]
    
    def record_request_start(self, model_type: ModelType):
        """記錄請求開始"""
        health = self.model_health[model_type]
        health.concurrent_requests += 1
        health.total_requests += 1
        
        metrics = self.model_metrics[model_type]
        metrics.requests_count += 1
        metrics.last_request_time = datetime.utcnow()
    
    def record_request_end(
        self, 
        model_type: ModelType, 
        success: bool, 
        response_time_ms: float,
        error: Optional[str] = None
    ):
        """記錄請求結束"""
        health = self.model_health[model_type]
        health.concurrent_requests = max(0, health.concurrent_requests - 1)
        
        metrics = self.model_metrics[model_type]
        metrics.total_response_time += response_time_ms
        
        if success:
            metrics.success_count += 1
        else:
            metrics.error_count += 1
            health.error_count += 1
            if error:
                health.last_error = error
        
        # 更新成功率
        health.success_rate = metrics.success_rate
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """獲取可用模型列表"""
        models = []
        
        for model_type in ModelType:
            config = self.model_configs[model_type]
            health = self.model_health[model_type]
            metrics = self.model_metrics[model_type]
            
            models.append({
                "model_type": model_type.value,
                "enabled": config.enabled,
                "status": health.status.value,
                "health": {
                    "last_check": health.last_check.isoformat() if health.last_check else None,
                    "response_time_ms": health.response_time_ms,
                    "success_rate": health.success_rate,
                    "concurrent_requests": health.concurrent_requests,
                    "error_count": health.error_count
                },
                "metrics": {
                    "requests_count": metrics.requests_count,
                    "success_count": metrics.success_count,
                    "error_count": metrics.error_count,
                    "average_response_time": metrics.average_response_time,
                    "success_rate": metrics.success_rate
                },
                "config": {
                    "weight": config.weight,
                    "max_requests_per_minute": config.max_requests_per_minute,
                    "timeout_seconds": config.timeout_seconds
                }
            })
        
        return models
    
    def get_router_stats(self) -> Dict[str, Any]:
        """獲取路由器統計資訊"""
        return {
            "load_balance_strategy": self.load_balance_strategy.value,
            "health_check_enabled": self.health_check_enabled,
            "failover_enabled": self.failover_enabled,
            "active_models": [m.value for m in self.active_models],
            "total_models": len(self.model_configs),
            "healthy_models": len([
                m for m in ModelType 
                if self.model_health[m].status == ModelStatus.HEALTHY
            ]),
            "round_robin_index": self.round_robin_index,
            "models": self.get_available_models()
        }
    
    def enable_model(self, model_type: ModelType):
        """啟用模型"""
        if model_type in self.model_configs:
            self.model_configs[model_type].enabled = True
            self._update_active_models()
            logger.info(f"已啟用模型: {model_type.value}")
    
    def disable_model(self, model_type: ModelType):
        """停用模型"""
        if model_type in self.model_configs:
            self.model_configs[model_type].enabled = False
            self.model_health[model_type].status = ModelStatus.DISABLED
            self._update_active_models()
            logger.info(f"已停用模型: {model_type.value}")
    
    def update_model_weight(self, model_type: ModelType, weight: float):
        """更新模型權重"""
        if model_type in self.model_configs:
            self.model_configs[model_type].weight = max(0.0, weight)
            logger.info(f"已更新 {model_type.value} 權重: {weight}")

# 建立全域路由器實例
model_router = ModelRouter()