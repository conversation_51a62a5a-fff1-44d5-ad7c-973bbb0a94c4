"""
BPM 過濾服務
實作 BPM 相關性檢測演算法和語義分析邏輯
"""

import os
import re
import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime
from enum import Enum
import json

from ..config.bpm_keywords import BmpKeywordConfig

# 設定日誌
logger = logging.getLogger(__name__)

class FilterResult(str, Enum):
    """過濾結果類型"""
    ALLOWED = "allowed"
    REJECTED = "rejected"
    WARNING = "warning"
    NEEDS_REVIEW = "needs_review"

class ConfidenceLevel(str, Enum):
    """信心水準"""
    VERY_HIGH = "very_high"    # > 0.9
    HIGH = "high"              # 0.7 - 0.9
    MEDIUM = "medium"          # 0.5 - 0.7
    LOW = "low"                # 0.3 - 0.5
    VERY_LOW = "very_low"      # < 0.3

class BmpFilter:
    """BPM 過濾器類別"""
    
    def __init__(self):
        # 載入關鍵字配置
        self.keyword_config = BmpKeywordConfig()
        
        # 過濾配置
        self.min_confidence_threshold = float(os.getenv("BMP_MIN_CONFIDENCE", "0.3"))
        self.rejection_threshold = float(os.getenv("BMP_REJECTION_THRESHOLD", "0.5"))
        self.strict_mode = os.getenv("BMP_STRICT_MODE", "false").lower() == "true"
        
        # 語義分析配置
        self.enable_semantic_analysis = os.getenv("BMP_ENABLE_SEMANTIC", "true").lower() == "true"
        self.weight_exact_match = float(os.getenv("BMP_WEIGHT_EXACT", "1.0"))
        self.weight_partial_match = float(os.getenv("BMP_WEIGHT_PARTIAL", "0.7"))
        self.weight_context_match = float(os.getenv("BMP_WEIGHT_CONTEXT", "0.5"))
        
        # 統計資料
        self.filter_stats = {
            "total_requests": 0,
            "allowed": 0,
            "rejected": 0,
            "warnings": 0,
            "needs_review": 0,
            "confidence_distribution": {
                "very_high": 0,
                "high": 0,
                "medium": 0,
                "low": 0,
                "very_low": 0
            }
        }
    
    def analyze_content(self, text: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        分析內容的 BPM 相關性
        
        Args:
            text: 要分析的文本
            user_id: 用戶 ID（用於記錄）
        
        Returns:
            分析結果字典
        """
        self.filter_stats["total_requests"] += 1
        
        analysis_result = {
            "text": text,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat(),
            "result": FilterResult.ALLOWED,
            "confidence": 0.0,
            "confidence_level": ConfidenceLevel.VERY_LOW,
            "matched_keywords": [],
            "rejected_patterns": [],
            "warning_patterns": [],
            "topic_analysis": {},
            "suggestions": [],
            "processing_time_ms": 0
        }
        
        start_time = datetime.utcnow()
        
        try:
            # 基本驗證
            if not text or not text.strip():
                analysis_result["result"] = FilterResult.REJECTED
                analysis_result["suggestions"] = ["輸入內容不能為空"]
                return analysis_result
            
            text_clean = text.strip().lower()
            
            # 1. 檢查拒絕模式
            rejection_analysis = self._check_rejection_patterns(text_clean)
            if rejection_analysis["rejected"]:
                analysis_result["result"] = FilterResult.REJECTED
                analysis_result["rejected_patterns"] = rejection_analysis["patterns"]
                analysis_result["suggestions"] = rejection_analysis["suggestions"]
                self.filter_stats["rejected"] += 1
                return analysis_result
            
            # 2. 檢查警告模式
            warning_analysis = self._check_warning_patterns(text_clean)
            if warning_analysis["has_warnings"]:
                analysis_result["warning_patterns"] = warning_analysis["patterns"]
                analysis_result["suggestions"].extend(warning_analysis["suggestions"])
            
            # 3. BPM 關鍵字分析
            keyword_analysis = self._analyze_bmp_keywords(text_clean)
            analysis_result["matched_keywords"] = keyword_analysis["matched"]
            
            # 4. 主題分析
            topic_analysis = self._analyze_topics(text_clean)
            analysis_result["topic_analysis"] = topic_analysis
            
            # 5. 計算信心分數
            confidence_score = self._calculate_confidence(
                keyword_analysis, 
                topic_analysis, 
                len(text_clean.split())
            )
            analysis_result["confidence"] = confidence_score
            analysis_result["confidence_level"] = self._get_confidence_level(confidence_score)
            
            # 6. 決定最終結果
            final_result = self._determine_result(
                confidence_score, 
                warning_analysis["has_warnings"]
            )
            analysis_result["result"] = final_result
            
            # 7. 生成建議
            suggestions = self._generate_suggestions(
                confidence_score, 
                keyword_analysis, 
                topic_analysis
            )
            analysis_result["suggestions"].extend(suggestions)
            
            # 更新統計
            self._update_stats(final_result, analysis_result["confidence_level"])
            
        except Exception as e:
            logger.error(f"BMP 過濾分析失敗: {e}")
            analysis_result["result"] = FilterResult.NEEDS_REVIEW
            analysis_result["suggestions"] = ["系統分析失敗，請稍後再試"]
            self.filter_stats["needs_review"] += 1
        
        finally:
            # 記錄處理時間
            end_time = datetime.utcnow()
            processing_time = (end_time - start_time).total_seconds() * 1000
            analysis_result["processing_time_ms"] = processing_time
            
            logger.debug(f"BMP 過濾完成: {user_id}, 結果: {analysis_result['result']}, "
                        f"信心: {analysis_result['confidence']:.3f}, "
                        f"處理時間: {processing_time:.1f}ms")
        
        return analysis_result
    
    def _check_rejection_patterns(self, text: str) -> Dict[str, Any]:
        """檢查拒絕模式"""
        rejected_patterns = []
        suggestions = []
        
        # 檢查明確拒絕的關鍵字
        for category, keywords in self.keyword_config.rejection_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    rejected_patterns.append({
                        "category": category,
                        "keyword": keyword,
                        "reason": f"包含非 BPM 相關內容: {category}"
                    })
        
        # 檢查模式
        non_bmp_patterns = [
            (r'\b(password|密碼|登入|login)\b', "技術支援問題"),
            (r'\b(weather|天氣|新聞|news)\b', "一般資訊查詢"),
            (r'\b(game|遊戲|entertainment|娛樂)\b', "娛樂內容"),
            (r'\b(personal|個人|private|私人)\b', "個人事務"),
        ]
        
        for pattern, reason in non_bmp_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                rejected_patterns.append({
                    "pattern": pattern,
                    "reason": reason
                })
        
        if rejected_patterns:
            suggestions = [
                "請提出與 Business Process Management 相關的問題",
                "例如：流程優化、組織管理、專案協調、政策制定等主題",
                "如需其他協助，請聯絡相關部門"
            ]
        
        return {
            "rejected": len(rejected_patterns) > 0,
            "patterns": rejected_patterns,
            "suggestions": suggestions
        }
    
    def _check_warning_patterns(self, text: str) -> Dict[str, Any]:
        """檢查警告模式"""
        warning_patterns = []
        suggestions = []
        
        # 敏感資訊檢查
        sensitive_patterns = [
            (r'\b(confidential|機密|secret|秘密)\b', "機密資訊"),
            (r'\b(password|密碼|token|令牌)\b', "認證資訊"),
            (r'\b(salary|薪資|budget|預算)\b', "財務敏感資訊"),
            (r'\b\d{4}[-/]\d{2}[-/]\d{2}\b', "日期格式"),
            (r'\b\d{3}-\d{2}-\d{4}\b', "可能的識別號碼"),
        ]
        
        for pattern, category in sensitive_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                warning_patterns.append({
                    "category": category,
                    "pattern": pattern,
                    "matches": matches
                })
        
        if warning_patterns:
            suggestions = [
                "檢測到可能的敏感資訊，請注意資訊安全",
                "避免在聊天中提及機密、個人識別或財務資訊",
                "如需討論敏感主題，請使用適當的安全管道"
            ]
        
        return {
            "has_warnings": len(warning_patterns) > 0,
            "patterns": warning_patterns,
            "suggestions": suggestions
        }
    
    def _analyze_bmp_keywords(self, text: str) -> Dict[str, Any]:
        """分析 BPM 關鍵字"""
        matched_keywords = []
        total_score = 0.0
        
        # 精確匹配
        for category, keywords in self.keyword_config.core_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    score = self.weight_exact_match
                    matched_keywords.append({
                        "keyword": keyword,
                        "category": category,
                        "match_type": "exact",
                        "score": score
                    })
                    total_score += score
        
        # 部分匹配（詞根匹配）
        for category, keywords in self.keyword_config.related_keywords.items():
            for keyword in keywords:
                # 檢查詞根匹配
                if self._partial_match(keyword, text):
                    score = self.weight_partial_match
                    matched_keywords.append({
                        "keyword": keyword,
                        "category": category,
                        "match_type": "partial",
                        "score": score
                    })
                    total_score += score * 0.8  # 部分匹配降權
        
        # 上下文匹配
        context_matches = self._analyze_context_keywords(text)
        for match in context_matches:
            matched_keywords.append(match)
            total_score += match["score"]
        
        return {
            "matched": matched_keywords,
            "total_score": total_score,
            "unique_categories": list(set(m["category"] for m in matched_keywords))
        }
    
    def _analyze_topics(self, text: str) -> Dict[str, Any]:
        """主題分析"""
        topic_scores = {}
        
        # 預定義主題模式
        topic_patterns = {
            "流程管理": [
                r'\b(流程|process|workflow|作業流程)\b',
                r'\b(優化|optimize|improve|改善)\b',
                r'\b(效率|efficiency|效能|performance)\b'
            ],
            "組織管理": [
                r'\b(組織|organization|架構|structure)\b',
                r'\b(部門|department|team|團隊)\b',
                r'\b(管理|management|coordinate|協調)\b'
            ],
            "專案管理": [
                r'\b(專案|project|計畫|plan)\b',
                r'\b(任務|task|milestone|里程碑)\b',
                r'\b(進度|progress|schedule|排程)\b'
            ],
            "政策規範": [
                r'\b(政策|policy|規則|rule)\b',
                r'\b(合規|compliance|標準|standard)\b',
                r'\b(指導|guideline|程序|procedure)\b'
            ]
        }
        
        for topic, patterns in topic_patterns.items():
            score = 0
            matches = []
            
            for pattern in patterns:
                pattern_matches = re.findall(pattern, text, re.IGNORECASE)
                if pattern_matches:
                    score += len(pattern_matches) * 0.2
                    matches.extend(pattern_matches)
            
            if score > 0:
                topic_scores[topic] = {
                    "score": min(score, 1.0),  # 限制最大分數
                    "matches": matches,
                    "confidence": min(score / len(patterns), 1.0)
                }
        
        # 識別主要主題
        primary_topic = None
        if topic_scores:
            primary_topic = max(topic_scores.items(), key=lambda x: x[1]["score"])[0]
        
        return {
            "topics": topic_scores,
            "primary_topic": primary_topic,
            "topic_count": len(topic_scores)
        }
    
    def _partial_match(self, keyword: str, text: str) -> bool:
        """部分匹配檢查"""
        # 簡單的詞根匹配
        if len(keyword) >= 3:
            root = keyword[:len(keyword)//2]
            return root in text
        return False
    
    def _analyze_context_keywords(self, text: str) -> List[Dict[str, Any]]:
        """上下文關鍵字分析"""
        context_matches = []
        
        # 上下文模式（相關詞彙組合）
        context_patterns = [
            {
                "keywords": ["如何", "方法", "步驟"],
                "context": ["流程", "管理", "優化"],
                "category": "流程諮詢",
                "score": 0.6
            },
            {
                "keywords": ["建議", "推薦", "最佳"],
                "context": ["實務", "做法", "方案"],
                "category": "最佳實務",
                "score": 0.5
            },
            {
                "keywords": ["問題", "困難", "挑戰"],
                "context": ["解決", "改善", "處理"],
                "category": "問題解決",
                "score": 0.4
            }
        ]
        
        for pattern in context_patterns:
            keyword_found = any(kw in text for kw in pattern["keywords"])
            context_found = any(ctx in text for ctx in pattern["context"])
            
            if keyword_found and context_found:
                context_matches.append({
                    "category": pattern["category"],
                    "match_type": "context",
                    "score": pattern["score"],
                    "keywords": pattern["keywords"],
                    "context": pattern["context"]
                })
        
        return context_matches
    
    def _calculate_confidence(
        self, 
        keyword_analysis: Dict[str, Any], 
        topic_analysis: Dict[str, Any], 
        word_count: int
    ) -> float:
        """計算信心分數"""
        base_score = keyword_analysis["total_score"]
        
        # 主題加權
        topic_bonus = 0.0
        if topic_analysis["primary_topic"]:
            primary_score = topic_analysis["topics"][topic_analysis["primary_topic"]]["score"]
            topic_bonus = primary_score * 0.3
        
        # 多樣性加權
        diversity_bonus = len(keyword_analysis["unique_categories"]) * 0.1
        
        # 長度懲罰（過短的文本降低信心）
        length_factor = 1.0
        if word_count < 3:
            length_factor = 0.5
        elif word_count < 5:
            length_factor = 0.8
        
        # 計算最終分數
        confidence = (base_score + topic_bonus + diversity_bonus) * length_factor
        
        # 正規化到 0-1 範圍
        return min(confidence / 2.0, 1.0)
    
    def _get_confidence_level(self, confidence: float) -> ConfidenceLevel:
        """獲取信心水準"""
        if confidence >= 0.9:
            return ConfidenceLevel.VERY_HIGH
        elif confidence >= 0.7:
            return ConfidenceLevel.HIGH
        elif confidence >= 0.5:
            return ConfidenceLevel.MEDIUM
        elif confidence >= 0.3:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def _determine_result(self, confidence: float, has_warnings: bool) -> FilterResult:
        """決定最終結果"""
        if confidence < self.min_confidence_threshold:
            return FilterResult.REJECTED
        elif confidence < self.rejection_threshold:
            return FilterResult.NEEDS_REVIEW
        elif has_warnings:
            return FilterResult.WARNING
        else:
            return FilterResult.ALLOWED
    
    def _generate_suggestions(
        self, 
        confidence: float, 
        keyword_analysis: Dict[str, Any], 
        topic_analysis: Dict[str, Any]
    ) -> List[str]:
        """生成改善建議"""
        suggestions = []
        
        if confidence < 0.5:
            suggestions.extend([
                "為了獲得更精確的回答，建議在問題中包含更多 BPM 相關術語",
                "可以具體描述您想了解的業務流程管理問題"
            ])
        
        if len(keyword_analysis["unique_categories"]) == 0:
            suggestions.append("嘗試使用如：流程、管理、組織、專案、政策等關鍵字")
        
        if not topic_analysis["primary_topic"]:
            suggestions.append("明確指出您關注的 BPM 領域，例如流程優化、組織管理等")
        
        if confidence >= 0.7:
            suggestions.append("您的問題與 BPM 高度相關，將提供詳細的專業回答")
        
        return suggestions
    
    def _update_stats(self, result: FilterResult, confidence_level: ConfidenceLevel):
        """更新統計資料"""
        if result == FilterResult.ALLOWED:
            self.filter_stats["allowed"] += 1
        elif result == FilterResult.REJECTED:
            self.filter_stats["rejected"] += 1
        elif result == FilterResult.WARNING:
            self.filter_stats["warnings"] += 1
        elif result == FilterResult.NEEDS_REVIEW:
            self.filter_stats["needs_review"] += 1
        
        self.filter_stats["confidence_distribution"][confidence_level.value] += 1
    
    def get_filter_stats(self) -> Dict[str, Any]:
        """獲取過濾統計"""
        total = self.filter_stats["total_requests"]
        if total == 0:
            return self.filter_stats
        
        return {
            **self.filter_stats,
            "success_rate": (self.filter_stats["allowed"] + self.filter_stats["warnings"]) / total,
            "rejection_rate": self.filter_stats["rejected"] / total,
            "review_rate": self.filter_stats["needs_review"] / total
        }
    
    def reset_stats(self):
        """重置統計資料"""
        self.filter_stats = {
            "total_requests": 0,
            "allowed": 0,
            "rejected": 0,
            "warnings": 0,
            "needs_review": 0,
            "confidence_distribution": {
                "very_high": 0,
                "high": 0,
                "medium": 0,
                "low": 0,
                "very_low": 0
            }
        }

# 建立全域過濾器實例
bmp_filter = BmpFilter()