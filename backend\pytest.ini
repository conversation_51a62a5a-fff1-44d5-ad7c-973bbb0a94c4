[tool:pytest]
# pytest 設定檔

# 測試目錄
testpaths = tests

# Python 檔案模式
python_files = test_*.py *_test.py

# Python 類別模式
python_classes = Test*

# Python 函數模式
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 額外選項
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80

# 標記定義
markers =
    unit: 單元測試
    integration: 整合測試
    e2e: 端對端測試
    slow: 執行時間較長的測試
    auth: 認證相關測試
    api: API 端點測試

# 非同步測試支援
asyncio_mode = auto

# 測試覆蓋率設定
[coverage:run]
source = app
omit = 
    */tests/*
    */migrations/*
    */venv/*
    */env/*
    */__pycache__/*
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.: