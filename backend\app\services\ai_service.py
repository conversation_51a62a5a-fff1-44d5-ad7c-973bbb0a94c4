"""
AI 服務模組
整合多個 AI 模型 (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>) 提供智能問答服務
"""

import os
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
from enum import Enum
import json
import asyncio
import httpx
from fastapi import HTTPException, status

# AI 客戶端庫
import openai
from anthropic import AsyncAnthropic
import google.generativeai as genai

# 設定日誌
logger = logging.getLogger(__name__)

class AIModel(str, Enum):
    """支援的 AI 模型"""
    CHATGPT = "chatgpt"
    CLAUDE = "claude" 
    GEMINI = "gemini"

class MessageRole(str, Enum):
    """訊息角色"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"

class AIMessage:
    """AI 訊息資料結構"""
    
    def __init__(self, role: MessageRole, content: str, metadata: Optional[Dict] = None):
        self.role = role
        self.content = content
        self.metadata = metadata or {}
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "role": self.role.value,
            "content": self.content,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }

class AIResponse:
    """AI 回應資料結構"""
    
    def __init__(
        self,
        content: str,
        model: AIModel,
        usage: Optional[Dict] = None,
        metadata: Optional[Dict] = None
    ):
        self.content = content
        self.model = model
        self.usage = usage or {}
        self.metadata = metadata or {}
        self.timestamp = datetime.utcnow()
        self.response_time_ms = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "content": self.content,
            "model": self.model.value,
            "usage": self.usage,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
            "response_time_ms": self.response_time_ms
        }

class AIService:
    """AI 服務主類"""
    
    def __init__(self):
        # API 配置
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        self.google_api_key = os.getenv("GOOGLE_API_KEY")
        
        # 模型配置
        self.chatgpt_model = os.getenv("CHATGPT_MODEL", "gpt-4")
        self.claude_model = os.getenv("CLAUDE_MODEL", "claude-3-sonnet-20240229")
        self.gemini_model = os.getenv("GEMINI_MODEL", "gemini-pro")
        
        # 初始化客戶端
        self._initialize_clients()
        
        # BPM 系統提示詞
        self.system_prompt = self._load_system_prompt()
        
        # 請求配置
        self.max_tokens = int(os.getenv("AI_MAX_TOKENS", "2000"))
        self.temperature = float(os.getenv("AI_TEMPERATURE", "0.7"))
        self.timeout_seconds = int(os.getenv("AI_TIMEOUT", "30"))
        
        # 統計
        self.request_stats = {
            AIModel.CHATGPT: {"requests": 0, "success": 0, "errors": 0},
            AIModel.CLAUDE: {"requests": 0, "success": 0, "errors": 0},
            AIModel.GEMINI: {"requests": 0, "success": 0, "errors": 0}
        }
    
    def _initialize_clients(self):
        """初始化 AI 客戶端"""
        try:
            # OpenAI 客戶端
            if self.openai_api_key:
                openai.api_key = self.openai_api_key
                self.openai_client = openai
                logger.info("OpenAI 客戶端初始化成功")
            else:
                self.openai_client = None
                logger.warning("OpenAI API Key 未設定")
            
            # Anthropic 客戶端
            if self.anthropic_api_key:
                self.anthropic_client = AsyncAnthropic(api_key=self.anthropic_api_key)
                logger.info("Anthropic 客戶端初始化成功")
            else:
                self.anthropic_client = None
                logger.warning("Anthropic API Key 未設定")
            
            # Google 客戶端
            if self.google_api_key:
                genai.configure(api_key=self.google_api_key)
                self.gemini_client = genai.GenerativeModel(self.gemini_model)
                logger.info("Google Gemini 客戶端初始化成功")
            else:
                self.gemini_client = None
                logger.warning("Google API Key 未設定")
                
        except Exception as e:
            logger.error(f"AI 客戶端初始化失敗: {e}")
    
    def _load_system_prompt(self) -> str:
        """載入系統提示詞"""
        default_prompt = """您是一個專業的 Business Process Management (BPM) 助理。您的任務是協助用戶解決業務流程管理相關的問題。

您的專業領域包括：
- 業務流程設計與優化
- 組織架構與管理
- 專案管理與協調
- 政策制定與合規
- 系統整合與自動化
- 績效分析與改善

請遵循以下原則：
1. 只回答與 BPM 相關的問題
2. 提供實用、具體的建議
3. 使用專業但易懂的語言
4. 必要時提供步驟化的解決方案
5. 如果問題超出 BPM 範圍，請禮貌地引導用戶回到相關主題

請用繁體中文回答，並保持專業、友善的語調。"""

        # 從環境變數載入自定義提示詞
        custom_prompt = os.getenv("AI_SYSTEM_PROMPT")
        if custom_prompt:
            return custom_prompt
        
        return default_prompt
    
    def get_available_models(self) -> List[AIModel]:
        """獲取可用的 AI 模型"""
        available = []
        
        if self.openai_client:
            available.append(AIModel.CHATGPT)
        if self.anthropic_client:
            available.append(AIModel.CLAUDE)
        if self.gemini_client:
            available.append(AIModel.GEMINI)
        
        return available
    
    async def generate_response(
        self,
        messages: List[AIMessage],
        model: AIModel,
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """生成 AI 回應"""
        
        if model not in self.get_available_models():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"模型 {model.value} 不可用"
            )
        
        self.request_stats[model]["requests"] += 1
        start_time = datetime.utcnow()
        
        try:
            if model == AIModel.CHATGPT:
                response = await self._call_chatgpt(messages, stream, **kwargs)
            elif model == AIModel.CLAUDE:
                response = await self._call_claude(messages, stream, **kwargs)
            elif model == AIModel.GEMINI:
                response = await self._call_gemini(messages, stream, **kwargs)
            else:
                raise ValueError(f"不支援的模型: {model}")
            
            # 計算回應時間
            end_time = datetime.utcnow()
            response.response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            self.request_stats[model]["success"] += 1
            logger.info(f"AI 回應生成成功 - 模型: {model.value}, 時間: {response.response_time_ms}ms")
            
            return response
            
        except Exception as e:
            self.request_stats[model]["errors"] += 1
            logger.error(f"AI 回應生成失敗 - 模型: {model.value}, 錯誤: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI 服務暫時無法使用: {str(e)}"
            )
    
    async def _call_chatgpt(
        self, 
        messages: List[AIMessage], 
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """呼叫 ChatGPT API"""
        
        # 轉換訊息格式
        openai_messages = [{"role": "system", "content": self.system_prompt}]
        for msg in messages:
            openai_messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        try:
            if stream:
                # 串流回應 (待實作)
                response = await self.openai_client.ChatCompletion.acreate(
                    model=self.chatgpt_model,
                    messages=openai_messages,
                    max_tokens=kwargs.get("max_tokens", self.max_tokens),
                    temperature=kwargs.get("temperature", self.temperature),
                    stream=True,
                    timeout=self.timeout_seconds
                )
                # 處理串流回應
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content
            else:
                response = await self.openai_client.ChatCompletion.acreate(
                    model=self.chatgpt_model,
                    messages=openai_messages,
                    max_tokens=kwargs.get("max_tokens", self.max_tokens),
                    temperature=kwargs.get("temperature", self.temperature),
                    timeout=self.timeout_seconds
                )
                content = response.choices[0].message.content
            
            # 使用統計
            usage = {
                "prompt_tokens": response.usage.prompt_tokens if hasattr(response, 'usage') else 0,
                "completion_tokens": response.usage.completion_tokens if hasattr(response, 'usage') else 0,
                "total_tokens": response.usage.total_tokens if hasattr(response, 'usage') else 0
            }
            
            return AIResponse(
                content=content,
                model=AIModel.CHATGPT,
                usage=usage,
                metadata={"model_version": self.chatgpt_model}
            )
            
        except Exception as e:
            logger.error(f"ChatGPT API 呼叫失敗: {e}")
            raise
    
    async def _call_claude(
        self, 
        messages: List[AIMessage], 
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """呼叫 Claude API"""
        
        # 轉換訊息格式
        claude_messages = []
        for msg in messages:
            if msg.role != MessageRole.SYSTEM:  # Claude 系統訊息另外處理
                claude_messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })
        
        try:
            if stream:
                # 串流回應
                response = await self.anthropic_client.messages.create(
                    model=self.claude_model,
                    system=self.system_prompt,
                    messages=claude_messages,
                    max_tokens=kwargs.get("max_tokens", self.max_tokens),
                    temperature=kwargs.get("temperature", self.temperature),
                    stream=True
                )
                
                content = ""
                usage = {}
                async for chunk in response:
                    if chunk.type == "content_block_delta":
                        content += chunk.delta.text
                    elif chunk.type == "message_delta":
                        usage = chunk.usage.__dict__ if hasattr(chunk, 'usage') else {}
            else:
                response = await self.anthropic_client.messages.create(
                    model=self.claude_model,
                    system=self.system_prompt,
                    messages=claude_messages,
                    max_tokens=kwargs.get("max_tokens", self.max_tokens),
                    temperature=kwargs.get("temperature", self.temperature)
                )
                content = response.content[0].text
                usage = response.usage.__dict__ if hasattr(response, 'usage') else {}
            
            return AIResponse(
                content=content,
                model=AIModel.CLAUDE,
                usage=usage,
                metadata={"model_version": self.claude_model}
            )
            
        except Exception as e:
            logger.error(f"Claude API 呼叫失敗: {e}")
            raise
    
    async def _call_gemini(
        self, 
        messages: List[AIMessage], 
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """呼叫 Gemini API"""
        
        # 建構對話歷史
        chat_history = []
        user_input = ""
        
        for msg in messages:
            if msg.role == MessageRole.USER:
                user_input = msg.content
            elif msg.role == MessageRole.ASSISTANT:
                chat_history.append({
                    "role": "user",
                    "parts": [user_input] if user_input else [""]
                })
                chat_history.append({
                    "role": "model", 
                    "parts": [msg.content]
                })
        
        try:
            # 設定生成配置
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=kwargs.get("max_tokens", self.max_tokens),
                temperature=kwargs.get("temperature", self.temperature)
            )
            
            # 設定安全設定
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}
            ]
            
            if stream:
                # 串流回應
                chat = self.gemini_client.start_chat(history=chat_history)
                response = await chat.send_message_async(
                    f"{self.system_prompt}\n\n{user_input}",
                    generation_config=generation_config,
                    safety_settings=safety_settings,
                    stream=True
                )
                
                content = ""
                async for chunk in response:
                    content += chunk.text
            else:
                chat = self.gemini_client.start_chat(history=chat_history)
                response = await chat.send_message_async(
                    f"{self.system_prompt}\n\n{user_input}",
                    generation_config=generation_config,
                    safety_settings=safety_settings
                )
                content = response.text
            
            # Gemini 使用統計 (如果可用)
            usage = {}
            if hasattr(response, 'usage_metadata'):
                usage = {
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "completion_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.total_token_count
                }
            
            return AIResponse(
                content=content,
                model=AIModel.GEMINI,
                usage=usage,
                metadata={"model_version": self.gemini_model}
            )
            
        except Exception as e:
            logger.error(f"Gemini API 呼叫失敗: {e}")
            raise
    
    async def generate_streaming_response(
        self,
        messages: List[AIMessage],
        model: AIModel,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """生成串流回應"""
        
        if model not in self.get_available_models():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"模型 {model.value} 不可用"
            )
        
        try:
            if model == AIModel.CHATGPT:
                async for chunk in self._stream_chatgpt(messages, **kwargs):
                    yield chunk
            elif model == AIModel.CLAUDE:
                async for chunk in self._stream_claude(messages, **kwargs):
                    yield chunk
            elif model == AIModel.GEMINI:
                async for chunk in self._stream_gemini(messages, **kwargs):
                    yield chunk
                    
        except Exception as e:
            logger.error(f"串流回應生成失敗: {e}")
            yield f"錯誤: {str(e)}"
    
    async def _stream_chatgpt(self, messages: List[AIMessage], **kwargs) -> AsyncGenerator[str, None]:
        """ChatGPT 串流回應"""
        # 實作 ChatGPT 串流邏輯
        pass
    
    async def _stream_claude(self, messages: List[AIMessage], **kwargs) -> AsyncGenerator[str, None]:
        """Claude 串流回應"""
        # 實作 Claude 串流邏輯
        pass
    
    async def _stream_gemini(self, messages: List[AIMessage], **kwargs) -> AsyncGenerator[str, None]:
        """Gemini 串流回應"""
        # 實作 Gemini 串流邏輯
        pass
    
    def get_model_info(self, model: AIModel) -> Dict[str, Any]:
        """獲取模型資訊"""
        model_info = {
            AIModel.CHATGPT: {
                "name": "ChatGPT",
                "provider": "OpenAI",
                "model_version": self.chatgpt_model,
                "available": self.openai_client is not None,
                "description": "OpenAI 的旗艦對話 AI 模型，擅長理解複雜問題並提供詳細回答"
            },
            AIModel.CLAUDE: {
                "name": "Claude",
                "provider": "Anthropic",
                "model_version": self.claude_model,
                "available": self.anthropic_client is not None,
                "description": "Anthropic 的 AI 助理，注重安全性和有用性，擅長分析和推理"
            },
            AIModel.GEMINI: {
                "name": "Gemini",
                "provider": "Google",
                "model_version": self.gemini_model,
                "available": self.gemini_client is not None,
                "description": "Google 的多模態 AI 模型，整合了先進的語言理解能力"
            }
        }
        
        return model_info.get(model, {})
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取服務統計"""
        total_requests = sum(stats["requests"] for stats in self.request_stats.values())
        total_success = sum(stats["success"] for stats in self.request_stats.values())
        total_errors = sum(stats["errors"] for stats in self.request_stats.values())
        
        return {
            "total_requests": total_requests,
            "success_rate": (total_success / total_requests) if total_requests > 0 else 0,
            "error_rate": (total_errors / total_requests) if total_requests > 0 else 0,
            "model_stats": self.request_stats,
            "available_models": [model.value for model in self.get_available_models()]
        }
    
    def reset_stats(self):
        """重置統計"""
        for model in self.request_stats:
            self.request_stats[model] = {"requests": 0, "success": 0, "errors": 0}

# 建立全域服務實例
ai_service = AIService()