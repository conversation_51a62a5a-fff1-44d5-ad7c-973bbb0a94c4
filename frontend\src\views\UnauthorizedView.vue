<template>
  <div class="unauthorized-view">
    <div class="unauthorized-container">
      <div class="unauthorized-content">
        <div class="error-illustration">
          <div class="error-code">403</div>
          <div class="error-icon">
            <i class="icon-forbidden"></i>
          </div>
        </div>
        
        <div class="error-message">
          <h1>權限不足</h1>
          <p>抱歉，您沒有足夠的權限存取此頁面。</p>
          <p class="permission-hint">請聯繫系統管理員以獲取相關權限。</p>
        </div>
        
        <div class="user-info" v-if="userInfo">
          <div class="info-card">
            <h3>當前用戶資訊</h3>
            <div class="info-row">
              <span class="info-label">用戶名稱：</span>
              <span class="info-value">{{ userInfo.name }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">電子郵件：</span>
              <span class="info-value">{{ userInfo.email }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">部門：</span>
              <span class="info-value">{{ userInfo.department }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">職位：</span>
              <span class="info-value">{{ userInfo.jobTitle }}</span>
            </div>
          </div>
        </div>
        
        <div class="error-actions">
          <router-link to="/chat" class="btn btn-primary">
            <i class="icon-home"></i>
            回到首頁
          </router-link>
          
          <button @click="goBack" class="btn btn-secondary">
            <i class="icon-back"></i>
            返回上頁
          </button>
          
          <button @click="contactAdmin" class="btn btn-ghost">
            <i class="icon-contact"></i>
            聯繫管理員
          </button>
        </div>
        
        <div class="permission-info">
          <div class="info-section">
            <h3>需要幫助？</h3>
            <p>如果您認為這是一個錯誤，請嘗試以下操作：</p>
            <ul class="help-list">
              <li>確認您已正確登入</li>
              <li>檢查您的用戶權限設定</li>
              <li>聯繫系統管理員申請權限</li>
              <li>重新登入後再試</li>
            </ul>
          </div>
          
          <div class="contact-info">
            <h4>聯繫資訊</h4>
            <div class="contact-item">
              <i class="icon-email"></i>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <i class="icon-phone"></i>
              <span>內線: 1234</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const router = useRouter()
const authStore = useAuthStore()

const userInfo = computed(() => {
  const user = authStore.state.user
  const employee = authStore.state.employee
  
  if (!user) return null
  
  return {
    name: user.display_name,
    email: user.email,
    department: user.department || employee?.department || '未指定',
    jobTitle: user.job_title || employee?.job_title || '未指定'
  }
})

const goBack = async () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    try {
      // 使用安全導航避免重複導航錯誤
      const { safeNavigate } = await import('@/utils/routerUtils')
      await safeNavigate('/chat', { replace: true })
    } catch (error) {
      console.error('導航到聊天室失敗:', error)
      // 回退到直接導航
      if (!error.message?.includes('Avoided redundant navigation')) {
        router.push('/chat').catch(err => {
          if (!err.message?.includes('Avoided redundant navigation')) {
            console.error('回退導航也失敗:', err)
          }
        })
      }
    }
  }
}

const contactAdmin = () => {
  const subject = encodeURIComponent('BPM Chatbot 權限申請')
  const body = encodeURIComponent(`
用戶資訊：
- 姓名: ${userInfo.value?.name || 'N/A'}
- 郵箱: ${userInfo.value?.email || 'N/A'}
- 部門: ${userInfo.value?.department || 'N/A'}

申請內容：
- 頁面: ${window.location.pathname}
- 時間: ${new Date().toLocaleString('zh-TW')}
- 描述: 請在此說明您需要的權限

請管理員協助處理權限申請。
  `.trim())
  
  const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`
  window.location.href = mailtoLink
}
</script>

<style scoped>
.unauthorized-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
  padding: var(--spacing-4);
}

.unauthorized-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.error-illustration {
  margin-bottom: var(--spacing-6);
}

.error-code {
  font-size: 6rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-warning);
  line-height: 1;
  margin-bottom: var(--spacing-3);
}

.error-icon {
  font-size: 3rem;
  color: var(--color-warning);
  margin-bottom: var(--spacing-4);
}

.error-message h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-3);
}

.error-message p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-relaxed);
}

.permission-hint {
  color: var(--color-warning) !important;
  font-weight: var(--font-weight-medium);
}

.user-info {
  margin: var(--spacing-8) 0;
}

.info-card {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.info-card h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-4);
  text-align: center;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--color-border-light);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: var(--font-weight-normal);
  text-align: right;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3);
  margin: var(--spacing-8) 0;
  flex-wrap: wrap;
}

.permission-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  margin-top: var(--spacing-8);
  text-align: left;
}

.info-section {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
}

.info-section h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-3);
}

.info-section p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-normal);
}

.help-list {
  list-style: none;
  padding: 0;
}

.help-list li {
  padding: var(--spacing-2) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  position: relative;
  padding-left: var(--spacing-4);
}

.help-list li::before {
  content: '•';
  color: var(--color-primary);
  position: absolute;
  left: 0;
  font-weight: var(--font-weight-bold);
}

.contact-info {
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
}

.contact-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary-dark);
  margin-bottom: var(--spacing-4);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) 0;
  font-size: var(--font-size-sm);
  color: var(--color-primary-dark);
}

.contact-item i {
  color: var(--color-primary);
  font-size: 1.1em;
}

@media (max-width: 768px) {
  .error-code {
    font-size: 4rem;
  }
  
  .error-message h1 {
    font-size: var(--font-size-2xl);
  }
  
  .error-message p {
    font-size: var(--font-size-base);
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .permission-info {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .info-card,
  .info-section,
  .contact-info {
    padding: var(--spacing-4);
  }
}

.icon-forbidden::before { content: '🚫'; }
.icon-home::before { content: '🏠'; }
.icon-back::before { content: '← '; }
.icon-contact::before { content: '📞'; }
.icon-email::before { content: '✉️'; }
.icon-phone::before { content: '📞'; }
</style>