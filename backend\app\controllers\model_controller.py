"""
模型控制器
實作模型選擇和管理的 API 端點，包含 /api/models/available, /api/models/select 等
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from ..models.model import ModelType, ModelStatus, LoadBalanceStrategy
from ..models.auth import Employee
from ..services.model_router import model_router
from ..middleware.auth import get_current_employee
from ..utils.response import APIResponse, create_error_response, create_success_response

# 設定日誌
logger = logging.getLogger(__name__)

# 建立路由器
router = APIRouter(prefix="/api/models", tags=["models"])

class ModelInfo(BaseModel):
    """模型資訊模型"""
    
    model_type: str = Field(..., description="模型類型")
    enabled: bool = Field(..., description="是否啟用")
    status: str = Field(..., description="模型狀態")
    health: Dict[str, Any] = Field(..., description="健康狀態")
    metrics: Dict[str, Any] = Field(..., description="使用指標")
    config: Dict[str, Any] = Field(..., description="配置資訊")

class ModelListResponse(BaseModel):
    """模型列表回應"""
    
    models: List[ModelInfo] = Field(..., description="模型列表")
    total_count: int = Field(..., description="總模型數")
    available_count: int = Field(..., description="可用模型數")
    load_balance_strategy: str = Field(..., description="負載平衡策略")

class ModelSelectionRequest(BaseModel):
    """模型選擇請求"""
    
    preferred_model: Optional[ModelType] = Field(default=None, description="偏好模型")
    exclude_models: Optional[List[ModelType]] = Field(default=[], description="排除模型")
    
    class Config:
        schema_extra = {
            "example": {
                "preferred_model": "chatgpt",
                "exclude_models": ["gemini"]
            }
        }

class ModelSelectionResponse(BaseModel):
    """模型選擇回應"""
    
    selected_model: str = Field(..., description="選中的模型")
    reason: str = Field(..., description="選擇原因")
    alternatives: List[str] = Field(..., description="備選模型")
    selection_time_ms: float = Field(..., description="選擇耗時（毫秒）")

class ModelConfigRequest(BaseModel):
    """模型配置請求"""
    
    model_type: ModelType = Field(..., description="模型類型")
    enabled: Optional[bool] = Field(default=None, description="是否啟用")
    weight: Optional[float] = Field(default=None, ge=0.0, le=10.0, description="負載平衡權重")
    
    class Config:
        schema_extra = {
            "example": {
                "model_type": "chatgpt",
                "enabled": True,
                "weight": 1.5
            }
        }

class RouterStatsResponse(BaseModel):
    """路由器統計回應"""
    
    load_balance_strategy: str = Field(..., description="負載平衡策略")
    health_check_enabled: bool = Field(..., description="是否啟用健康檢查")
    failover_enabled: bool = Field(..., description="是否啟用故障轉移")
    active_models: List[str] = Field(..., description="活躍模型列表")
    total_models: int = Field(..., description="總模型數")
    healthy_models: int = Field(..., description="健康模型數")
    round_robin_index: int = Field(..., description="輪詢索引")
    models: List[ModelInfo] = Field(..., description="詳細模型資訊")

@router.get("/available", response_model=APIResponse[ModelListResponse])
async def get_available_models(
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[ModelListResponse]:
    """
    獲取可用模型列表
    
    返回所有已配置的 AI 模型及其狀態資訊
    """
    try:
        # 獲取模型資訊
        models_data = model_router.get_available_models()
        
        # 轉換為回應格式
        models = [
            ModelInfo(
                model_type=model["model_type"],
                enabled=model["enabled"],
                status=model["status"],
                health=model["health"],
                metrics=model["metrics"],
                config=model["config"]
            )
            for model in models_data
        ]
        
        # 計算統計
        total_count = len(models)
        available_count = sum(1 for m in models if m.enabled and m.status in ["available", "busy"])
        
        # 獲取路由器統計
        router_stats = model_router.get_router_stats()
        
        response_data = ModelListResponse(
            models=models,
            total_count=total_count,
            available_count=available_count,
            load_balance_strategy=router_stats["load_balance_strategy"]
        )
        
        return create_success_response(
            data=response_data,
            message="模型列表獲取成功"
        )
        
    except Exception as e:
        logger.error(f"獲取模型列表失敗: {e}")
        raise HTTPException(status_code=500, detail="獲取模型列表失敗")

@router.post("/select", response_model=APIResponse[ModelSelectionResponse])
async def select_model(
    request: ModelSelectionRequest,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[ModelSelectionResponse]:
    """
    選擇最佳模型
    
    根據當前負載平衡策略和模型狀態選擇最適合的模型
    """
    try:
        start_time = datetime.utcnow()
        
        # 使用路由器選擇模型
        selected_model = model_router.select_model(
            preferred_model=request.preferred_model,
            exclude_models=request.exclude_models
        )
        
        end_time = datetime.utcnow()
        selection_time = (end_time - start_time).total_seconds() * 1000
        
        if not selected_model:
            raise HTTPException(
                status_code=503,
                detail="當前沒有可用的模型"
            )
        
        # 獲取備選模型
        all_active = model_router.active_models
        alternatives = [
            model.value for model in all_active 
            if model != selected_model and model not in (request.exclude_models or [])
        ]
        
        # 生成選擇原因
        router_stats = model_router.get_router_stats()
        strategy = router_stats["load_balance_strategy"]
        
        reason_map = {
            "round_robin": "輪詢策略選擇",
            "weighted_round_robin": "加權輪詢策略選擇",
            "least_connections": "最少連接策略選擇",
            "random": "隨機策略選擇",
            "health_based": "基於健康狀態策略選擇"
        }
        
        reason = reason_map.get(strategy, "路由器策略選擇")
        if request.preferred_model and request.preferred_model == selected_model:
            reason = "用戶偏好模型"
        
        response_data = ModelSelectionResponse(
            selected_model=selected_model.value,
            reason=reason,
            alternatives=alternatives,
            selection_time_ms=selection_time
        )
        
        return create_success_response(
            data=response_data,
            message="模型選擇成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模型選擇失敗: {e}")
        raise HTTPException(status_code=500, detail="模型選擇失敗")

@router.get("/stats", response_model=APIResponse[RouterStatsResponse])
async def get_router_stats(
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[RouterStatsResponse]:
    """
    獲取模型路由器統計資訊
    
    需要管理員權限才能存取詳細統計資訊
    """
    try:
        # 檢查權限（僅管理員可存取詳細統計）
        if not _is_admin(current_employee):
            raise HTTPException(status_code=403, detail="權限不足，需要管理員權限")
        
        # 獲取路由器統計
        stats = model_router.get_router_stats()
        
        # 轉換模型資訊
        models = [
            ModelInfo(
                model_type=model["model_type"],
                enabled=model["enabled"],
                status=model["status"],
                health=model["health"],
                metrics=model["metrics"],
                config=model["config"]
            )
            for model in stats["models"]
        ]
        
        response_data = RouterStatsResponse(
            load_balance_strategy=stats["load_balance_strategy"],
            health_check_enabled=stats["health_check_enabled"],
            failover_enabled=stats["failover_enabled"],
            active_models=stats["active_models"],
            total_models=stats["total_models"],
            healthy_models=stats["healthy_models"],
            round_robin_index=stats["round_robin_index"],
            models=models
        )
        
        return create_success_response(
            data=response_data,
            message="路由器統計獲取成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取路由器統計失敗: {e}")
        raise HTTPException(status_code=500, detail="獲取統計失敗")

@router.put("/config", response_model=APIResponse[Dict[str, str]])
async def update_model_config(
    request: ModelConfigRequest,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[Dict[str, str]]:
    """
    更新模型配置
    
    需要管理員權限才能修改模型配置
    """
    try:
        # 檢查權限
        if not _is_admin(current_employee):
            raise HTTPException(status_code=403, detail="權限不足，需要管理員權限")
        
        # 更新模型配置
        changes = []
        
        if request.enabled is not None:
            if request.enabled:
                model_router.enable_model(request.model_type)
                changes.append(f"啟用模型: {request.model_type.value}")
            else:
                model_router.disable_model(request.model_type)
                changes.append(f"停用模型: {request.model_type.value}")
        
        if request.weight is not None:
            model_router.update_model_weight(request.model_type, request.weight)
            changes.append(f"更新權重: {request.weight}")
        
        if not changes:
            return create_success_response(
                data={"status": "no_changes", "model": request.model_type.value},
                message="沒有配置變更"
            )
        
        return create_success_response(
            data={
                "status": "updated",
                "model": request.model_type.value,
                "changes": ", ".join(changes)
            },
            message="模型配置更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新模型配置失敗: {e}")
        raise HTTPException(status_code=500, detail="更新配置失敗")

@router.get("/health/{model_type}")
async def check_model_health(
    model_type: ModelType,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[Dict[str, Any]]:
    """
    檢查特定模型健康狀態
    
    執行即時健康檢查並返回結果
    """
    try:
        # 獲取模型客戶端
        client = model_router.model_clients.get(model_type)
        if not client:
            raise HTTPException(
                status_code=404,
                detail=f"模型 {model_type.value} 未找到"
            )
        
        # 執行健康檢查
        start_time = datetime.utcnow()
        is_healthy = await client.health_check()
        end_time = datetime.utcnow()
        
        check_time = (end_time - start_time).total_seconds() * 1000
        
        # 獲取模型統計
        stats = client.get_stats()
        
        health_data = {
            "model_type": model_type.value,
            "is_healthy": is_healthy,
            "check_time_ms": check_time,
            "timestamp": end_time.isoformat(),
            "stats": stats
        }
        
        return create_success_response(
            data=health_data,
            message=f"模型 {model_type.value} 健康檢查完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模型健康檢查失敗: {e}")
        raise HTTPException(status_code=500, detail="健康檢查失敗")

@router.post("/strategy")
async def update_load_balance_strategy(
    strategy: LoadBalanceStrategy,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[Dict[str, str]]:
    """
    更新負載平衡策略
    
    需要管理員權限才能修改負載平衡策略
    """
    try:
        # 檢查權限
        if not _is_admin(current_employee):
            raise HTTPException(status_code=403, detail="權限不足，需要管理員權限")
        
        # 更新策略
        old_strategy = model_router.load_balance_strategy
        model_router.load_balance_strategy = strategy
        
        logger.info(f"負載平衡策略已更新: {old_strategy.value} -> {strategy.value}")
        
        return create_success_response(
            data={
                "old_strategy": old_strategy.value,
                "new_strategy": strategy.value,
                "updated_by": current_employee.email
            },
            message="負載平衡策略更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新負載平衡策略失敗: {e}")
        raise HTTPException(status_code=500, detail="策略更新失敗")

@router.get("/health")
async def models_health_check() -> APIResponse[Dict[str, Any]]:
    """
    模型服務整體健康檢查
    """
    try:
        # 獲取路由器統計
        stats = model_router.get_router_stats()
        
        # 計算健康狀態
        total_models = stats["total_models"]
        healthy_models = stats["healthy_models"]
        active_models = len(stats["active_models"])
        
        overall_health = "healthy"
        if healthy_models == 0:
            overall_health = "critical"
        elif healthy_models < total_models * 0.5:
            overall_health = "degraded"
        
        health_data = {
            "overall_health": overall_health,
            "total_models": total_models,
            "healthy_models": healthy_models,
            "active_models": active_models,
            "load_balance_strategy": stats["load_balance_strategy"],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return create_success_response(
            data=health_data,
            message="模型服務健康檢查完成"
        )
        
    except Exception as e:
        logger.error(f"模型服務健康檢查失敗: {e}")
        raise HTTPException(status_code=500, detail="健康檢查失敗")

def _is_admin(employee: Employee) -> bool:
    """檢查是否為管理員"""
    import os
    
    admin_emails = [
        email.strip().lower() 
        for email in os.getenv("ADMIN_EMAILS", "").split(",")
        if email.strip()
    ]
    
    return employee.email.lower() in admin_emails