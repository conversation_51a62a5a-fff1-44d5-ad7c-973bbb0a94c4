"""
AI 模型資料模型
定義 AI 模型的資料結構和配置管理
"""

import json
import uuid
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from enum import Enum

class ModelType(str, Enum):
    """AI 模型類型列舉"""
    CHATGPT = "chatgpt"
    CLAUDE = "claude"
    GEMINI = "gemini"

class ModelStatus(str, Enum):
    """模型狀態列舉"""
    AVAILABLE = "available"
    BUSY = "busy"
    MAINTENANCE = "maintenance"
    ERROR = "error"
    DISABLED = "disabled"

class LoadBalanceStrategy(str, Enum):
    """負載平衡策略列舉"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    RANDOM = "random"
    HEALTH_BASED = "health_based"

class ModelCapability(str, Enum):
    """模型能力列舉"""
    TEXT_GENERATION = "text_generation"
    CONVERSATION = "conversation"
    TRANSLATION = "translation"
    SUMMARIZATION = "summarization"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    CODE_GENERATION = "code_generation"
    IMAGE_ANALYSIS = "image_analysis"
    EMBEDDING = "embedding"
    MODERATION = "moderation"

class ModelConfigBase(BaseModel):
    """模型配置基礎類"""
    
    model_type: ModelType = Field(..., description="模型類型")
    model_name: str = Field(..., description="模型名稱")
    display_name: str = Field(..., description="顯示名稱")
    description: Optional[str] = Field(default=None, description="模型描述")
    
    # 基本配置
    max_tokens: int = Field(default=2000, ge=1, le=32000, description="最大 token 數")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="溫度參數")
    top_p: float = Field(default=1.0, ge=0.0, le=1.0, description="Top-p 參數")
    
    # 性能配置
    timeout: int = Field(default=30, ge=1, le=300, description="請求超時（秒）")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重試次數")
    retry_delay: float = Field(default=1.0, ge=0.1, le=60.0, description="重試延遲（秒）")
    
    # 能力配置
    capabilities: List[ModelCapability] = Field(default=[], description="模型能力列表")
    
    # 限制配置
    max_requests_per_minute: int = Field(default=60, ge=1, description="每分鐘最大請求數")
    max_tokens_per_minute: int = Field(default=40000, ge=100, description="每分鐘最大 token 數")
    
    @validator('capabilities')
    def validate_capabilities(cls, v):
        """驗證模型能力"""
        if not v:
            # 預設所有模型都有基本對話能力
            return [ModelCapability.TEXT_GENERATION, ModelCapability.CONVERSATION]
        return v

class ModelConfig(ModelConfigBase):
    """完整模型配置"""
    
    config_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="配置 ID")
    
    # 狀態資訊
    status: ModelStatus = Field(default=ModelStatus.AVAILABLE, description="模型狀態")
    enabled: bool = Field(default=True, description="是否啟用")
    
    # 負載平衡
    weight: float = Field(default=1.0, ge=0.0, le=10.0, description="負載平衡權重")
    priority: int = Field(default=0, ge=0, le=100, description="優先級")
    
    # 成本配置
    cost_per_1k_input_tokens: float = Field(default=0.0, ge=0.0, description="每 1K 輸入 token 成本")
    cost_per_1k_output_tokens: float = Field(default=0.0, ge=0.0, description="每 1K 輸出 token 成本")
    
    # 版本資訊
    version: str = Field(default="1.0.0", description="模型版本")
    provider: str = Field(..., description="模型提供商")
    api_endpoint: Optional[str] = Field(default=None, description="API 端點")
    
    # 時間戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="建立時間")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新時間")
    
    # 額外配置
    extra_config: Dict[str, Any] = Field(default_factory=dict, description="額外配置")
    
    def update_status(self, status: ModelStatus, reason: Optional[str] = None) -> None:
        """更新模型狀態"""
        self.status = status
        self.updated_at = datetime.utcnow()
        if reason:
            self.extra_config["status_reason"] = reason
            self.extra_config["status_updated_at"] = datetime.utcnow().isoformat()
    
    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """計算成本"""
        input_cost = (input_tokens / 1000) * self.cost_per_1k_input_tokens
        output_cost = (output_tokens / 1000) * self.cost_per_1k_output_tokens
        return input_cost + output_cost
    
    def has_capability(self, capability: ModelCapability) -> bool:
        """檢查是否具有特定能力"""
        return capability in self.capabilities
    
    def to_redis_dict(self) -> Dict[str, Any]:
        """轉換為 Redis 儲存格式"""
        return {
            "config_id": self.config_id,
            "model_type": self.model_type.value,
            "model_name": self.model_name,
            "display_name": self.display_name,
            "description": self.description,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "capabilities": [cap.value for cap in self.capabilities],
            "max_requests_per_minute": self.max_requests_per_minute,
            "max_tokens_per_minute": self.max_tokens_per_minute,
            "status": self.status.value,
            "enabled": self.enabled,
            "weight": self.weight,
            "priority": self.priority,
            "cost_per_1k_input_tokens": self.cost_per_1k_input_tokens,
            "cost_per_1k_output_tokens": self.cost_per_1k_output_tokens,
            "version": self.version,
            "provider": self.provider,
            "api_endpoint": self.api_endpoint,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "extra_config": self.extra_config
        }
    
    @classmethod
    def from_redis_dict(cls, data: Dict[str, Any]) -> "ModelConfig":
        """從 Redis 資料建立模型配置物件"""
        # 處理時間戳
        for time_field in ["created_at", "updated_at"]:
            if isinstance(data.get(time_field), str):
                data[time_field] = datetime.fromisoformat(data[time_field])
        
        # 處理列舉類型
        if isinstance(data.get("model_type"), str):
            data["model_type"] = ModelType(data["model_type"])
        
        if isinstance(data.get("status"), str):
            data["status"] = ModelStatus(data["status"])
        
        if isinstance(data.get("capabilities"), list):
            data["capabilities"] = [ModelCapability(cap) for cap in data["capabilities"]]
        
        return cls(**data)

class ModelUsageStats(BaseModel):
    """模型使用統計"""
    
    model_type: ModelType = Field(..., description="模型類型")
    model_name: str = Field(..., description="模型名稱")
    
    # 請求統計
    total_requests: int = Field(default=0, description="總請求數")
    successful_requests: int = Field(default=0, description="成功請求數")
    failed_requests: int = Field(default=0, description="失敗請求數")
    
    # Token 統計
    total_input_tokens: int = Field(default=0, description="總輸入 token 數")
    total_output_tokens: int = Field(default=0, description="總輸出 token 數")
    
    # 成本統計
    total_cost: float = Field(default=0.0, description="總成本")
    
    # 時間統計
    total_response_time: float = Field(default=0.0, description="總回應時間（毫秒）")
    min_response_time: float = Field(default=0.0, description="最小回應時間（毫秒）")
    max_response_time: float = Field(default=0.0, description="最大回應時間（毫秒）")
    
    # 時間範圍
    start_time: datetime = Field(default_factory=datetime.utcnow, description="統計開始時間")
    last_request_time: Optional[datetime] = Field(default=None, description="最後請求時間")
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def failure_rate(self) -> float:
        """失敗率"""
        return 1.0 - self.success_rate
    
    @property
    def average_response_time(self) -> float:
        """平均回應時間"""
        if self.total_requests == 0:
            return 0.0
        return self.total_response_time / self.total_requests
    
    @property
    def total_tokens(self) -> int:
        """總 token 數"""
        return self.total_input_tokens + self.total_output_tokens
    
    @property
    def average_input_tokens(self) -> float:
        """平均輸入 token 數"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_input_tokens / self.successful_requests
    
    @property
    def average_output_tokens(self) -> float:
        """平均輸出 token 數"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_output_tokens / self.successful_requests
    
    @property
    def average_cost_per_request(self) -> float:
        """平均每請求成本"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_cost / self.successful_requests
    
    def update_request_stats(
        self,
        success: bool,
        input_tokens: int = 0,
        output_tokens: int = 0,
        response_time: float = 0.0,
        cost: float = 0.0
    ) -> None:
        """更新請求統計"""
        self.total_requests += 1
        self.last_request_time = datetime.utcnow()
        
        if success:
            self.successful_requests += 1
            self.total_input_tokens += input_tokens
            self.total_output_tokens += output_tokens
            self.total_cost += cost
            
            # 更新回應時間統計
            self.total_response_time += response_time
            if self.min_response_time == 0.0 or response_time < self.min_response_time:
                self.min_response_time = response_time
            if response_time > self.max_response_time:
                self.max_response_time = response_time
        else:
            self.failed_requests += 1
    
    def reset_stats(self) -> None:
        """重置統計資料"""
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.total_response_time = 0.0
        self.min_response_time = 0.0
        self.max_response_time = 0.0
        self.start_time = datetime.utcnow()
        self.last_request_time = None

class ModelHealthCheck(BaseModel):
    """模型健康檢查"""
    
    model_type: ModelType = Field(..., description="模型類型")
    model_name: str = Field(..., description="模型名稱")
    
    # 健康狀態
    is_healthy: bool = Field(default=False, description="是否健康")
    status: ModelStatus = Field(default=ModelStatus.ERROR, description="健康狀態")
    
    # 檢查結果
    response_time: float = Field(default=0.0, description="回應時間（毫秒）")
    error_message: Optional[str] = Field(default=None, description="錯誤訊息")
    
    # 時間戳
    check_time: datetime = Field(default_factory=datetime.utcnow, description="檢查時間")
    
    # 健康檢查配置
    timeout: int = Field(default=10, description="健康檢查超時（秒）")
    test_prompt: str = Field(default="Hello", description="測試提示")
    
    def update_health(
        self,
        is_healthy: bool,
        response_time: float = 0.0,
        error_message: Optional[str] = None
    ) -> None:
        """更新健康狀態"""
        self.is_healthy = is_healthy
        self.response_time = response_time
        self.error_message = error_message
        self.check_time = datetime.utcnow()
        
        if is_healthy:
            if response_time < 5000:  # 5 秒內回應
                self.status = ModelStatus.AVAILABLE
            else:
                self.status = ModelStatus.BUSY
        else:
            self.status = ModelStatus.ERROR

class ModelRequest(BaseModel):
    """模型請求"""
    
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="請求 ID")
    session_id: str = Field(..., description="會話 ID")
    user_id: str = Field(..., description="用戶 ID")
    
    # 模型配置
    model_type: Optional[ModelType] = Field(default=None, description="指定模型類型")
    model_name: Optional[str] = Field(default=None, description="指定模型名稱")
    
    # 請求內容
    messages: List[Dict[str, str]] = Field(..., description="對話訊息")
    
    # 生成參數
    max_tokens: Optional[int] = Field(default=None, description="最大 token 數")
    temperature: Optional[float] = Field(default=None, description="溫度參數")
    top_p: Optional[float] = Field(default=None, description="Top-p 參數")
    stream: bool = Field(default=False, description="是否串流回應")
    
    # 請求配置
    timeout: Optional[int] = Field(default=None, description="請求超時")
    max_retries: Optional[int] = Field(default=None, description="最大重試次數")
    
    # 額外資料
    metadata: Dict[str, Any] = Field(default_factory=dict, description="附加資料")
    
    # 時間戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="建立時間")

class ModelResponse(BaseModel):
    """模型回應"""
    
    request_id: str = Field(..., description="請求 ID")
    response_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="回應 ID")
    
    # 模型資訊
    model_type: ModelType = Field(..., description="使用的模型類型")
    model_name: str = Field(..., description="使用的模型名稱")
    
    # 回應內容
    content: str = Field(..., description="回應內容")
    finish_reason: str = Field(..., description="完成原因")
    
    # 使用統計
    usage: Dict[str, int] = Field(..., description="Token 使用統計")
    response_time: float = Field(..., description="回應時間（毫秒）")
    cost: float = Field(default=0.0, description="成本")
    
    # 狀態資訊
    success: bool = Field(default=True, description="是否成功")
    error_message: Optional[str] = Field(default=None, description="錯誤訊息")
    
    # 額外資料
    metadata: Dict[str, Any] = Field(default_factory=dict, description="附加資料")
    
    # 時間戳
    created_at: datetime = Field(default_factory=datetime.utcnow, description="建立時間")
    
    @classmethod
    def from_error(
        cls,
        request_id: str,
        model_type: ModelType,
        model_name: str,
        error_message: str,
        response_time: float = 0.0
    ) -> "ModelResponse":
        """從錯誤建立回應"""
        return cls(
            request_id=request_id,
            model_type=model_type,
            model_name=model_name,
            content="",
            finish_reason="error",
            usage={"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0},
            response_time=response_time,
            success=False,
            error_message=error_message
        )

class ModelSelectionCriteria(BaseModel):
    """模型選擇條件"""
    
    # 基本條件
    required_capabilities: List[ModelCapability] = Field(default=[], description="必需能力")
    preferred_model_types: List[ModelType] = Field(default=[], description="偏好模型類型")
    excluded_model_types: List[ModelType] = Field(default=[], description="排除模型類型")
    
    # 性能條件
    max_response_time: Optional[float] = Field(default=None, description="最大回應時間（毫秒）")
    min_success_rate: Optional[float] = Field(default=None, description="最小成功率")
    
    # 成本條件
    max_cost_per_request: Optional[float] = Field(default=None, description="最大每請求成本")
    
    # 負載條件
    max_concurrent_requests: Optional[int] = Field(default=None, description="最大並發請求數")
    
    # 策略配置
    load_balance_strategy: LoadBalanceStrategy = Field(default=LoadBalanceStrategy.HEALTH_BASED, description="負載平衡策略")
    fallback_enabled: bool = Field(default=True, description="是否啟用備援")

# 預定義模型配置
DEFAULT_MODEL_CONFIGS = {
    ModelType.CHATGPT: ModelConfig(
        model_type=ModelType.CHATGPT,
        model_name="gpt-4",
        display_name="ChatGPT (GPT-4)",
        description="OpenAI GPT-4 模型，具有強大的理解和生成能力",
        max_tokens=2000,
        temperature=0.7,
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CONVERSATION,
            ModelCapability.TRANSLATION,
            ModelCapability.SUMMARIZATION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.EMBEDDING,
            ModelCapability.MODERATION
        ],
        cost_per_1k_input_tokens=0.03,
        cost_per_1k_output_tokens=0.06,
        provider="OpenAI",
        version="gpt-4"
    ),
    
    ModelType.CLAUDE: ModelConfig(
        model_type=ModelType.CLAUDE,
        model_name="claude-3-sonnet-20240229",
        display_name="Claude 3 Sonnet",
        description="Anthropic Claude 3 Sonnet 模型，平衡性能和成本",
        max_tokens=2000,
        temperature=0.7,
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CONVERSATION,
            ModelCapability.TRANSLATION,
            ModelCapability.SUMMARIZATION,
            ModelCapability.SENTIMENT_ANALYSIS
        ],
        cost_per_1k_input_tokens=0.003,
        cost_per_1k_output_tokens=0.015,
        provider="Anthropic",
        version="claude-3-sonnet-20240229"
    ),
    
    ModelType.GEMINI: ModelConfig(
        model_type=ModelType.GEMINI,
        model_name="gemini-pro",
        display_name="Gemini Pro",
        description="Google Gemini Pro 模型，支援多模態功能",
        max_tokens=2000,
        temperature=0.7,
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CONVERSATION,
            ModelCapability.TRANSLATION,
            ModelCapability.IMAGE_ANALYSIS
        ],
        cost_per_1k_input_tokens=0.0005,
        cost_per_1k_output_tokens=0.0015,
        provider="Google",
        version="gemini-pro"
    )
}