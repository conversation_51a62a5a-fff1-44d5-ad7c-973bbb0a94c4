"""
Redis 資料服務模組
處理會話管理、聊天記錄和快取功能
"""

import os
import json
import logging
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta
import redis.asyncio as redis
from redis.asyncio import Redis
from fastapi import HTTPException, status
import uuid
import hashlib

# 設定日誌
logger = logging.getLogger(__name__)

class RedisService:
    """Redis 資料服務類別"""
    
    def __init__(self):
        # Redis 連線配置
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_db = int(os.getenv("REDIS_DB", "0"))
        self.redis_password = os.getenv("REDIS_PASSWORD")
        
        # 連線池配置
        self.max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
        self.retry_on_timeout = True
        self.health_check_interval = 30
        
        # 建立連線池
        self._redis_pool: Optional[Redis] = None
        
        # 快取設定
        self.default_ttl = int(os.getenv("REDIS_DEFAULT_TTL", "3600"))  # 1 hour
        self.session_ttl = int(os.getenv("REDIS_SESSION_TTL", "1800"))  # 30 minutes
        self.chat_history_ttl = int(os.getenv("REDIS_CHAT_HISTORY_TTL", "604800"))  # 7 days
        
        # Key 前綴
        self.session_prefix = "session:"
        self.user_prefix = "user:"
        self.chat_prefix = "chat:"
        self.employee_prefix = "employee:"
        self.cache_prefix = "cache:"
    
    async def initialize(self) -> None:
        """初始化 Redis 連線"""
        try:
            self._redis_pool = redis.from_url(
                self.redis_url,
                db=self.redis_db,
                password=self.redis_password,
                max_connections=self.max_connections,
                retry_on_timeout=self.retry_on_timeout,
                health_check_interval=self.health_check_interval,
                decode_responses=True,
                encoding='utf-8'
            )
            
            # 測試連線
            await self._redis_pool.ping()
            logger.info("Redis 連線建立成功")
            
        except Exception as e:
            logger.error(f"Redis 連線失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="資料庫服務暫時無法使用"
            )
    
    async def close(self) -> None:
        """關閉 Redis 連線"""
        if self._redis_pool:
            await self._redis_pool.close()
            logger.info("Redis 連線已關閉")
    
    @property
    def redis(self) -> Redis:
        """獲取 Redis 連線"""
        if not self._redis_pool:
            raise RuntimeError("Redis 尚未初始化")
        return self._redis_pool
    
    # === 會話管理 ===
    
    async def create_session(
        self, 
        user_id: str, 
        employee_id: str, 
        session_data: Dict[str, Any],
        ttl_seconds: Optional[int] = None
    ) -> str:
        """建立新會話"""
        session_id = str(uuid.uuid4())
        session_key = f"{self.session_prefix}{session_id}"
        
        # 準備會話資料
        session_info = {
            "session_id": session_id,
            "user_id": user_id,
            "employee_id": employee_id,
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(seconds=ttl_seconds or self.session_ttl)).isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "is_active": True,
            **session_data
        }
        
        try:
            # 儲存會話資料
            await self.redis.setex(
                session_key,
                ttl_seconds or self.session_ttl,
                json.dumps(session_info, ensure_ascii=False)
            )
            
            # 建立用戶到會話的索引
            user_sessions_key = f"{self.user_prefix}{user_id}:sessions"
            await self.redis.sadd(user_sessions_key, session_id)
            await self.redis.expire(user_sessions_key, ttl_seconds or self.session_ttl)
            
            logger.info(f"會話建立成功: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"建立會話失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="會話建立失敗"
            )
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """獲取會話資訊"""
        session_key = f"{self.session_prefix}{session_id}"
        
        try:
            session_data = await self.redis.get(session_key)
            
            if not session_data:
                return None
            
            session_info = json.loads(session_data)
            
            # 檢查會話是否過期
            expires_at = datetime.fromisoformat(session_info["expires_at"])
            if datetime.utcnow() > expires_at:
                await self.delete_session(session_id)
                return None
            
            return session_info
            
        except Exception as e:
            logger.error(f"獲取會話失敗: {session_id} - {e}")
            return None
    
    async def update_session_activity(self, session_id: str) -> bool:
        """更新會話活動時間"""
        session_key = f"{self.session_prefix}{session_id}"
        
        try:
            session_data = await self.redis.get(session_key)
            
            if not session_data:
                return False
            
            session_info = json.loads(session_data)
            session_info["last_activity"] = datetime.utcnow().isoformat()
            
            # 更新會話資料
            await self.redis.setex(
                session_key,
                self.session_ttl,
                json.dumps(session_info, ensure_ascii=False)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"更新會話活動失敗: {session_id} - {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """刪除會話"""
        session_key = f"{self.session_prefix}{session_id}"
        
        try:
            # 獲取會話資訊以取得 user_id
            session_data = await self.redis.get(session_key)
            if session_data:
                session_info = json.loads(session_data)
                user_id = session_info.get("user_id")
                
                # 從用戶會話索引中移除
                if user_id:
                    user_sessions_key = f"{self.user_prefix}{user_id}:sessions"
                    await self.redis.srem(user_sessions_key, session_id)
            
            # 刪除會話
            result = await self.redis.delete(session_key)
            
            if result:
                logger.info(f"會話刪除成功: {session_id}")
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"刪除會話失敗: {session_id} - {e}")
            return False
    
    async def get_user_sessions(self, user_id: str) -> List[str]:
        """獲取用戶所有會話"""
        user_sessions_key = f"{self.user_prefix}{user_id}:sessions"
        
        try:
            session_ids = await self.redis.smembers(user_sessions_key)
            return list(session_ids)
            
        except Exception as e:
            logger.error(f"獲取用戶會話失敗: {user_id} - {e}")
            return []
    
    async def delete_user_sessions(self, user_id: str) -> int:
        """刪除用戶所有會話"""
        session_ids = await self.get_user_sessions(user_id)
        deleted_count = 0
        
        for session_id in session_ids:
            if await self.delete_session(session_id):
                deleted_count += 1
        
        # 清除用戶會話索引
        user_sessions_key = f"{self.user_prefix}{user_id}:sessions"
        await self.redis.delete(user_sessions_key)
        
        logger.info(f"用戶會話清除完成: {user_id}, 刪除數量: {deleted_count}")
        return deleted_count
    
    # === 聊天記錄管理 ===
    
    async def save_chat_message(
        self, 
        session_id: str, 
        message_data: Dict[str, Any]
    ) -> str:
        """儲存聊天訊息"""
        message_id = str(uuid.uuid4())
        chat_key = f"{self.chat_prefix}{session_id}"
        
        # 準備訊息資料
        message_info = {
            "message_id": message_id,
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat(),
            **message_data
        }
        
        try:
            # 將訊息加入列表
            await self.redis.lpush(
                chat_key,
                json.dumps(message_info, ensure_ascii=False)
            )
            
            # 設定過期時間
            await self.redis.expire(chat_key, self.chat_history_ttl)
            
            # 限制聊天記錄數量（保留最新 1000 條）
            await self.redis.ltrim(chat_key, 0, 999)
            
            logger.debug(f"聊天訊息儲存成功: {message_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"儲存聊天訊息失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="訊息儲存失敗"
            )
    
    async def get_chat_history(
        self, 
        session_id: str, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """獲取聊天記錄"""
        chat_key = f"{self.chat_prefix}{session_id}"
        
        try:
            # 獲取指定範圍的訊息
            messages = await self.redis.lrange(
                chat_key, 
                offset, 
                offset + limit - 1
            )
            
            # 解析訊息資料
            chat_history = []
            for message_json in messages:
                try:
                    message_data = json.loads(message_json)
                    chat_history.append(message_data)
                except json.JSONDecodeError as e:
                    logger.warning(f"解析聊天訊息失敗: {e}")
                    continue
            
            # 按時間順序排序（最新的在前）
            chat_history.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            return chat_history
            
        except Exception as e:
            logger.error(f"獲取聊天記錄失敗: {session_id} - {e}")
            return []
    
    async def delete_chat_history(self, session_id: str) -> bool:
        """刪除聊天記錄"""
        chat_key = f"{self.chat_prefix}{session_id}"
        
        try:
            result = await self.redis.delete(chat_key)
            
            if result:
                logger.info(f"聊天記錄刪除成功: {session_id}")
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"刪除聊天記錄失敗: {session_id} - {e}")
            return False
    
    # === 員工資料快取 ===
    
    async def cache_employee_data(
        self, 
        employee_id: str, 
        employee_data: Dict[str, Any],
        ttl_seconds: Optional[int] = None
    ) -> bool:
        """快取員工資料"""
        employee_key = f"{self.employee_prefix}{employee_id}"
        
        try:
            # 加入快取時間戳
            cache_data = {
                **employee_data,
                "cached_at": datetime.utcnow().isoformat(),
                "cache_expires_at": (datetime.utcnow() + timedelta(seconds=ttl_seconds or self.default_ttl)).isoformat()
            }
            
            await self.redis.setex(
                employee_key,
                ttl_seconds or self.default_ttl,
                json.dumps(cache_data, ensure_ascii=False)
            )
            
            logger.debug(f"員工資料快取成功: {employee_id}")
            return True
            
        except Exception as e:
            logger.error(f"快取員工資料失敗: {employee_id} - {e}")
            return False
    
    async def get_cached_employee_data(self, employee_id: str) -> Optional[Dict[str, Any]]:
        """獲取快取的員工資料"""
        employee_key = f"{self.employee_prefix}{employee_id}"
        
        try:
            cache_data = await self.redis.get(employee_key)
            
            if not cache_data:
                return None
            
            employee_data = json.loads(cache_data)
            
            # 檢查快取是否過期
            if "cache_expires_at" in employee_data:
                expires_at = datetime.fromisoformat(employee_data["cache_expires_at"])
                if datetime.utcnow() > expires_at:
                    await self.redis.delete(employee_key)
                    return None
            
            return employee_data
            
        except Exception as e:
            logger.error(f"獲取快取員工資料失敗: {employee_id} - {e}")
            return None
    
    async def delete_cached_employee_data(self, employee_id: str) -> bool:
        """刪除快取的員工資料"""
        employee_key = f"{self.employee_prefix}{employee_id}"
        
        try:
            result = await self.redis.delete(employee_key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"刪除快取員工資料失敗: {employee_id} - {e}")
            return False
    
    # === 通用快取操作 ===
    
    async def set_cache(
        self, 
        key: str, 
        value: Any, 
        ttl_seconds: Optional[int] = None
    ) -> bool:
        """設定快取"""
        cache_key = f"{self.cache_prefix}{key}"
        
        try:
            # 序列化值
            if isinstance(value, (dict, list)):
                cache_value = json.dumps(value, ensure_ascii=False)
            else:
                cache_value = str(value)
            
            if ttl_seconds:
                await self.redis.setex(cache_key, ttl_seconds, cache_value)
            else:
                await self.redis.set(cache_key, cache_value)
            
            return True
            
        except Exception as e:
            logger.error(f"設定快取失敗: {key} - {e}")
            return False
    
    async def get_cache(self, key: str) -> Optional[Any]:
        """獲取快取"""
        cache_key = f"{self.cache_prefix}{key}"
        
        try:
            value = await self.redis.get(cache_key)
            
            if value is None:
                return None
            
            # 嘗試解析 JSON
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
            
        except Exception as e:
            logger.error(f"獲取快取失敗: {key} - {e}")
            return None
    
    async def delete_cache(self, key: str) -> bool:
        """刪除快取"""
        cache_key = f"{self.cache_prefix}{key}"
        
        try:
            result = await self.redis.delete(cache_key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"刪除快取失敗: {key} - {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """檢查 key 是否存在"""
        try:
            result = await self.redis.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"檢查 key 存在失敗: {key} - {e}")
            return False
    
    # === 統計和監控 ===
    
    async def get_stats(self) -> Dict[str, Any]:
        """獲取 Redis 統計資訊"""
        try:
            info = await self.redis.info()
            
            # 獲取自定義統計
            session_count = len(await self.redis.keys(f"{self.session_prefix}*"))
            cache_count = len(await self.redis.keys(f"{self.cache_prefix}*"))
            employee_cache_count = len(await self.redis.keys(f"{self.employee_prefix}*"))
            chat_count = len(await self.redis.keys(f"{self.chat_prefix}*"))
            
            return {
                "redis_info": {
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory": info.get("used_memory_human", "0B"),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0)
                },
                "application_stats": {
                    "active_sessions": session_count,
                    "cached_items": cache_count,
                    "cached_employees": employee_cache_count,
                    "chat_sessions": chat_count
                }
            }
            
        except Exception as e:
            logger.error(f"獲取 Redis 統計失敗: {e}")
            return {}
    
    async def health_check(self) -> bool:
        """健康檢查"""
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis 健康檢查失敗: {e}")
            return False
    
    # === 清理操作 ===
    
    async def cleanup_expired_sessions(self) -> int:
        """清理過期會話"""
        try:
            session_keys = await self.redis.keys(f"{self.session_prefix}*")
            cleaned_count = 0
            
            for session_key in session_keys:
                session_data = await self.redis.get(session_key)
                if session_data:
                    try:
                        session_info = json.loads(session_data)
                        expires_at = datetime.fromisoformat(session_info["expires_at"])
                        
                        if datetime.utcnow() > expires_at:
                            session_id = session_info["session_id"]
                            if await self.delete_session(session_id):
                                cleaned_count += 1
                    except (json.JSONDecodeError, KeyError, ValueError):
                        # 無效的會話資料，直接刪除
                        await self.redis.delete(session_key)
                        cleaned_count += 1
            
            logger.info(f"過期會話清理完成，清理數量: {cleaned_count}")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理過期會話失敗: {e}")
            return 0

# 建立全域服務實例
redis_service = RedisService()