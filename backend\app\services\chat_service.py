"""
聊天服務實作
整合所有服務提供統一的聊天邏輯，包含訊息處理、模型路由、結果格式化
"""

import os
import logging
import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
import uuid

from ..models.message import Message, MessageRole, MessageStatus, MessageCreateRequest, MessageResponse
from ..models.session import Session, SessionStatus
from ..models.model import ModelType, ModelRequest, ModelResponse
from ..services.redis_client import get_redis_client
from ..services.session_service import get_session_service
from ..services.model_router import model_router
from .bpm_filter import bmp_filter
from ..clients.openai_client import get_openai_client
from ..clients.claude_client import get_claude_client
from ..clients.gemini_client import get_gemini_client

# 設定日誌
logger = logging.getLogger(__name__)

class ChatServiceError(Exception):
    """聊天服務錯誤"""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.error_code = error_code

class ChatService:
    """聊天服務類"""
    
    def __init__(self):
        # 初始化服務依賴
        self.redis_client = get_redis_client()
        self.session_service = get_session_service()
        
        # 註冊 AI 模型客戶端到路由器
        model_router.register_client(ModelType.CHATGPT, get_openai_client())
        model_router.register_client(ModelType.CLAUDE, get_claude_client())
        model_router.register_client(ModelType.GEMINI, get_gemini_client())
        
        # 聊天配置
        self.max_message_length = int(os.getenv("CHAT_MAX_MESSAGE_LENGTH", "10000"))
        self.max_messages_per_session = int(os.getenv("CHAT_MAX_MESSAGES_PER_SESSION", "1000"))
        self.enable_bmp_filter = os.getenv("CHAT_ENABLE_BMP_FILTER", "true").lower() == "true"
        self.default_model = ModelType(os.getenv("CHAT_DEFAULT_MODEL", "chatgpt"))
        
        # 統計資料
        self.stats = {
            "total_messages": 0,
            "successful_messages": 0,
            "failed_messages": 0,
            "filtered_messages": 0,
            "total_sessions": 0,
            "active_sessions": 0
        }
        
        logger.info("聊天服務初始化完成")
    
    async def start_services(self):
        """啟動相關服務"""
        try:
            # 啟動模型健康檢查
            await model_router.start_health_checks()
            logger.info("聊天服務已啟動")
        except Exception as e:
            logger.error(f"聊天服務啟動失敗: {e}")
            raise ChatServiceError(f"服務啟動失敗: {e}", "service_start_failed")
    
    async def stop_services(self):
        """停止相關服務"""
        try:
            # 停止模型健康檢查
            await model_router.stop_health_checks()
            logger.info("聊天服務已停止")
        except Exception as e:
            logger.error(f"聊天服務停止失敗: {e}")
    
    async def send_message(
        self,
        user_id: str,
        employee_id: str,
        content: str,
        session_id: Optional[str] = None,
        preferred_model: Optional[ModelType] = None,
        stream: bool = False,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        發送聊天訊息
        
        Args:
            user_id: 用戶 ID
            employee_id: 員工 ID  
            content: 訊息內容
            session_id: 會話 ID（可選，會自動建立）
            preferred_model: 偏好的 AI 模型
            stream: 是否串流回應
            metadata: 附加資料
        
        Returns:
            聊天回應結果
        """
        start_time = datetime.utcnow()
        request_id = str(uuid.uuid4())
        
        try:
            # 更新統計
            self.stats["total_messages"] += 1
            
            # 1. 驗證輸入
            await self._validate_message_input(content, user_id, employee_id)
            
            # 2. 取得或建立會話
            session = await self._get_or_create_session(user_id, employee_id, session_id)
            
            # 3. 建立用戶訊息
            user_message = await self._create_user_message(
                user_id, session.session_id, content, metadata or {}
            )
            
            # 4. BPM 內容過濾
            if self.enable_bmp_filter:
                filter_result = await self._filter_bmp_content(content, user_id)
                user_message.filter_result = filter_result
                
                if filter_result["result"] == "rejected":
                    self.stats["filtered_messages"] += 1
                    return await self._create_rejection_response(
                        request_id, user_message, filter_result, start_time
                    )
            
            # 5. 選擇 AI 模型
            selected_model = self._select_ai_model(preferred_model, session)
            
            # 6. 準備對話歷史
            conversation_history = await self._prepare_conversation_history(session.session_id)
            
            # 7. 生成 AI 回應
            if stream:
                return await self._generate_streaming_response(
                    request_id, user_message, selected_model, conversation_history, start_time
                )
            else:
                return await self._generate_response(
                    request_id, user_message, selected_model, conversation_history, start_time
                )
                
        except ChatServiceError:
            raise
        except Exception as e:
            self.stats["failed_messages"] += 1
            logger.error(f"發送訊息失敗: {e}")
            raise ChatServiceError(f"訊息處理失敗: {e}", "message_processing_failed")
    
    async def get_chat_history(
        self,
        user_id: str,
        session_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        獲取聊天歷史
        
        Args:
            user_id: 用戶 ID
            session_id: 會話 ID
            limit: 限制數量
            offset: 偏移量
        
        Returns:
            聊天歷史資料
        """
        try:
            # 驗證會話權限
            session = await self.session_service.get_session(session_id)
            if not session or session.user_id != user_id:
                raise ChatServiceError("會話不存在或無權限存取", "session_access_denied")
            
            # 獲取訊息歷史
            messages_key = f"messages:{session_id}"
            message_data_list = await self.redis_client.get_list_range(
                messages_key, offset, offset + limit - 1
            )
            
            messages = []
            for message_data in message_data_list:
                try:
                    message = Message.from_json(message_data)
                    messages.append(MessageResponse.from_message(message))
                except Exception as e:
                    logger.warning(f"解析訊息失敗: {e}")
                    continue
            
            # 獲取總數
            total_count = await self.redis_client.get_list_length(messages_key)
            
            return {
                "session_id": session_id,
                "messages": messages,
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            }
            
        except ChatServiceError:
            raise
        except Exception as e:
            logger.error(f"獲取聊天歷史失敗: {e}")
            raise ChatServiceError(f"獲取歷史失敗: {e}", "history_fetch_failed")
    
    async def get_session_info(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """
        獲取會話資訊
        
        Args:
            user_id: 用戶 ID
            session_id: 會話 ID
        
        Returns:
            會話資訊
        """
        try:
            session = await self.session_service.get_session(session_id)
            if not session or session.user_id != user_id:
                raise ChatServiceError("會話不存在或無權限存取", "session_access_denied")
            
            # 獲取會話統計
            messages_key = f"messages:{session_id}"
            message_count = await self.redis_client.get_list_length(messages_key)
            
            return {
                "session_id": session.session_id,
                "user_id": session.user_id,
                "employee_id": session.employee_id,
                "status": session.status.value,
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
                "message_count": message_count,
                "preferred_model": session.preferred_model,
                "remaining_seconds": session.remaining_seconds,
                "tags": session.tags,
                "metadata": session.metadata
            }
            
        except ChatServiceError:
            raise
        except Exception as e:
            logger.error(f"獲取會話資訊失敗: {e}")
            raise ChatServiceError(f"獲取會話資訊失敗: {e}", "session_info_failed")
    
    async def _validate_message_input(self, content: str, user_id: str, employee_id: str):
        """驗證訊息輸入"""
        if not content or not content.strip():
            raise ChatServiceError("訊息內容不能為空", "empty_content")
        
        if len(content) > self.max_message_length:
            raise ChatServiceError(
                f"訊息長度超過限制 ({self.max_message_length} 字元)", 
                "content_too_long"
            )
        
        if not user_id or not employee_id:
            raise ChatServiceError("用戶資訊不完整", "invalid_user")
    
    async def _get_or_create_session(
        self, 
        user_id: str, 
        employee_id: str, 
        session_id: Optional[str]
    ) -> Session:
        """取得或建立會話"""
        if session_id:
            # 嘗試取得現有會話
            session = await self.session_service.get_session(session_id)
            if session and session.user_id == user_id and session.is_active:
                # 更新會話活動時間
                session.update_activity()
                await self.session_service.update_session(session)
                return session
        
        # 建立新會話
        session_data = {
            "preferred_model": self.default_model.value,
            "chat_service_version": "1.0.0"
        }
        
        session = await self.session_service.create_session(
            user_id, employee_id, session_data
        )
        
        self.stats["total_sessions"] += 1
        logger.info(f"建立新會話: {session.session_id}")
        return session
    
    async def _create_user_message(
        self, 
        user_id: str, 
        session_id: str, 
        content: str, 
        metadata: Dict[str, Any]
    ) -> Message:
        """建立用戶訊息"""
        message = Message(
            session_id=session_id,
            user_id=user_id,
            role=MessageRole.USER,
            content=content,
            status=MessageStatus.PENDING,
            metadata=metadata
        )
        
        # 儲存到 Redis
        await self._save_message(message)
        return message
    
    async def _filter_bmp_content(self, content: str, user_id: str) -> Dict[str, Any]:
        """BPM 內容過濾"""
        try:
            return bmp_filter.analyze_content(content, user_id)
        except Exception as e:
            logger.error(f"BPM 過濾失敗: {e}")
            # 過濾失敗時預設允許，但記錄警告
            return {
                "result": "allowed",
                "confidence": 0.0,
                "error": str(e),
                "warning": "過濾器暫時無法使用"
            }
    
    def _select_ai_model(
        self, 
        preferred_model: Optional[ModelType], 
        session: Session
    ) -> ModelType:
        """選擇 AI 模型"""
        # 優先使用指定模型
        if preferred_model:
            return preferred_model
        
        # 使用會話偏好模型
        if session.preferred_model:
            try:
                return ModelType(session.preferred_model)
            except ValueError:
                pass
        
        # 使用路由器選擇
        selected = model_router.select_model()
        if selected:
            return selected
        
        # 最後備援
        return self.default_model
    
    async def _prepare_conversation_history(self, session_id: str) -> List[Dict[str, str]]:
        """準備對話歷史"""
        messages_key = f"messages:{session_id}"
        
        # 獲取最近的對話記錄（限制數量以避免 token 過多）
        max_history = int(os.getenv("CHAT_MAX_HISTORY_MESSAGES", "20"))
        recent_messages = await self.redis_client.get_list_range(
            messages_key, -max_history, -1
        )
        
        conversation = []
        for message_data in recent_messages:
            try:
                message = Message.from_json(message_data)
                if message.status == MessageStatus.COMPLETED:
                    conversation.append({
                        "role": message.role.value,
                        "content": message.content
                    })
            except Exception as e:
                logger.warning(f"解析歷史訊息失敗: {e}")
                continue
        
        # 添加系統提示
        system_prompt = self._get_system_prompt()
        if system_prompt:
            conversation.insert(0, {
                "role": "system",
                "content": system_prompt
            })
        
        return conversation
    
    def _get_system_prompt(self) -> str:
        """獲取系統提示"""
        return """你是一個專門協助企業 Business Process Management (BPM) 的 AI 助手。

你的職責：
1. 只回答與 BPM、業務流程管理、組織管理相關的問題
2. 提供專業、準確的 BPM 建議和指導
3. 幫助優化業務流程和提升組織效率
4. 對於非 BPM 相關問題，禮貌地說明你只能協助 BPM 相關事務

回應原則：
- 使用繁體中文回應
- 保持專業和友善的語調
- 提供具體、可操作的建議
- 適當時引用最佳實務案例
"""
    
    async def _generate_response(
        self,
        request_id: str,
        user_message: Message,
        model_type: ModelType,
        conversation_history: List[Dict[str, str]],
        start_time: datetime
    ) -> Dict[str, Any]:
        """生成 AI 回應"""
        client = model_router.model_clients.get(model_type)
        if not client:
            raise ChatServiceError(f"模型 {model_type.value} 不可用", "model_unavailable")
        
        try:
            # 記錄模型使用開始
            model_router.record_request_start(model_type)
            
            # 調用 AI 模型
            ai_response = await client.generate_response(
                messages=conversation_history,
                user_id=user_message.user_id,
                session_id=user_message.session_id
            )
            
            # 記錄模型使用結束
            model_router.record_request_end(
                model_type, 
                True, 
                ai_response.get("response_time_ms", 0)
            )
            
            # 建立助手訊息
            assistant_message = await self._create_assistant_message(
                user_message, ai_response, model_type
            )
            
            # 更新用戶訊息狀態
            user_message.mark_as_completed()
            await self._save_message(user_message)
            
            # 更新統計
            self.stats["successful_messages"] += 1
            
            # 計算總處理時間
            end_time = datetime.utcnow()
            total_time = (end_time - start_time).total_seconds() * 1000
            
            return {
                "request_id": request_id,
                "user_message": MessageResponse.from_message(user_message),
                "assistant_message": MessageResponse.from_message(assistant_message),
                "model_used": model_type.value,
                "processing_time_ms": total_time,
                "ai_response_time_ms": ai_response.get("response_time_ms", 0),
                "token_usage": ai_response.get("usage", {}),
                "cost": ai_response.get("cost_usd", 0.0),
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            # 記錄模型使用失敗
            model_router.record_request_end(
                model_type, 
                False, 
                (datetime.utcnow() - start_time).total_seconds() * 1000,
                str(e)
            )
            
            # 更新用戶訊息狀態為失敗
            user_message.mark_as_failed({"error": str(e)})
            await self._save_message(user_message)
            
            self.stats["failed_messages"] += 1
            logger.error(f"AI 模型回應失敗: {e}")
            raise ChatServiceError(f"AI 回應生成失敗: {e}", "ai_response_failed")
    
    async def _generate_streaming_response(
        self,
        request_id: str,
        user_message: Message,
        model_type: ModelType,
        conversation_history: List[Dict[str, str]],
        start_time: datetime
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成串流 AI 回應"""
        # 串流回應的實作會比較複雜，這裡提供基本框架
        # 實際使用時需要根據前端需求調整
        client = model_router.model_clients.get(model_type)
        if not client:
            raise ChatServiceError(f"模型 {model_type.value} 不可用", "model_unavailable")
        
        try:
            model_router.record_request_start(model_type)
            
            # 標記用戶訊息為處理中
            user_message.mark_as_processing()
            await self._save_message(user_message)
            
            # 生成串流回應
            async for chunk in client.generate_response(
                messages=conversation_history,
                user_id=user_message.user_id,
                session_id=user_message.session_id,
                stream=True
            ):
                yield {
                    "request_id": request_id,
                    "type": "chunk",
                    "content": chunk.get("content", ""),
                    "is_final": chunk.get("is_final", False),
                    "model_used": model_type.value
                }
            
            # 標記完成
            user_message.mark_as_completed()
            await self._save_message(user_message)
            
            model_router.record_request_end(model_type, True, 0)
            self.stats["successful_messages"] += 1
            
        except Exception as e:
            model_router.record_request_end(model_type, False, 0, str(e))
            user_message.mark_as_failed({"error": str(e)})
            await self._save_message(user_message)
            
            self.stats["failed_messages"] += 1
            raise ChatServiceError(f"串流回應失敗: {e}", "streaming_failed")
    
    async def _create_assistant_message(
        self,
        user_message: Message,
        ai_response: Dict[str, Any],
        model_type: ModelType
    ) -> Message:
        """建立助手訊息"""
        assistant_message = Message(
            session_id=user_message.session_id,
            user_id=user_message.user_id,
            role=MessageRole.ASSISTANT,
            content=ai_response.get("content", ""),
            status=MessageStatus.COMPLETED,
            model_name=model_type.value,
            model_response_time=ai_response.get("response_time_ms", 0),
            token_usage=ai_response.get("usage", {}),
            metadata={
                "ai_response": ai_response,
                "related_user_message": user_message.message_id
            }
        )
        
        # 儲存到 Redis
        await self._save_message(assistant_message)
        return assistant_message
    
    async def _create_rejection_response(
        self,
        request_id: str,
        user_message: Message,
        filter_result: Dict[str, Any],
        start_time: datetime
    ) -> Dict[str, Any]:
        """建立拒絕回應"""
        rejection_content = "抱歉，我只能協助回答 Business Process Management (BPM) 相關的問題。"
        
        if "suggestions" in filter_result and filter_result["suggestions"]:
            rejection_content += "\n\n" + "\n".join(filter_result["suggestions"])
        
        # 建立拒絕助手訊息
        assistant_message = Message(
            session_id=user_message.session_id,
            user_id=user_message.user_id,
            role=MessageRole.ASSISTANT,
            content=rejection_content,
            status=MessageStatus.COMPLETED,
            metadata={
                "filter_result": filter_result,
                "rejection_reason": "non_bmp_content"
            }
        )
        
        # 更新用戶訊息狀態
        user_message.status = MessageStatus.COMPLETED
        
        # 儲存訊息
        await self._save_message(user_message)
        await self._save_message(assistant_message)
        
        end_time = datetime.utcnow()
        total_time = (end_time - start_time).total_seconds() * 1000
        
        return {
            "request_id": request_id,
            "user_message": MessageResponse.from_message(user_message),
            "assistant_message": MessageResponse.from_message(assistant_message),
            "model_used": "filter",
            "processing_time_ms": total_time,
            "filter_result": filter_result,
            "rejected": True,
            "timestamp": end_time.isoformat()
        }
    
    async def _save_message(self, message: Message):
        """儲存訊息到 Redis"""
        try:
            # 儲存到訊息列表
            messages_key = f"messages:{message.session_id}"
            await self.redis_client.append_to_list(messages_key, message.to_json())
            
            # 設定 TTL（與會話時間一致）
            await self.redis_client.expire(messages_key, 86400)  # 24 小時
            
            # 儲存到個別訊息鍵（用於快速查詢）
            message_key = f"message:{message.message_id}"
            await self.redis_client.set_json(message_key, message.to_redis_dict(), 86400)
            
        except Exception as e:
            logger.error(f"儲存訊息失敗: {e}")
            raise ChatServiceError(f"訊息儲存失敗: {e}", "message_save_failed")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """獲取服務統計"""
        return {
            **self.stats,
            "success_rate": (
                self.stats["successful_messages"] / self.stats["total_messages"]
                if self.stats["total_messages"] > 0 else 0.0
            ),
            "filter_rate": (
                self.stats["filtered_messages"] / self.stats["total_messages"]
                if self.stats["total_messages"] > 0 else 0.0
            ),
            "model_stats": model_router.get_router_stats(),
            "filter_stats": bmp_filter.get_filter_stats() if self.enable_bmp_filter else {}
        }
    
    def reset_stats(self):
        """重置統計資料"""
        self.stats = {
            "total_messages": 0,
            "successful_messages": 0,
            "failed_messages": 0,
            "filtered_messages": 0,
            "total_sessions": 0,
            "active_sessions": 0
        }

# 全域聊天服務實例
_chat_service: Optional[ChatService] = None

def get_chat_service() -> ChatService:
    """獲取聊天服務實例"""
    global _chat_service
    if _chat_service is None:
        _chat_service = ChatService()
    return _chat_service

async def start_chat_service():
    """啟動聊天服務"""
    service = get_chat_service()
    await service.start_services()

async def stop_chat_service():
    """停止聊天服務"""
    global _chat_service
    if _chat_service:
        await _chat_service.stop_services()