{"version": 3, "file": "RedirectClient.mjs", "sources": ["../../src/interaction_client/RedirectClient.ts"], "sourcesContent": [null], "names": ["BrowserUtils.replaceHash", "BrowserUtils.isInIframe", "BrowserUtils.getHomepage", "ResponseHandler.validateInteractionType", "BrowserUtils.clearHash", "BrowserAuthErrorCodes.noStateInHash", "BrowserAuthErrorCodes.nativeConnectionNotEstablished", "BrowserAuthErrorCodes.noCachedAuthorityError", "BrowserUtils.getCurrentUri"], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAGG;AAgDH,SAAS,iBAAiB,GAAA;IACtB,IACI,OAAO,MAAM,KAAK,WAAW;AAC7B,QAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;AACzC,QAAA,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB,KAAK,UAAU,EAC3D;AACE,QAAA,OAAO,SAAS,CAAC;AACpB,KAAA;IAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC5E,IAAA,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM;AACvC,UAAG,iBAAiB,CAAC,CAAC,CAAiC;UACrD,SAAS,CAAC;IAChB,OAAO,UAAU,EAAE,IAAI,CAAC;AAC5B,CAAC;AAEK,MAAO,cAAe,SAAQ,yBAAyB,CAAA;AAGzD,IAAA,WAAA,CACI,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,iBAAqC,EACrC,iBAAsC,EACtC,oBAA2C,EAC3C,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,oBAAoB,EACpB,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;KAC1C;AAED;;;AAGG;IACH,MAAM,YAAY,CAAC,OAAwB,EAAA;AACvC,QAAA,MAAM,YAAY,GAAG,MAAM,WAAW,CAClC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;AAErC,QAAA,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAClC,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,KAAK,EAClB,YAAY,CAAC,SAAS,EACtB,YAAY,CAAC,SAAS,IAAI,EAAE,EAC5B,YAAY,CAAC,OAAO,IAAI,IAAI,CAC/B,CAAC;QACF,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,oBAAoB,CAC7B,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAG,CAAC,KAA0B,KAAI;;YAEpD,IAAI,KAAK,CAAC,SAAS,EAAE;AACjB,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sEAAsE,CACzE,CAAC;gBACF,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5D,gBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,oBAAoB,EAC9B,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACL,aAAA;AACL,SAAC,CAAC;QAEF,IAAI;;AAEA,YAAA,MAAM,eAAe,GACjB,MAAM,WAAW,CACb,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,2DAA2D,EAC7E,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,YAAY,CAAC,CAAC;;AAGpB,YAAA,MAAM,UAAU,GAA4B,MAAM,WAAW,CACzD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;gBACE,sBAAsB;gBACtB,gBAAgB,EAAE,YAAY,CAAC,SAAS;gBACxC,wBAAwB,EAAE,YAAY,CAAC,iBAAiB;gBACxD,2BAA2B,EAAE,YAAY,CAAC,oBAAoB;gBAC9D,OAAO,EAAE,YAAY,CAAC,OAAO;AAChC,aAAA,CAAC,CAAC;;YAGH,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAC1C,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,eAAe,EACf,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,YAAA,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC;AAChD,gBAAA,GAAG,YAAY;gBACf,YAAY,EAAE,oBAAoB,CAAC,iBAAiB,CAChD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,oBAAoB,EACzB,OAAO,CAAC,oBAAoB,CAC/B;AACJ,aAAA,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAC/C,OAAO,CAAC,iBAAiB,CAC5B,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAwB,qBAAA,EAAA,iBAAiB,CAAE,CAAA,CAAC,CAAC;;AAGpE,YAAA,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;;AAGtD,YAAA,OAAO,MAAM,kBAAkB,CAAC,mBAAmB,CAAC,WAAW,EAAE;gBAC7D,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AACvC,gBAAA,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AAC7D,gBAAA,iBAAiB,EAAE,iBAAiB;gBACpC,kBAAkB,EACd,OAAO,CAAC,kBAAkB;AAC1B,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB;AAC1C,aAAA,CAAC,CAAC;AACN,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvC,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YACzD,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5D,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;;;AAMG;AACH,IAAA,MAAM,qBAAqB,CACvB,IAAe,GAAA,EAAE,EACjB,iBAA6C,EAAA;QAE7C,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,qBAAqB,CAC9B,CAAC;QAEF,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;AACpD,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,uFAAuF,CAC1F,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACD,YAAA,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAC3D,IAAI,IAAI,EAAE,CACb,CAAC;YACF,IAAI,CAAC,YAAY,EAAE;;AAEf,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,sGAAsG,CACzG,CAAC;gBACF,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAC7C,eAAe,CAAC,QAAQ,CAC3B,CAAC;;AAGF,gBAAA,IAAI,iBAAiB,EAAE,KAAK,cAAc,EAAE;AACxC,oBAAA,iBAAiB,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAoB,CAAC;AAC5D,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iEAAiE,CACpE,CAAC;AACL,iBAAA;AACD,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;;AAGD,YAAA,MAAM,eAAe,GACjB,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,UAAU,EAC7B,IAAI,CACP,IAAI,SAAS,CAAC,YAAY,CAAC;YAChC,MAAM,yBAAyB,GAC3B,SAAS,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;AACjD,YAAA,MAAM,oBAAoB,GAAG,SAAS,CAAC,iBAAiB,CACpD,MAAM,CAAC,QAAQ,CAAC,IAAI,CACvB,CAAC;YAEF,IACI,yBAAyB,KAAK,oBAAoB;AAClD,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAC5C;;AAEE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;gBAEF,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;;AAEnC,oBAAAA,WAAwB,CAAC,eAAe,CAAC,CAAC;AAC7C,iBAAA;gBAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAC9C,YAAY,EACZ,sBAAsB,CACzB,CAAC;AAEF,gBAAA,OAAO,gBAAgB,CAAC;AAC3B,aAAA;iBAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;AACpD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2DAA2D,CAC9D,CAAC;gBACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAC5B,YAAY,EACZ,sBAAsB,CACzB,CAAC;AACL,aAAA;AAAM,iBAAA,IACH,CAACC,UAAuB,EAAE;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAC1C;AACE;;;AAGG;AACH,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,QAAQ,EAC3B,cAAc,EACd,IAAI,CACP,CAAC;AACF,gBAAA,MAAM,iBAAiB,GAAsB;oBACzC,KAAK,EAAE,KAAK,CAAC,qBAAqB;AAClC,oBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,oBAAA,SAAS,EAAE,IAAI;iBAClB,CAAC;AAEF;;;AAGG;gBACH,IAAI,qBAAqB,GAAY,IAAI,CAAC;AAC1C,gBAAA,IAAI,CAAC,eAAe,IAAI,eAAe,KAAK,MAAM,EAAE;;AAEhD,oBAAA,MAAM,QAAQ,GAAGC,WAAwB,EAAE,CAAC;;AAE5C,oBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,UAAU,EAC7B,QAAQ,EACR,IAAI,CACP,CAAC;AACF,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4EAA4E,CAC/E,CAAC;oBACF,qBAAqB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,QAAQ,EACR,iBAAiB,CACpB,CAAC;AACT,iBAAA;AAAM,qBAAA;;oBAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAkC,+BAAA,EAAA,eAAe,CAAE,CAAA,CACtD,CAAC;oBACF,qBAAqB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,eAAe,EACf,iBAAiB,CACpB,CAAC;AACT,iBAAA;;gBAGD,IAAI,CAAC,qBAAqB,EAAE;oBACxB,OAAO,MAAM,IAAI,CAAC,cAAc,CAC5B,YAAY,EACZ,sBAAsB,CACzB,CAAC;AACL,iBAAA;AACJ,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;YACD,IAAI,CAAC,cAAc,CAAC,6BAA6B,CAC7C,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACF,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACO,IAAA,mBAAmB,CACzB,oBAA4B,EAAA;AAE5B,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;;QAEtD,IAAI,cAAc,GAAG,oBAAoB,CAAC;QAC1C,IAAI,CAAC,cAAc,EAAE;YACjB,IACI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBAC/C,kBAAkB,CAAC,KAAK,EAC1B;AACE,gBAAA,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3C,aAAA;AAAM,iBAAA;AACH,gBAAA,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AACzC,aAAA;AACJ,SAAA;QACD,IAAI,QAAQ,GAAG,QAAQ,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;AAEhE,QAAA,IAAI,QAAQ,EAAE;YACV,IAAI;AACA,gBAAAC,uBAAuC,CACnC,QAAQ,EACR,IAAI,CAAC,aAAa,EAClB,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA6C,0CAAA,EAAA,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,YAAY,CAAA,CAAE,CAChF,CAAC;AACL,iBAAA;AACD,gBAAA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACrB,aAAA;AAED,YAAAC,SAAsB,CAAC,MAAM,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yDAAyD,CAC5D,CAAC;AACF,YAAA,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACpD,kBAAkB,CAAC,QAAQ,EAC3B,IAAI,CACP,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAC1B,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CACpE,CAAC;AAEF,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,QAAQ,GAAG,QAAQ,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACxD,YAAA,IAAI,QAAQ,EAAE;AACV,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,CAClE,CAAC;AACF,gBAAA,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACjC,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACrB;AAED;;;;AAIG;AACO,IAAA,MAAM,cAAc,CAC1B,YAA6C,EAC7C,sBAA8C,EAAA;AAE9C,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,sBAAsB,CAACC,aAAmC,CAAC,CAAC;AACrE,SAAA;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;QAEvE,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iDAAiD,CACpD,CAAC;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,MAAM,sBAAsB,CACxBC,8BAAoD,CACvD,CAAC;AACL,aAAA;YACD,MAAM,uBAAuB,GAAG,IAAI,uBAAuB,CACvD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,gBAAgB,EACrB,KAAK,CAAC,iBAAiB,EACvB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,EACzB,YAAY,CAAC,SAAS,EACtB,IAAI,CAAC,aAAa,EAClB,aAAa,CAAC,aAAa,CAC9B,CAAC;AACF,YAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,aAAa,CAAC,iBAAiB,CACxD,IAAI,CAAC,aAAa,EAClB,KAAK,CACR,CAAC;AACF,YAAA,OAAO,uBAAuB;AACzB,iBAAA,YAAY,CAAC;AACV,gBAAA,GAAG,aAAa;AAChB,gBAAA,KAAK,EAAE,gBAAgB;gBACvB,MAAM,EAAE,SAAS;aACpB,CAAC;iBACD,OAAO,CAAC,MAAK;AACV,gBAAA,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACnD,aAAC,CAAC,CAAC;AACV,SAAA;;QAGD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,EAAE;AACnB,YAAA,MAAM,sBAAsB,CACxBC,sBAA4C,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,UAAU,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,CAAC,CAAC;AAElE,QAAA,eAAe,CAAC,cAAc,CAC1B,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,aAAa,CAChB,CAAC;QACF,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAC1C,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,aAAa,EACb,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;QACF,OAAO,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;KACrE;AAED;;;;AAIG;IACH,MAAM,MAAM,CAAC,aAAiC,EAAA;AAC1C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QACvE,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,MAAM,CACf,CAAC;QAEF,IAAI;AACA,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,YAAY,EACtB,eAAe,CAAC,QAAQ,EACxB,aAAa,CAChB,CAAC;;YAGF,MAAM,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AAE1D,YAAA,MAAM,iBAAiB,GAAsB;gBACzC,KAAK,EAAE,KAAK,CAAC,MAAM;AACnB,gBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,gBAAA,SAAS,EAAE,KAAK;aACnB,CAAC;AAEF,YAAA,MAAM,UAAU,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;gBACE,sBAAsB;AACtB,gBAAA,gBAAgB,EAAE,aAAa,IAAI,aAAa,CAAC,SAAS;gBAC1D,2BAA2B,EACvB,aAAa,EAAE,oBAAoB;gBACvC,OAAO,EAAE,CAAC,aAAa,IAAI,aAAa,CAAC,OAAO,KAAK,SAAS;AACjE,aAAA,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EAAE;gBACzD,IAAI;AACA,oBAAA,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC;AAC3C,iBAAA;gBAAC,MAAM;AACJ,oBAAA,IAAI,kBAAkB,CAAC,OAAO,EAAE,aAAa,EAAE;AAC3C,wBAAA,KAAK,IAAI,CAAC,cAAc,CAAC,aAAa,CAClC,kBAAkB,CAAC,OAAO,EAAE,aAAa,EACzC,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,QAAQ,EACxB,kBAAkB,CACrB,CAAC;wBAEF,OAAO;AACV,qBAAA;AACJ,iBAAA;AACJ,aAAA;;YAGD,MAAM,SAAS,GACX,UAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;AAEhD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,QAAQ,EACxB,kBAAkB,CACrB,CAAC;;AAEF,YAAA,IACI,aAAa;AACb,gBAAA,OAAO,aAAa,CAAC,kBAAkB,KAAK,UAAU,EACxD;gBACE,MAAM,QAAQ,GAAG,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAE7D,IAAI,QAAQ,KAAK,KAAK,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4DAA4D,CAC/D,CAAC;;AAEF,oBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,EAAE;AACjD,wBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AACtD,qBAAA;oBACD,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,SAAS,EACT,iBAAiB,CACpB,CAAC;oBACF,OAAO;AACV,iBAAA;AAAM,qBAAA;;AAEH,oBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,CAClE,CAAC;AACL,iBAAA;AACJ,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,EAAE;AACjD,oBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AACtD,iBAAA;gBACD,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,SAAS,EACT,iBAAiB,CACpB,CAAC;gBACF,OAAO;AACV,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,QAAQ,EACxB,IAAI,EACJ,CAAe,CAClB,CAAC;AACF,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,UAAU,EACpB,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACF,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,UAAU,EACpB,eAAe,CAAC,QAAQ,CAC3B,CAAC;KACL;AAED;;;AAGG;AACO,IAAA,oBAAoB,CAAC,gBAAyB,EAAA;QACpD,MAAM,iBAAiB,GAAG,gBAAgB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnE,OAAO,SAAS,CAAC,cAAc,CAC3B,iBAAiB,EACjBC,aAA0B,EAAE,CAC/B,CAAC;KACL;AACJ;;;;"}