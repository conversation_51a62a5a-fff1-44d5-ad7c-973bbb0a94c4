{"version": 3, "file": "ResponseHandler.mjs", "sources": ["../../src/response/ResponseHandler.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.hashEmptyError", "BrowserAuthErrorCodes.hashDoesNotContainKnownProperties", "BrowserAuthErrorCodes.noStateInHash", "BrowserAuthErrorCodes.unableToParseState", "BrowserAuthErrorCodes.stateInteractionTypeMismatch"], "mappings": ";;;;;;;AAAA;;;AAGG;SAea,mBAAmB,CAC/B,cAAsB,EACtB,gBAAwB,EACxB,MAAc,EAAA;;IAGd,MAAM,YAAY,GAAG,QAAQ,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;IACtE,IAAI,CAAC,YAAY,EAAE;AACf,QAAA,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,cAAc,CAAC,EAAE;;YAEnD,MAAM,CAAC,KAAK,CACR,CAAA,kDAAA,EAAqD,gBAAgB,CAAyC,sCAAA,EAAA,gBAAgB,CAA4F,0FAAA,CAAA,CAC7N,CAAC;AACF,YAAA,MAAM,sBAAsB,CAACA,cAAoC,CAAC,CAAC;AACtE,SAAA;AAAM,aAAA;YACH,MAAM,CAAC,KAAK,CACR,CAAA,EAAA,EAAK,gBAAgB,CAA4F,yFAAA,EAAA,gBAAgB,CAA6D,2DAAA,CAAA,CACjM,CAAC;YACF,MAAM,CAAC,QAAQ,CACX,CAAA,IAAA,EAAO,gBAAgB,CAAiB,cAAA,EAAA,cAAc,CAAE,CAAA,CAC3D,CAAC;AACF,YAAA,MAAM,sBAAsB,CACxBC,iCAAuD,CAC1D,CAAC;AACL,SAAA;AACJ,KAAA;AACD,IAAA,OAAO,YAAY,CAAC;AACxB,CAAC;AAED;;AAEG;SACa,uBAAuB,CACnC,QAAyC,EACzC,aAAsB,EACtB,eAAgC,EAAA;AAEhC,IAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACjB,QAAA,MAAM,sBAAsB,CAACC,aAAmC,CAAC,CAAC;AACrE,KAAA;IAED,MAAM,gBAAgB,GAAG,0BAA0B,CAC/C,aAAa,EACb,QAAQ,CAAC,KAAK,CACjB,CAAC;IACF,IAAI,CAAC,gBAAgB,EAAE;AACnB,QAAA,MAAM,sBAAsB,CAACC,kBAAwC,CAAC,CAAC;AAC1E,KAAA;AAED,IAAA,IAAI,gBAAgB,CAAC,eAAe,KAAK,eAAe,EAAE;AACtD,QAAA,MAAM,sBAAsB,CACxBC,4BAAkD,CACrD,CAAC;AACL,KAAA;AACL;;;;"}