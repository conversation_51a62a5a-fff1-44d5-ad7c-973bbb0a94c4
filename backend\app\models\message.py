"""
訊息資料模型
定義聊天訊息的資料結構和序列化邏輯
"""

import json
import uuid
from typing import Optional, Dict, List, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

class MessageRole(str, Enum):
    """訊息角色列舉"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class MessageStatus(str, Enum):
    """訊息狀態列舉"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    DELETED = "deleted"

class MessageBase(BaseModel):
    """訊息基礎模型"""
    
    role: MessageRole = Field(..., description="訊息角色")
    content: str = Field(..., min_length=1, max_length=10000, description="訊息內容")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="時間戳")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")

class Message(MessageBase):
    """完整訊息模型"""
    
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="訊息 ID")
    session_id: str = Field(..., description="會話 ID")
    user_id: str = Field(..., description="用戶 ID")
    
    # 訊息狀態
    status: MessageStatus = Field(default=MessageStatus.PENDING, description="訊息狀態")
    
    # AI 模型相關
    model_name: Optional[str] = Field(default=None, description="使用的 AI 模型")
    model_response_time: Optional[float] = Field(default=None, description="模型回應時間（毫秒）")
    
    # 過濾和上下文
    filter_result: Optional[Dict[str, Any]] = Field(default=None, description="內容過濾結果")
    context_used: bool = Field(default=False, description="是否使用了上下文")
    context_sources: Optional[List[str]] = Field(default=[], description="上下文來源")
    
    # 使用統計
    token_usage: Optional[Dict[str, int]] = Field(default=None, description="Token 使用統計")
    
    # 編輯歷史
    edited_at: Optional[datetime] = Field(default=None, description="編輯時間")
    original_content: Optional[str] = Field(default=None, description="原始內容")
    edit_count: int = Field(default=0, description="編輯次數")
    
    @validator('metadata')
    def validate_metadata(cls, v):
        """驗證附加資料"""
        if v is None:
            return {}
        
        # 確保 metadata 可以序列化為 JSON
        try:
            json.dumps(v, ensure_ascii=False)
        except (TypeError, ValueError) as e:
            raise ValueError(f"metadata 必須可以序列化為 JSON: {e}")
        
        return v
    
    @validator('content')
    def validate_content(cls, v):
        """驗證內容"""
        if not v or not v.strip():
            raise ValueError("訊息內容不能為空")
        return v.strip()
    
    def to_redis_dict(self) -> Dict[str, Any]:
        """轉換為 Redis 儲存格式"""
        return {
            "message_id": self.message_id,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "role": self.role.value,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "model_name": self.model_name,
            "model_response_time": self.model_response_time,
            "filter_result": self.filter_result,
            "context_used": self.context_used,
            "context_sources": self.context_sources,
            "token_usage": self.token_usage,
            "edited_at": self.edited_at.isoformat() if self.edited_at else None,
            "original_content": self.original_content,
            "edit_count": self.edit_count,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_redis_dict(cls, data: Dict[str, Any]) -> "Message":
        """從 Redis 資料建立訊息物件"""
        # 處理時間戳
        if isinstance(data.get("timestamp"), str):
            data["timestamp"] = datetime.fromisoformat(data["timestamp"])
        
        if isinstance(data.get("edited_at"), str):
            data["edited_at"] = datetime.fromisoformat(data["edited_at"])
        elif data.get("edited_at") is None:
            data.pop("edited_at", None)
        
        # 處理角色和狀態
        if isinstance(data.get("role"), str):
            data["role"] = MessageRole(data["role"])
        
        if isinstance(data.get("status"), str):
            data["status"] = MessageStatus(data["status"])
        
        return cls(**data)
    
    def to_json(self) -> str:
        """轉換為 JSON 字串"""
        return json.dumps(self.to_redis_dict(), ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> "Message":
        """從 JSON 字串建立訊息物件"""
        data = json.loads(json_str)
        return cls.from_redis_dict(data)
    
    def edit_content(self, new_content: str) -> None:
        """編輯訊息內容"""
        if self.original_content is None:
            self.original_content = self.content
        
        self.content = new_content.strip()
        self.edited_at = datetime.utcnow()
        self.edit_count += 1
        self.status = MessageStatus.COMPLETED
    
    def mark_as_processing(self) -> None:
        """標記為處理中"""
        self.status = MessageStatus.PROCESSING
    
    def mark_as_completed(self) -> None:
        """標記為已完成"""
        self.status = MessageStatus.COMPLETED
    
    def mark_as_failed(self, error_info: Optional[Dict[str, Any]] = None) -> None:
        """標記為失敗"""
        self.status = MessageStatus.FAILED
        if error_info:
            self.metadata["error"] = error_info
    
    def add_context_info(self, sources: List[str], used: bool = True) -> None:
        """添加上下文資訊"""
        self.context_used = used
        self.context_sources = sources
        if used:
            self.metadata["context_count"] = len(sources)
    
    def set_model_info(self, model_name: str, response_time: float, token_usage: Optional[Dict[str, int]] = None) -> None:
        """設定模型資訊"""
        self.model_name = model_name
        self.model_response_time = response_time
        if token_usage:
            self.token_usage = token_usage
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            MessageRole: lambda v: v.value,
            MessageStatus: lambda v: v.value
        }
        
        schema_extra = {
            "example": {
                "message_id": "550e8400-e29b-41d4-a716-446655440000",
                "session_id": "sess_123456",
                "user_id": "user_789",
                "role": "user",
                "content": "如何優化業務流程？",
                "timestamp": "2024-01-01T10:00:00Z",
                "status": "completed",
                "model_name": "chatgpt",
                "context_used": True,
                "metadata": {
                    "client_info": {
                        "user_agent": "Mozilla/5.0...",
                        "ip_address": "*************"
                    }
                }
            }
        }

class MessageCreateRequest(BaseModel):
    """建立訊息請求模型"""
    
    content: str = Field(..., min_length=1, max_length=10000, description="訊息內容")
    role: MessageRole = Field(default=MessageRole.USER, description="訊息角色")
    session_id: Optional[str] = Field(default=None, description="會話 ID")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")
    
    class Config:
        schema_extra = {
            "example": {
                "content": "請幫我分析這個業務流程的優化方案",
                "role": "user",
                "session_id": "sess_123456",
                "metadata": {
                    "source": "web_interface",
                    "priority": "normal"
                }
            }
        }

class MessageResponse(BaseModel):
    """訊息回應模型"""
    
    message_id: str = Field(..., description="訊息 ID")
    session_id: str = Field(..., description="會話 ID")
    content: str = Field(..., description="訊息內容")
    role: str = Field(..., description="訊息角色")
    timestamp: str = Field(..., description="時間戳")
    status: str = Field(..., description="訊息狀態")
    model_name: Optional[str] = Field(default=None, description="AI 模型名稱")
    context_used: bool = Field(default=False, description="是否使用上下文")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")
    
    @classmethod
    def from_message(cls, message: Message) -> "MessageResponse":
        """從 Message 物件建立回應"""
        return cls(
            message_id=message.message_id,
            session_id=message.session_id,
            content=message.content,
            role=message.role.value,
            timestamp=message.timestamp.isoformat(),
            status=message.status.value,
            model_name=message.model_name,
            context_used=message.context_used,
            metadata=message.metadata
        )

class MessageListResponse(BaseModel):
    """訊息列表回應模型"""
    
    messages: List[MessageResponse] = Field(..., description="訊息列表")
    total: int = Field(..., description="總訊息數")
    session_id: str = Field(..., description="會話 ID")
    has_more: bool = Field(default=False, description="是否有更多訊息")
    next_offset: Optional[int] = Field(default=None, description="下一頁偏移量")
    
    class Config:
        schema_extra = {
            "example": {
                "messages": [
                    {
                        "message_id": "550e8400-e29b-41d4-a716-446655440000",
                        "session_id": "sess_123456",
                        "content": "如何優化業務流程？",
                        "role": "user",
                        "timestamp": "2024-01-01T10:00:00Z",
                        "status": "completed"
                    }
                ],
                "total": 10,
                "session_id": "sess_123456",
                "has_more": True,
                "next_offset": 20
            }
        }

class MessageUpdateRequest(BaseModel):
    """訊息更新請求模型"""
    
    content: Optional[str] = Field(default=None, min_length=1, max_length=10000, description="新的訊息內容")
    status: Optional[MessageStatus] = Field(default=None, description="新的訊息狀態")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="新的附加資料")
    
    class Config:
        schema_extra = {
            "example": {
                "content": "更新後的訊息內容",
                "status": "completed",
                "metadata": {
                    "edited_reason": "typo_correction"
                }
            }
        }

class MessageSearchRequest(BaseModel):
    """訊息搜尋請求模型"""
    
    query: str = Field(..., min_length=1, description="搜尋關鍵字")
    session_id: Optional[str] = Field(default=None, description="限制在特定會話")
    role: Optional[MessageRole] = Field(default=None, description="限制訊息角色")
    start_date: Optional[datetime] = Field(default=None, description="開始日期")
    end_date: Optional[datetime] = Field(default=None, description="結束日期")
    limit: int = Field(default=20, ge=1, le=100, description="結果數量限制")
    offset: int = Field(default=0, ge=0, description="結果偏移量")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "業務流程",
                "session_id": "sess_123456",
                "role": "user",
                "limit": 20,
                "offset": 0
            }
        }