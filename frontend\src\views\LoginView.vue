<template>
  <div class="login-container">
    <!-- 登入頁面主體 -->
    <div class="login-card">
      <!-- <PERSON><PERSON> 和標題 -->
      <div class="login-header">
        <div class="logo">
          <svg 
            width="48" 
            height="48" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              d="M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z" 
              fill="#008787"
            />
            <path 
              d="M9 12L11 14L15 10" 
              stroke="white" 
              stroke-width="2" 
              stroke-linecap="round" 
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <h1 class="title">BPM Chatbot</h1>
        <p class="subtitle">智能 Business Process Management 問答系統</p>
      </div>

      <!-- 登入狀態顯示 -->
      <div class="login-status">
        <div v-if="isLoading" class="status-loading">
          <div class="spinner"></div>
          <p>{{ loadingMessage }}</p>
        </div>

        <div v-else-if="error" class="status-error">
          <div class="error-icon">⚠️</div>
          <p class="error-message">{{ error }}</p>
          <button 
            class="retry-button" 
            @click="clearError"
          >
            重試
          </button>
        </div>

        <div v-else-if="!isAuthenticated" class="status-ready">
          <div class="welcome-message">
            <h2>歡迎使用 BPM Chatbot</h2>
            <p>請使用您的公司帳號登入以開始使用智能問答服務</p>
          </div>

          <!-- 功能特色 -->
          <div class="features">
            <div class="feature-item">
              <div class="feature-icon">🤖</div>
              <div class="feature-text">
                <h3>智能問答</h3>
                <p>專業的 BPM 流程諮詢服務</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔒</div>
              <div class="feature-text">
                <h3>安全認證</h3>
                <p>企業級 Azure AD 身份驗證</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <div class="feature-text">
                <h3>多模型支援</h3>
                <p>ChatGPT、Claude、Gemini 任您選擇</p>
              </div>
            </div>
          </div>

          <!-- 登入按鈕 -->
          <button 
            class="login-button"
            @click="handleLogin"
            :disabled="isLoading"
          >
            <span class="button-icon">🏢</span>
            使用公司帳號登入
          </button>
        </div>

        <div v-else class="status-authenticated">
          <div class="user-info">
            <div class="avatar">
              {{ userInitials }}
            </div>
            <div class="user-details">
              <h3>{{ userDisplayName }}</h3>
              <p>{{ userEmail }}</p>
              <p class="user-department">{{ userDepartment }}</p>
            </div>
          </div>
          
          <div class="session-info">
            <p class="session-text">
              會話剩餘時間: {{ remainingSessionTime }}
            </p>
            <div class="session-progress" :class="{ 'session-warning': sessionProgressPercent < 20 }">
              <div 
                class="session-bar" 
                :style="{ width: sessionProgressPercent + '%' }"
              ></div>
            </div>
            <div v-if="sessionProgressPercent < 10" class="session-alert">
              ⚠️ 會話即將過期，請準備重新登入
            </div>
          </div>

          <div class="action-buttons">
            <button 
              class="continue-button"
              @click="handleContinue"
            >
              進入聊天室
            </button>
            <button 
              class="logout-button"
              @click="handleLogout"
            >
              登出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 頁腳資訊 -->
    <div class="login-footer">
      <p>&copy; 2024 公司名稱. 版權所有</p>
      <div class="footer-links">
        <a href="#" @click.prevent="showPrivacyPolicy">隱私政策</a>
        <a href="#" @click.prevent="showTermsOfService">服務條款</a>
        <a href="#" @click.prevent="showSupport">技術支援</a>
      </div>
    </div>

    <!-- 隱私政策彈窗 -->
    <div v-if="showPrivacyModal" class="modal-overlay" @click="showPrivacyModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>隱私政策</h3>
          <button class="modal-close" @click="showPrivacyModal = false">×</button>
        </div>
        <div class="modal-body">
          <p>我們重視您的隱私權。此 BPM Chatbot 系統：</p>
          <ul>
            <li>僅收集必要的身份認證資訊</li>
            <li>對話內容僅用於提供服務，不會用於其他目的</li>
            <li>所有資料均加密存儲和傳輸</li>
            <li>遵循公司資料保護政策</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import authService from '@/services/authService'

/**
 * 用戶資料結構
 * @typedef {Object} User
 * @property {string} name - 用戶名稱
 * @property {string} email - 電子郵件
 * @property {string} department - 部門
 * @property {string} given_name - 名字
 * @property {string} surname - 姓氏
 * @property {string} display_name - 顯示名稱
 */

/**
 * 會話資訊結構
 * @typedef {Object} SessionInfo
 * @property {Date} created_at - 創建時間
 * @property {Date} expires_at - 過期時間
 * @property {Date} last_activity - 最後活動時間
 * @property {boolean} is_valid - 是否有效
 */

/**
 * 登入視圖組件
 * 統一處理所有登入相關邏輯和 UI
 */
export default defineComponent({
  name: 'LoginView',
  setup() {
    // Router 和 Store
    const router = useRouter()
    const authStore = useAuthStore()

    // 響應式狀態
    /** @type {import('vue').Ref<boolean>} */
    const isLoading = ref(false)
    /** @type {import('vue').Ref<string>} */
    const loadingMessage = ref('')
    /** @type {import('vue').Ref<string|null>} */
    const error = ref(null)
    /** @type {import('vue').Ref<boolean>} */
    const isAuthenticated = ref(false)
    /** @type {import('vue').Ref<User|null>} */
    const user = ref(null)
    /** @type {import('vue').Ref<SessionInfo|null>} */
    const session = ref(null)
    /** @type {import('vue').Ref<boolean>} */
    const showPrivacyModal = ref(false)

    // 會話時間更新定時器
    let sessionTimer = null

    // 計算屬性
    const userDisplayName = computed(() => {
      return user.value?.display_name || user.value?.name || '未知用戶'
    })

    const userEmail = computed(() => {
      return user.value?.email || user.value?.userPrincipalName || ''
    })

    const userDepartment = computed(() => {
      return user.value?.department ? `${user.value.department} 部門` : ''
    })

    const userInitials = computed(() => {
      if (user.value?.givenName && user.value?.surname) {
        return (user.value.givenName.charAt(0) + user.value.surname.charAt(0)).toUpperCase()
      }
      if (user.value?.displayName || user.value?.display_name) {
        const name = user.value.displayName || user.value.display_name
        const words = name.split(' ')
        if (words.length >= 2) {
          return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase()
        }
        return name.charAt(0).toUpperCase()
      }
      return '?'
    })

    const remainingSessionTime = computed(() => {
      if (!session.value) return '0 分鐘'
      
      const now = new Date()
      const expires = session.value.expires_at
      const diff = expires.getTime() - now.getTime()
      
      if (diff <= 0) return '已過期'
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(minutes / 60)
      
      if (hours > 0) {
        return `${hours} 小時 ${minutes % 60} 分鐘`
      }
      
      return `${minutes} 分鐘`
    })

    const sessionProgressPercent = computed(() => {
      if (!session.value) return 0
      
      const now = new Date()
      const created = session.value.created_at
      const expires = session.value.expires_at
      
      const total = expires.getTime() - created.getTime()
      const used = now.getTime() - created.getTime()
      
      if (total <= 0) return 0
      
      const remaining = Math.max(0, 100 - (used / total) * 100)
      return Math.min(100, remaining)
    })

    // 方法
    /**
     * 檢查認證狀態
     */
    const checkAuthStatus = async () => {
      try {
        isAuthenticated.value = authService.isAuthenticated()
        
        if (isAuthenticated.value) {
          user.value = authService.getCurrentUser()
          session.value = authService.getSession()
          
          // 檢查會話是否有效
          if (!authService.isSessionValid()) {
            await handleSessionExpired()
          } else {
            startSessionTimer()
          }
        }
      } catch (err) {
        console.error('檢查認證狀態失敗:', err)
        error.value = '檢查登入狀態時發生錯誤'
      }
    }

    /**
     * 處理登入
     */
    const handleLogin = async () => {
      if (isLoading.value) return
      
      isLoading.value = true
      loadingMessage.value = '正在重定向到登入頁面...'
      error.value = null
      
      try {
        loadingMessage.value = '正在驗證身份...'
        const userData = await authService.login()
        
        loadingMessage.value = '正在載入用戶資訊...'
        user.value = userData
        isAuthenticated.value = true
        session.value = authService.getSession()
        
        // 同步到 authStore
        await authStore.login()
        
        loadingMessage.value = '登入成功！'
        
        // 短暫顯示成功訊息後自動進入聊天室
        setTimeout(() => {
          handleContinue()
        }, 1000)
        
      } catch (err) {
        console.error('登入失敗:', err)
        // 處理常見的 AAD 錯誤
        if (err.message?.includes('user_cancelled')) {
          error.value = '您取消了登入流程'
        } else if (err.message?.includes('consent_required')) {
          error.value = '需要管理員同意才能使用此應用程式'
        } else if (err.message?.includes('此服務僅限公司員工使用')) {
          error.value = '此服務僅限公司員工使用，請使用公司帳號登入'
        } else {
          error.value = err.message || '登入失敗，請稍後再試'
        }
      } finally {
        isLoading.value = false
        loadingMessage.value = ''
      }
    }

    /**
     * 處理登出
     */
    const handleLogout = async () => {
      if (isLoading.value) return
      
      isLoading.value = true
      loadingMessage.value = '正在登出...'
      
      try {
        await authService.logout()
        await authStore.logout()
        
        // 清除本地狀態
        isAuthenticated.value = false
        user.value = null
        session.value = null
        
        stopSessionTimer()
        
      } catch (err) {
        console.error('登出失敗:', err)
        error.value = err.message || '登出失敗'
      } finally {
        isLoading.value = false
        loadingMessage.value = ''
      }
    }

    /**
     * 繼續到聊天室
     */
    const handleContinue = async () => {
      if (isAuthenticated.value) {
        try {
          // 使用安全導航避免重複導航錯誤
          const { safeNavigate } = await import('@/utils/routerUtils')
          await safeNavigate('/chat', { replace: true })
        } catch (error) {
          console.error('導航到聊天室失敗:', error)
          // 回退到直接導航
          if (!error.message?.includes('Avoided redundant navigation')) {
            router.push('/chat').catch(err => {
              if (!err.message?.includes('Avoided redundant navigation')) {
                console.error('回退導航也失敗:', err)
              }
            })
          }
        }
      }
    }

    /**
     * 處理會話過期
     */
    const handleSessionExpired = async () => {
      try {
        await authService.refreshSession()
        session.value = authService.getSession()
        await checkAuthStatus()
      } catch (err) {
        console.error('會話過期:', err)
        error.value = '會話已過期，請重新登入'
        isAuthenticated.value = false
        user.value = null
        session.value = null
      }
    }

    /**
     * 清除錯誤訊息
     */
    const clearError = () => {
      error.value = null
    }

    /**
     * 開始會話監控定時器
     */
    const startSessionTimer = () => {
      stopSessionTimer()
      
      sessionTimer = window.setInterval(async () => {
        if (!authService.isSessionValid()) {
          await handleSessionExpired()
        } else {
          // 更新會話資訊
          session.value = authService.getSession()
          
          // 檢查 token 是否即將過期
          try {
            const isExpiring = await authService.isTokenExpiringSoon(10) // 10 分鐘內過期
            if (isExpiring) {
              console.log('[Login] Token 即將過期，嘗試自動刷新')
              await authService.forceRefreshToken()
              session.value = authService.getSession()
            }
          } catch (err) {
            console.warn('[Login] 檢查 token 過期狀態失敗:', err)
          }
        }
      }, 60000) // 每分鐘檢查一次
    }

    /**
     * 停止會話監控定時器
     */
    const stopSessionTimer = () => {
      if (sessionTimer) {
        clearInterval(sessionTimer)
        sessionTimer = null
      }
    }

    /**
     * 顯示隱私政策
     */
    const showPrivacyPolicy = () => {
      showPrivacyModal.value = true
    }

    /**
     * 顯示服務條款
     */
    const showTermsOfService = () => {
      // 實作服務條款顯示
      alert('服務條款功能待實作')
    }

    /**
     * 顯示技術支援
     */
    const showSupport = () => {
      // 實作技術支援聯絡
      alert('技術支援功能待實作')
    }

    // 生命週期
    onMounted(async () => {
      await checkAuthStatus()
      
      // 如果已經登入，直接跳轉到聊天室
      if (isAuthenticated.value) {
        router.push('/chat')
      }
    })

    onUnmounted(() => {
      stopSessionTimer()
    })

    return {
      // 狀態
      isLoading,
      loadingMessage,
      error,
      isAuthenticated,
      user,
      session,
      showPrivacyModal,
      
      // 計算屬性
      userDisplayName,
      userEmail,
      userDepartment,
      userInitials,
      remainingSessionTime,
      sessionProgressPercent,
      
      // 方法
      handleLogin,
      handleLogout,
      handleContinue,
      clearError,
      showPrivacyPolicy,
      showTermsOfService,
      showSupport
    }
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #008787 0%, #005555 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 135, 135, 0.2);
  max-width: 480px;
  width: 100%;
  text-align: center;
}

.login-header {
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 16px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.login-status {
  margin-bottom: 40px;
}

/* 載入狀態 */
.status-loading {
  padding: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #008787;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 錯誤狀態 */
.status-error {
  padding: 20px;
  background: #fed7d7;
  border-radius: 8px;
  margin-bottom: 20px;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.error-message {
  color: #c53030;
  margin: 0 0 16px 0;
}

.retry-button {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #c53030;
}

/* 準備登入狀態 */
.welcome-message {
  margin-bottom: 32px;
}

.welcome-message h2 {
  font-size: 24px;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.welcome-message p {
  color: #718096;
  margin: 0;
}

.features {
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  text-align: left;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #f7fafc;
}

.feature-icon {
  font-size: 24px;
  margin-right: 16px;
}

.feature-text h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.feature-text p {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.login-button {
  background: #008787;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  background: #006666;
  transform: translateY(-2px);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  font-size: 18px;
}

/* 已認證狀態 */
.status-authenticated {
  text-align: left;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f7fafc;
  border-radius: 12px;
  margin-bottom: 20px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #008787;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  margin-right: 16px;
}

.user-details h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.user-details p {
  font-size: 14px;
  color: #718096;
  margin: 0 0 2px 0;
}

.user-department {
  color: #008787 !important;
  font-weight: 500;
}

.session-info {
  margin-bottom: 24px;
}

.session-text {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
}

.session-progress {
  background: #e2e8f0;
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.3s;
}

.session-progress.session-warning {
  background: #fed7d7;
}

.session-bar {
  background: #008787;
  height: 100%;
  transition: all 0.3s;
}

.session-progress.session-warning .session-bar {
  background: #e53e3e;
}

.session-alert {
  margin-top: 12px;
  padding: 8px 12px;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 6px;
  font-size: 14px;
  color: #c53030;
  text-align: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.continue-button {
  flex: 1;
  background: #008787;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.continue-button:hover {
  background: #006666;
}

.logout-button {
  background: #e2e8f0;
  color: #718096;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-button:hover {
  background: #cbd5e0;
  color: #2d3748;
}

/* 頁腳 */
.login-footer {
  margin-top: 40px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.login-footer p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: white;
}

/* 彈窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #2d3748;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #2d3748;
}

.modal-body {
  padding: 24px;
}

.modal-body ul {
  padding-left: 20px;
}

.modal-body li {
  margin-bottom: 8px;
  color: #4a5568;
}

/* 響應式設計 */
@media (max-width: 600px) {
  .login-card {
    padding: 24px;
    margin: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 12px;
  }
}
</style>