"""
OpenAI 客戶端單元測試
測試 OpenAI ChatGPT API 整合功能
"""

import pytest
import json
from unittest.mock import AsyncMock, Mock, patch, MagicMock
import httpx
from datetime import datetime

from app.clients.openai_client import OpenAIClient, OpenAIClientError


class TestOpenAIClient:
    """OpenAI 客戶端測試類"""

    @pytest.fixture
    def openai_client(self):
        """創建 OpenAI 客戶端實例"""
        with patch.dict('os.environ', {
            'OPENAI_API_KEY': 'test_api_key',
            'OPENAI_MODEL': 'gpt-3.5-turbo',
            'OPENAI_MAX_TOKENS': '1000',
            'OPENAI_TEMPERATURE': '0.7'
        }):
            return OpenAIClient()

    @pytest.fixture
    def mock_httpx_client(self):
        """模擬 HTTPX 客戶端"""
        mock_client = AsyncMock(spec=httpx.AsyncClient)
        return mock_client

    @pytest.fixture
    def sample_openai_response(self):
        """範例 OpenAI API 回應"""
        return {
            "id": "chatcmpl-123",
            "object": "chat.completion",
            "created": 1677652288,
            "model": "gpt-3.5-turbo",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "這是一個測試回應"
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 30,
                "total_tokens": 50
            }
        }

    @pytest.fixture
    def sample_stream_response(self):
        """範例串流回應"""
        return [
            {
                "id": "chatcmpl-123",
                "object": "chat.completion.chunk",
                "choices": [{"delta": {"content": "這是"}}]
            },
            {
                "id": "chatcmpl-123", 
                "object": "chat.completion.chunk",
                "choices": [{"delta": {"content": "一個"}}]
            },
            {
                "id": "chatcmpl-123",
                "object": "chat.completion.chunk", 
                "choices": [{"delta": {"content": "測試"}}]
            },
            {
                "id": "chatcmpl-123",
                "object": "chat.completion.chunk",
                "choices": [{"finish_reason": "stop"}]
            }
        ]

    @pytest.mark.asyncio
    async def test_chat_completion_success(self, openai_client, mock_httpx_client, sample_openai_response):
        """測試成功的聊天完成請求"""
        # 設定模擬回應
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_openai_response
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            result = await openai_client.chat_completion(messages)
            
            assert result is not None
            assert result["content"] == "這是一個測試回應"
            assert result["model"] == "gpt-3.5-turbo"
            assert result["usage"]["total_tokens"] == 50
            
            # 驗證 API 呼叫
            mock_httpx_client.post.assert_called_once()
            call_args = mock_httpx_client.post.call_args
            assert "https://api.openai.com/v1/chat/completions" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_chat_completion_with_parameters(self, openai_client, mock_httpx_client, sample_openai_response):
        """測試帶參數的聊天完成請求"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_openai_response
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            result = await openai_client.chat_completion(
                messages=messages,
                temperature=0.8,
                max_tokens=1500,
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )
            
            assert result is not None
            
            # 檢查請求參數
            call_args = mock_httpx_client.post.call_args
            request_data = json.loads(call_args[1]["content"])
            assert request_data["temperature"] == 0.8
            assert request_data["max_tokens"] == 1500
            assert request_data["top_p"] == 0.9

    @pytest.mark.asyncio
    async def test_chat_completion_api_error(self, openai_client, mock_httpx_client):
        """測試 API 錯誤處理"""
        # 模擬 API 錯誤回應
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "error": {
                "message": "Invalid request",
                "type": "invalid_request_error",
                "code": "invalid_request"
            }
        }
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            with pytest.raises(OpenAIClientError) as exc_info:
                await openai_client.chat_completion(messages)
            
            assert "Invalid request" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_chat_completion_rate_limit(self, openai_client, mock_httpx_client):
        """測試速率限制錯誤處理"""
        mock_response = Mock()
        mock_response.status_code = 429
        mock_response.json.return_value = {
            "error": {
                "message": "Rate limit exceeded",
                "type": "rate_limit_error"
            }
        }
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            with pytest.raises(OpenAIClientError) as exc_info:
                await openai_client.chat_completion(messages)
            
            assert "rate limit" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_chat_completion_retry_mechanism(self, openai_client, mock_httpx_client, sample_openai_response):
        """測試重試機制"""
        # 第一次失敗，第二次成功
        mock_response_fail = Mock()
        mock_response_fail.status_code = 500
        mock_response_fail.json.return_value = {"error": {"message": "Internal server error"}}
        
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = sample_openai_response
        
        mock_httpx_client.post.side_effect = [mock_response_fail, mock_response_success]
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            result = await openai_client.chat_completion(messages)
            
            assert result is not None
            assert result["content"] == "這是一個測試回應"
            # 驗證重試了兩次
            assert mock_httpx_client.post.call_count == 2

    @pytest.mark.asyncio
    async def test_stream_completion(self, openai_client, mock_httpx_client, sample_stream_response):
        """測試串流完成功能"""
        # 模擬串流回應
        async def mock_stream():
            for chunk in sample_stream_response:
                yield f"data: {json.dumps(chunk)}\n\n"
            yield "data: [DONE]\n\n"
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = mock_stream()
        mock_httpx_client.stream.return_value.__aenter__.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            chunks = []
            async for chunk in openai_client.stream_completion(messages):
                chunks.append(chunk)
            
            assert len(chunks) == 3  # 排除結束標記
            assert chunks[0]["content"] == "這是"
            assert chunks[1]["content"] == "一個"
            assert chunks[2]["content"] == "測試"

    @pytest.mark.asyncio
    async def test_is_healthy_success(self, openai_client, mock_httpx_client):
        """測試健康檢查成功"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"object": "list", "data": []}
        mock_httpx_client.get.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            is_healthy = await openai_client.is_healthy()
            
            assert is_healthy is True

    @pytest.mark.asyncio
    async def test_is_healthy_failure(self, openai_client, mock_httpx_client):
        """測試健康檢查失敗"""
        mock_httpx_client.get.side_effect = httpx.RequestError("Connection failed")
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            is_healthy = await openai_client.is_healthy()
            
            assert is_healthy is False

    @pytest.mark.asyncio
    async def test_get_health_metrics(self, openai_client, mock_httpx_client):
        """測試獲取健康指標"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"object": "list", "data": []}
        mock_httpx_client.get.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            # 模擬回應時間測量
            with patch('time.time', side_effect=[1000.0, 1001.2]):
                health = await openai_client.get_health()
                
                assert health["status"] == "healthy"
                assert health["response_time"] > 0
                assert health["error_rate"] >= 0

    @pytest.mark.asyncio
    async def test_get_models_list(self, openai_client, mock_httpx_client):
        """測試獲取模型列表"""
        mock_models_response = {
            "object": "list",
            "data": [
                {
                    "id": "gpt-3.5-turbo",
                    "object": "model",
                    "owned_by": "openai"
                },
                {
                    "id": "gpt-4",
                    "object": "model", 
                    "owned_by": "openai"
                }
            ]
        }
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_models_response
        mock_httpx_client.get.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            models = await openai_client.get_models()
            
            assert len(models) == 2
            assert "gpt-3.5-turbo" in [m["id"] for m in models]
            assert "gpt-4" in [m["id"] for m in models]

    @pytest.mark.asyncio
    async def test_calculate_tokens(self, openai_client):
        """測試 Token 計算功能"""
        text = "這是一個測試訊息"
        
        # 模擬 tiktoken
        with patch('tiktoken.encoding_for_model') as mock_tiktoken:
            mock_encoder = Mock()
            mock_encoder.encode.return_value = [1, 2, 3, 4, 5]  # 5 個 tokens
            mock_tiktoken.return_value = mock_encoder
            
            token_count = openai_client.calculate_tokens(text)
            
            assert token_count == 5

    @pytest.mark.asyncio
    async def test_estimate_cost(self, openai_client):
        """測試成本估算功能"""
        prompt_tokens = 100
        completion_tokens = 50
        
        cost = openai_client.estimate_cost(prompt_tokens, completion_tokens)
        
        assert cost > 0
        assert isinstance(cost, (int, float))

    @pytest.mark.asyncio
    async def test_validate_messages(self, openai_client):
        """測試訊息驗證功能"""
        # 有效訊息
        valid_messages = [
            {"role": "user", "content": "測試訊息"}
        ]
        assert openai_client.validate_messages(valid_messages) is True
        
        # 無效訊息 - 缺少角色
        invalid_messages = [
            {"content": "測試訊息"}
        ]
        assert openai_client.validate_messages(invalid_messages) is False
        
        # 無效訊息 - 空內容
        empty_content_messages = [
            {"role": "user", "content": ""}
        ]
        assert openai_client.validate_messages(empty_content_messages) is False

    @pytest.mark.asyncio
    async def test_format_messages(self, openai_client):
        """測試訊息格式化功能"""
        raw_messages = [
            {"role": "user", "content": "  測試訊息  "},  # 包含空格
            {"role": "assistant", "content": "回應訊息"}
        ]
        
        formatted = openai_client.format_messages(raw_messages)
        
        assert len(formatted) == 2
        assert formatted[0]["content"] == "測試訊息"  # 去除空格
        assert formatted[1]["role"] == "assistant"

    @pytest.mark.asyncio
    async def test_timeout_handling(self, openai_client, mock_httpx_client):
        """測試超時處理"""
        mock_httpx_client.post.side_effect = httpx.TimeoutException("Request timeout")
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            with pytest.raises(OpenAIClientError) as exc_info:
                await openai_client.chat_completion(messages)
            
            assert "timeout" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_network_error_handling(self, openai_client, mock_httpx_client):
        """測試網路錯誤處理"""
        mock_httpx_client.post.side_effect = httpx.NetworkError("Network unreachable")
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            with pytest.raises(OpenAIClientError) as exc_info:
                await openai_client.chat_completion(messages)
            
            assert "network" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_context_length_validation(self, openai_client):
        """測試上下文長度驗證"""
        # 創建超長的訊息
        long_content = "x" * 50000  # 超過模型上下文限制
        messages = [{"role": "user", "content": long_content}]
        
        with patch.object(openai_client, 'calculate_tokens', return_value=100000):
            with pytest.raises(OpenAIClientError) as exc_info:
                await openai_client.chat_completion(messages)
            
            assert "context length" in str(exc_info.value).lower() or "too long" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_content_filtering(self, openai_client, mock_httpx_client):
        """測試內容過濾處理"""
        # 模擬內容過濾錯誤
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "chatcmpl-123",
            "choices": [
                {
                    "message": None,
                    "finish_reason": "content_filter"
                }
            ]
        }
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            with pytest.raises(OpenAIClientError) as exc_info:
                await openai_client.chat_completion(messages)
            
            assert "content filter" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_function_calling(self, openai_client, mock_httpx_client):
        """測試函數呼叫功能"""
        # 模擬函數呼叫回應
        function_response = {
            "id": "chatcmpl-123",
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": None,
                        "function_call": {
                            "name": "get_weather",
                            "arguments": '{"location": "台北"}'
                        }
                    },
                    "finish_reason": "function_call"
                }
            ]
        }
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = function_response
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "台北天氣如何？"}]
            functions = [
                {
                    "name": "get_weather",
                    "description": "獲取天氣資訊",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string"}
                        }
                    }
                }
            ]
            
            result = await openai_client.chat_completion(messages, functions=functions)
            
            assert result is not None
            assert "function_call" in result

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, openai_client, mock_httpx_client, sample_openai_response):
        """測試並發請求處理"""
        import asyncio
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_openai_response
        mock_httpx_client.post.return_value = mock_response
        
        with patch.object(openai_client, '_http_client', mock_httpx_client):
            messages = [{"role": "user", "content": "測試訊息"}]
            
            # 同時發送多個請求
            tasks = [
                openai_client.chat_completion(messages)
                for _ in range(5)
            ]
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 5
            assert all(r["content"] == "這是一個測試回應" for r in results)

    def test_client_initialization(self):
        """測試客戶端初始化"""
        # 測試缺少 API 金鑰
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(OpenAIClientError) as exc_info:
                OpenAIClient()
            
            assert "API key" in str(exc_info.value)

    def test_client_configuration(self, openai_client):
        """測試客戶端配置"""
        assert openai_client.model == "gpt-3.5-turbo"
        assert openai_client.max_tokens == 1000
        assert openai_client.temperature == 0.7
        
        # 測試配置更新
        openai_client.update_config(
            model="gpt-4",
            temperature=0.5,
            max_tokens=2000
        )
        
        assert openai_client.model == "gpt-4"
        assert openai_client.temperature == 0.5
        assert openai_client.max_tokens == 2000


class TestOpenAIClientError:
    """OpenAI 客戶端錯誤測試類"""

    def test_openai_client_error_creation(self):
        """測試創建 OpenAI 客戶端錯誤物件"""
        error = OpenAIClientError("API 錯誤", "API_ERROR")
        
        assert str(error) == "API 錯誤"
        assert error.error_code == "API_ERROR"

    def test_openai_client_error_with_details(self):
        """測試帶詳細資訊的錯誤物件"""
        error_details = {
            "type": "rate_limit_error",
            "code": "rate_limit_exceeded"
        }
        
        error = OpenAIClientError(
            "速率限制超出",
            "RATE_LIMIT",
            error_details
        )
        
        assert error.error_details == error_details
        assert error.error_details["type"] == "rate_limit_error"