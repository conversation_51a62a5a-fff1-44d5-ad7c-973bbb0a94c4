/**
 * BPM Chatbot 前端應用程式入口
 * Vue.js 3 + JavaScript + Pinia + Vue Router + Azure AD
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 全域樣式
import '@/assets/styles/global.css'

// 初始化導航攔截器
import '@/utils/navigationInterceptor'

// 建立 Vue 應用程式實例
const app = createApp(App)

// 註冊 Pinia 狀態管理
const pinia = createPinia()
app.use(pinia)

// 註冊 Vue Router
app.use(router)

// 全域屬性
app.config.globalProperties.$version = __APP_VERSION__

// 開發模式配置
if (import.meta.env.DEV) {
  app.config.performance = true
  console.log('🚀 BMP Chatbot 開發模式啟動')
  console.log('📋 應用程式版本:', __APP_VERSION__)

  // 載入調試器
  Promise.all([
    import('@/utils/authDebug.js'),
    import('@/utils/routerDebug.js')
  ]).then(() => {
    console.log('🔧 調試器已載入')
    console.log('💡 認證調試命令:')
    console.log('   - window.authDebugger.testSessionInfo() // 測試會話資訊')
    console.log('   - window.authDebugger.testTokenRefresh() // 測試 token 刷新')
    console.log('   - window.authDebugger.runFullDiagnostic() // 完整認證診斷')
    console.log('💡 路由調試命令:')
    console.log('   - window.routerDebugger.testRedundantNavigation() // 測試重複導航')
    console.log('   - window.routerDebugger.showNavigationHistory() // 顯示導航歷史')
    console.log('   - window.routerDebugger.runFullDiagnostic() // 完整路由診斷')
    console.log('💡 導航攔截器命令:')
    console.log('   - window.navigationInterceptor.getDebugInfo() // 獲取攔截器狀態')
    console.log('   - window.navigationInterceptor.clearPendingNavigations() // 清除待處理導航')
  })
}

// 生產模式配置
if (import.meta.env.PROD) {
  app.config.errorHandler = (err, vm, info) => {
    console.error('應用程式錯誤:', err, info)
    // TODO: 發送錯誤到監控服務
  }
}

// 掛載應用程式
app.mount('#app')

// 移除載入畫面
const loadingElement = document.getElementById('loading')
if (loadingElement) {
  loadingElement.style.display = 'none'
}