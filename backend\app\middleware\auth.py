"""
Azure AD 認證中介軟體
處理 AAD token 驗證和員工身份確認
"""

import os
import logging
from typing import Optional, Dict, Any
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
from jwt.exceptions import DecodeError, ExpiredSignatureError, InvalidTokenError
import httpx
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

# 設定日誌
logger = logging.getLogger(__name__)

# HTTP Bearer Token 安全方案
security = HTTPBearer()

class AADTokenValidator:
    """Azure AD Token 驗證器"""
    
    def __init__(self):
        self.tenant_id = os.getenv("AAD_TENANT_ID")
        self.client_id = os.getenv("AAD_CLIENT_ID")
        self.authority = os.getenv("AAD_AUTHORITY")
        
        if not all([self.tenant_id, self.client_id, self.authority]):
            raise ValueError("缺少必要的 Azure AD 配置")
        
        # JWKS URI for token verification
        self.jwks_uri = f"https://login.microsoftonline.com/{self.tenant_id}/discovery/keys"
        self.issuer = f"https://sts.windows.net/{self.tenant_id}/"
        
        # 快取 JWKS 金鑰
        self._jwks_cache: Optional[Dict] = None
        self._cache_expires: float = 0
    
    async def get_jwks(self) -> Dict:
        """獲取 JWKS 金鑰（帶快取）"""
        import time
        
        current_time = time.time()
        
        # 檢查快取是否有效（1小時）
        if self._jwks_cache and current_time < self._cache_expires:
            return self._jwks_cache
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.jwks_uri)
                response.raise_for_status()
                
                self._jwks_cache = response.json()
                self._cache_expires = current_time + 3600  # 1 hour
                
                logger.info("JWKS 金鑰已更新")
                return self._jwks_cache
                
        except httpx.RequestError as e:
            logger.error(f"獲取 JWKS 失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="認證服務暫時無法使用"
            )
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """驗證 Azure AD token"""
        try:
            # 獲取 JWKS 金鑰
            jwks = await self.get_jwks()
            
            # 解碼 token header 來獲取 kid
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get("kid")
            
            if not kid:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token 缺少 kid"
                )
            
            # 尋找匹配的公鑰
            public_key = None
            for key in jwks.get("keys", []):
                if key.get("kid") == kid:
                    public_key = jwt.algorithms.RSAAlgorithm.from_jwk(key)
                    break
            
            if not public_key:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="找不到匹配的公鑰"
                )
            
            # 驗證 token
            payload = jwt.decode(
                token,
                public_key,
                algorithms=["RS256"],
                audience=self.client_id,
                issuer=self.issuer,
                options={"verify_exp": True}
            )
            
            logger.info(f"Token 驗證成功: user={payload.get('upn', 'unknown')}")
            return payload
            
        except ExpiredSignatureError:
            logger.warning("Token 已過期")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token 已過期"
            )
        except DecodeError:
            logger.warning("Token 格式錯誤")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token 格式錯誤"
            )
        except InvalidTokenError as e:
            logger.warning(f"Token 驗證失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token 驗證失敗"
            )

# 全域驗證器實例
token_validator = AADTokenValidator()

class AuthenticatedUser:
    """認證用戶資訊"""
    
    def __init__(self, token_payload: Dict[str, Any]):
        self.object_id = token_payload.get("oid")
        self.user_principal_name = token_payload.get("upn")
        self.name = token_payload.get("name")
        self.email = token_payload.get("email", self.user_principal_name)
        self.tenant_id = token_payload.get("tid")
        self.roles = token_payload.get("roles", [])
        self.groups = token_payload.get("groups", [])
        
        # 確認必要欄位
        if not all([self.object_id, self.user_principal_name]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token 缺少必要的用戶資訊"
            )
    
    @property
    def is_employee(self) -> bool:
        """檢查是否為公司員工"""
        # 這裡可以加入更複雜的員工驗證邏輯
        # 例如檢查 email domain、特定群組等
        
        if not self.email:
            return False
        
        # 檢查 email domain（範例）
        allowed_domains = os.getenv("ALLOWED_EMAIL_DOMAINS", "").split(",")
        if allowed_domains and allowed_domains != [""]:
            email_domain = self.email.split("@")[-1].lower()
            return email_domain in [domain.strip().lower() for domain in allowed_domains]
        
        # 如果沒有設定允許的 domain，則預設為允許
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "object_id": self.object_id,
            "user_principal_name": self.user_principal_name,
            "name": self.name,
            "email": self.email,
            "tenant_id": self.tenant_id,
            "roles": self.roles,
            "groups": self.groups,
            "is_employee": self.is_employee
        }

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> AuthenticatedUser:
    """
    依賴注入函數：獲取當前認證用戶
    用於保護需要認證的 API 端點
    """
    try:
        # 驗證 token
        token_payload = await token_validator.verify_token(credentials.credentials)
        
        # 建立用戶物件
        user = AuthenticatedUser(token_payload)
        
        # 檢查是否為員工
        if not user.is_employee:
            logger.warning(f"非員工嘗試存取: {user.email}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="此服務僅限公司員工使用"
            )
        
        return user
        
    except HTTPException:
        # 重新拋出 HTTP 異常
        raise
    except Exception as e:
        logger.error(f"認證過程發生錯誤: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="認證服務發生錯誤"
        )

async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[AuthenticatedUser]:
    """
    依賴注入函數：獲取可選的認證用戶
    用於不強制要求認證的端點
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None

class AuthMiddleware:
    """認證中介軟體類"""
    
    def __init__(self, require_auth: bool = True):
        self.require_auth = require_auth
    
    async def __call__(self, request: Request, call_next):
        """中介軟體處理函數"""
        
        # 檢查是否為需要認證的路徑
        protected_paths = ["/api/chat", "/api/models", "/api/user"]
        public_paths = ["/", "/health", "/docs", "/openapi.json"]
        
        path = request.url.path
        
        # 公開路徑不需要認證
        if any(path.startswith(public_path) for public_path in public_paths):
            return await call_next(request)
        
        # 受保護路徑需要認證
        if any(path.startswith(protected_path) for protected_path in protected_paths):
            auth_header = request.headers.get("Authorization")
            
            if not auth_header:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="缺少認證 token",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            # 驗證 token 格式
            if not auth_header.startswith("Bearer "):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="認證 token 格式錯誤",
                    headers={"WWW-Authenticate": "Bearer"}
                )
        
        # 繼續處理請求
        response = await call_next(request)
        return response

# 建立中介軟體實例
auth_middleware = AuthMiddleware()

# 錯誤處理函數
async def auth_exception_handler(request: Request, exc: HTTPException):
    """認證錯誤處理器"""
    
    logger.warning(f"認證失敗: {exc.detail} - Path: {request.url.path}")
    
    return {
        "error": "authentication_failed",
        "message": exc.detail,
        "status_code": exc.status_code
    }