import { CommonAuthorizationCodeRequest } from "@azure/msal-common/browser";
export type AuthorizationCodeRequest = Partial<Omit<CommonAuthorizationCodeRequest, "code" | "enableSpaAuthorizationCode" | "requestedClaimsHash">> & {
    code?: string;
    nativeAccountId?: string;
    cloudGraphHostName?: string;
    msGraphHost?: string;
    cloudInstanceHostName?: string;
};
//# sourceMappingURL=AuthorizationCodeRequest.d.ts.map