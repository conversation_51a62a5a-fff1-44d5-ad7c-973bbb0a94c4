# 認證機制改進設計文件

## 1. 設計概述

### 1.1 目標
基於現有 Azure AD 認證系統，重構前端認證架構，統一組件設計，完善會話管理，並將所有 TypeScript 語法轉換為 JavaScript 標準。

### 1.2 核心原則
- **統一性**：統一 LoginView 和 LoginComponent 的架構
- **標準化**：全面採用 JavaScript (ES2022+) + JSDoc 語法
- **會話管理**：完善 token 刷新和會話監控機制
- **用戶體驗**：無縫登入體驗和清晰的錯誤處理
- **狀態同步**：authStore 與 authService 狀態一致性

## 2. 系統架構設計

### 2.1 整體架構

```mermaid
graph TB
    subgraph "Vue 3 前端應用"
        A1[LoginView.vue] --> A2[LoginComponent.vue]
        A2 --> A3[authService.js]
        A3 --> A4[authStore.js]
        A4 --> A5[其他組件]
    end
    
    subgraph "認證服務層"
        B1[AuthService 類別]
        B2[會話管理器]
        B3[Token 刷新器]
        B4[狀態同步器]
    end
    
    subgraph "Azure AD"
        C1[MSAL Browser]
        C2[Token 端點]
        C3[用戶資訊]
    end
    
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B1 --> C1
    C1 --> C2
    C1 --> C3
    
    B4 --> A4
    B3 --> A4
```

### 2.2 組件關係重構

**現況問題**：
- LoginView.vue 與 LoginComponent.vue 功能重疊
- LoginComponent 調用不存在的 authService 方法
- 狀態管理分散且不一致

**重構設計**：
```javascript
// 新架構：統一單一登入組件
LoginView.vue (頁面)
└── 使用 Composition API
    ├── 導入 authService
    ├── 導入 authStore  
    └── 統一所有登入邏輯
```

## 3. 詳細技術設計

### 3.1 認證服務 (AuthService) 增強

#### 3.1.1 新增缺失方法

```javascript
/**
 * 會話管理相關方法 - 補充 LoginComponent 所需功能
 */
class AuthService {
  /**
   * 獲取會話資訊
   * @returns {Object|null} 會話物件包含 created_at, expires_at 等
   */
  getSession() {
    // 實作邏輯
  }
  
  /**
   * 檢查會話是否有效
   * @returns {boolean} 會話有效性
   */
  isSessionValid() {
    // 實作邏輯
  }
  
  /**
   * 刷新會話
   * @returns {Promise<Object>} 新的會話資訊
   */
  refreshSession() {
    // 實作邏輯
  }
  
  /**
   * 檢查 token 是否即將過期
   * @param {number} minutesBefore - 提前幾分鐘檢查
   * @returns {Promise<boolean>} 是否即將過期
   */
  isTokenExpiringSoon(minutesBefore) {
    // 實作邏輯
  }
  
  /**
   * 強制刷新 token
   * @returns {Promise<string>} 新的 access token
   */
  forceRefreshToken() {
    // 實作邏輯
  }
}
```

#### 3.1.2 會話時間管理設計

```javascript
/**
 * 會話時間計算和監控
 */
class SessionManager {
  /**
   * 會話資料結構
   * @typedef {Object} SessionInfo
   * @property {Date} created_at - 創建時間
   * @property {Date} expires_at - 過期時間  
   * @property {Date} last_activity - 最後活動時間
   * @property {number} duration - 會話時長（秒）
   * @property {boolean} is_valid - 是否有效
   */
  
  /**
   * 創建新會話
   * @param {Object} tokenResponse - MSAL token 回應
   * @returns {SessionInfo} 會話資訊
   */
  createSession(tokenResponse) {
    const now = new Date()
    const expiresAt = new Date(tokenResponse.expiresOn)
    
    return {
      created_at: now,
      expires_at: expiresAt,
      last_activity: now,
      duration: Math.floor((expiresAt - now) / 1000),
      is_valid: true,
      token_expires_on: tokenResponse.expiresOn,
      refresh_threshold: 10 * 60 * 1000 // 10分鐘前刷新
    }
  }
  
  /**
   * 計算剩餘時間百分比
   * @param {SessionInfo} session - 會話資訊
   * @returns {number} 剩餘時間百分比 (0-100)
   */
  calculateProgressPercent(session) {
    if (!session?.created_at || !session?.expires_at) return 0
    
    const now = new Date()
    const total = session.expires_at.getTime() - session.created_at.getTime()
    const used = now.getTime() - session.created_at.getTime()
    
    if (total <= 0) return 0
    return Math.max(0, Math.min(100, 100 - (used / total) * 100))
  }
}
```

### 3.2 組件統一設計

#### 3.2.1 LoginView.vue 重構

**原有問題**：
- 使用 `<script setup lang="ts">` TypeScript 語法
- 功能與 LoginComponent 重複

**重構方案**：
```vue
<!-- LoginView.vue - 統一為主要登入組件 -->
<script>
import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import authService from '@/services/authService'

/**
 * 登入視圖組件
 * 統一處理所有登入相關邏輯和 UI
 */
export default defineComponent({
  name: 'LoginView',
  setup() {
    // 組件狀態和邏輯
    return {
      // 導出的響應式資料和方法
    }
  }
})
</script>
```

#### 3.2.2 移除重複組件

**策略**：
- 保留 LoginView.vue 作為主要登入頁面
- 將 LoginComponent.vue 的完整功能整合到 LoginView.vue
- 移除或重構 LoginComponent.vue 為更簡單的可復用組件

### 3.3 狀態管理統一

#### 3.3.1 authStore.js 與 authService 同步

**現有問題**：
```javascript
// authStore.js - 計算屬性依賴 authService 但狀態不同步
const state = computed(() => ({
  isAuthenticated: authService.isAuthenticated(), // authService 狀態
  isLoading: isLoading.value,                    // store 內部狀態
  user: authService.getCurrentUser(),            // authService 狀態
}))
```

**重構設計**：
```javascript
/**
 * 統一狀態管理 - authStore.js
 */
export const useAuthStore = defineStore('auth', () => {
  // 單一資料來源：所有狀態從 authService 獲取
  const authState = ref(authService.getAuthState())
  
  /**
   * 從 authService 同步狀態
   */
  const syncWithService = () => {
    authState.value = authService.getAuthState()
  }
  
  /**
   * 計算屬性基於統一狀態
   */
  const isAuthenticated = computed(() => authState.value.isAuthenticated)
  const currentUser = computed(() => authState.value.user)
  const isLoading = computed(() => authState.value.isLoading)
  
  return {
    // 狀態
    isAuthenticated,
    currentUser,
    isLoading,
    // 方法
    syncWithService,
    // ... 其他方法
  }
})
```

### 3.4 錯誤處理設計

#### 3.4.1 統一錯誤類型

```javascript
/**
 * 認證錯誤類型定義
 */
const AuthErrorTypes = {
  USER_CANCELLED: 'user_cancelled',
  CONSENT_REQUIRED: 'consent_required', 
  INTERACTION_IN_PROGRESS: 'interaction_in_progress',
  SESSION_EXPIRED: 'session_expired',
  NETWORK_ERROR: 'network_error',
  CONFIGURATION_ERROR: 'configuration_error',
  UNKNOWN_ERROR: 'unknown_error'
}

/**
 * 錯誤處理器
 */
class AuthErrorHandler {
  /**
   * 處理登入錯誤
   * @param {Error} error - 原始錯誤
   * @returns {Object} 格式化的錯誤資訊
   */
  static handleLoginError(error) {
    const errorInfo = {
      type: AuthErrorTypes.UNKNOWN_ERROR,
      message: '登入失敗，請稍後再試',
      detail: error.message,
      canRetry: true,
      showContact: false
    }
    
    if (error.errorCode === 'user_cancelled') {
      errorInfo.type = AuthErrorTypes.USER_CANCELLED
      errorInfo.message = '您取消了登入流程'
      errorInfo.canRetry = true
    }
    // ... 其他錯誤類型處理
    
    return errorInfo
  }
}
```

### 3.5 TypeScript 轉 JavaScript 標準

#### 3.5.1 語法轉換規範

**TypeScript 語法**：
```typescript
<script setup lang="ts">
import { ref, Ref } from 'vue'

const isLoading: Ref<boolean> = ref(false)
const user: Ref<User | null> = ref(null)

interface User {
  name: string
  email: string
}

const handleLogin = async (): Promise<void> => {
  // 邏輯
}
</script>
```

**JavaScript + JSDoc 轉換**：
```javascript
<script>
import { defineComponent, ref } from 'vue'

/**
 * 用戶資料結構
 * @typedef {Object} User
 * @property {string} name - 用戶名稱
 * @property {string} email - 電子郵件
 */

export default defineComponent({
  name: 'ComponentName',
  setup() {
    /** @type {import('vue').Ref<boolean>} */
    const isLoading = ref(false)
    
    /** @type {import('vue').Ref<User|null>} */
    const user = ref(null)
    
    /**
     * 處理登入
     * @returns {Promise<void>}
     */
    const handleLogin = async () => {
      // 邏輯
    }
    
    return {
      isLoading,
      user,
      handleLogin
    }
  }
})
</script>
```

## 4. 實作計劃

### 4.1 階段一：核心服務重構
1. **authService.js 增強**
   - 新增 `getSession()`, `isSessionValid()`, `refreshSession()` 方法
   - 實作 `SessionManager` 類別
   - 完善 token 自動刷新邏輯

2. **錯誤處理統一**
   - 建立 `AuthErrorHandler` 類別
   - 統一錯誤訊息格式

### 4.2 階段二：組件重構
1. **LoginView.vue 重寫**
   - 轉換為純 JavaScript 語法
   - 整合 LoginComponent 的完整功能
   - 實作會話時間顯示和監控

2. **LoginComponent.vue 處理**
   - 評估是否保留為簡化版組件
   - 或完全整合到 LoginView.vue

### 4.3 階段三：狀態管理統一
1. **authStore.js 重構**
   - 實作狀態同步機制
   - 統一資料來源
   - 移除重複的狀態管理

### 4.4 階段四：整合測試
1. **功能驗證**
   - 登入流程完整性
   - 會話管理正確性
   - 錯誤處理有效性

2. **相容性測試**
   - Azure AD 整合
   - 多瀏覽器支援
   - 網路異常處理

## 5. 資料結構設計

### 5.1 會話資料結構

```javascript
/**
 * 會話資訊資料結構
 * @typedef {Object} SessionInfo
 * @property {Date} created_at - 會話創建時間
 * @property {Date} expires_at - 會話過期時間
 * @property {Date} last_activity - 最後活動時間
 * @property {number} duration - 總時長（毫秒）
 * @property {boolean} is_valid - 是否有效
 * @property {Date} token_expires_on - Token 過期時間
 * @property {number} refresh_threshold - 刷新閾值（毫秒）
 * @property {string} session_id - 會話識別碼
 */
```

### 5.2 認證狀態資料結構

```javascript
/**
 * 認證狀態資料結構
 * @typedef {Object} AuthState
 * @property {boolean} isAuthenticated - 是否已認證
 * @property {boolean} isInitialized - 是否已初始化
 * @property {boolean} isLoading - 是否載入中
 * @property {User|null} user - 用戶資訊
 * @property {SessionInfo|null} session - 會話資訊
 * @property {string|null} error - 錯誤訊息
 * @property {boolean} hasValidSession - 是否有有效會話
 */
```

## 6. 技術實作細節

### 6.1 會話監控機制

```javascript
/**
 * 會話監控器
 */
class SessionMonitor {
  constructor(authService, authStore) {
    this.authService = authService
    this.authStore = authStore
    this.monitorInterval = null
  }
  
  /**
   * 開始監控會話
   * @param {number} intervalMs - 監控間隔（毫秒）
   */
  startMonitoring(intervalMs = 60000) {
    this.stopMonitoring()
    
    this.monitorInterval = setInterval(async () => {
      try {
        const session = this.authService.getSession()
        if (!session || !this.authService.isSessionValid()) {
          await this.handleSessionExpired()
          return
        }
        
        // 檢查是否需要刷新 token
        if (await this.authService.isTokenExpiringSoon(10)) {
          await this.authService.forceRefreshToken()
          this.authStore.syncWithService()
        }
      } catch (error) {
        console.warn('會話監控錯誤:', error)
      }
    }, intervalMs)
  }
  
  /**
   * 停止監控會話
   */
  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
  }
  
  /**
   * 處理會話過期
   */
  async handleSessionExpired() {
    try {
      await this.authService.refreshSession()
      this.authStore.syncWithService()
    } catch (error) {
      // 會話無法恢復，需要重新登入
      this.authService.clearLocalSession()
      this.authStore.syncWithService()
      // 可以觸發重新登入流程或顯示登入頁面
    }
  }
}
```

### 6.2 自動 Token 刷新策略

```javascript
/**
 * Token 刷新管理器
 */
class TokenRefreshManager {
  constructor(msalInstance) {
    this.msalInstance = msalInstance
    this.refreshPromise = null
  }
  
  /**
   * 檢查並刷新 token
   * @param {Object} account - MSAL 帳戶物件
   * @param {number} thresholdMinutes - 提前刷新的分鐘數
   * @returns {Promise<string>} 新的 access token
   */
  async checkAndRefresh(account, thresholdMinutes = 10) {
    if (!account) {
      throw new Error('帳戶資訊不存在')
    }
    
    // 防止重複刷新請求
    if (this.refreshPromise) {
      return this.refreshPromise
    }
    
    try {
      const tokenRequest = {
        scopes: ["openid", "profile", "User.Read", "email"],
        account: account
      }
      
      this.refreshPromise = this.msalInstance.acquireTokenSilent(tokenRequest)
      const response = await this.refreshPromise
      
      return response.accessToken
    } catch (error) {
      // 靜默刷新失敗，嘗試交互式刷新
      if (error instanceof InteractionRequiredAuthError) {
        const response = await this.msalInstance.acquireTokenPopup(tokenRequest)
        return response.accessToken
      }
      throw error
    } finally {
      this.refreshPromise = null
    }
  }
}
```

## 7. 性能優化

### 7.1 載入性能
- 延遲載入非關鍵認證組件
- 快取 Azure AD 配置資訊
- 最佳化 MSAL 初始化流程

### 7.2 記憶體管理
- 適時清理事件監聽器
- 避免會話監控記憶體洩漏
- 清理過期的 token 資訊

### 7.3 網路最佳化
- 實作 token 刷新失敗重試機制
- 優化 API 請求頻率
- 處理網路連接異常

## 8. 安全性考量

### 8.1 Token 安全
- 避免在 console 輸出敏感資訊
- 適時清理記憶體中的 token
- 實作 token 洩漏檢測

### 8.2 會話安全
- 實作會話過期自動處理
- 防止會話固定攻擊
- 多標籤頁會話同步

### 8.3 錯誤資訊安全
- 過濾敏感錯誤資訊
- 避免暴露系統內部資訊
- 記錄安全事件供審計

## 9. 測試策略

### 9.1 單元測試
- AuthService 方法測試
- SessionManager 邏輯測試
- 錯誤處理機制測試

### 9.2 整合測試
- Azure AD 登入流程
- Token 刷新機制
- 會話管理功能

### 9.3 E2E 測試
- 完整登入登出流程
- 會話過期處理
- 多種錯誤場景

這個設計文件涵蓋了認證機制的完整重構方案，確保符合 JavaScript 標準並解決現有的架構問題。