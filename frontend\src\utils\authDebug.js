/**
 * 認證調試工具
 * 用於測試和調試認證相關功能
 */

import authService from '@/services/authService'

export class AuthDebugger {
  constructor() {
    this.logPrefix = '[AuthDebug]'
  }

  /**
   * 測試會話資訊獲取
   */
  async testSessionInfo() {
    console.group(`${this.logPrefix} 測試會話資訊獲取`)
    
    try {
      // 檢查認證狀態
      const isAuth = authService.isAuthenticated()
      console.log('認證狀態:', isAuth)
      
      if (!isAuth) {
        console.warn('用戶未認證，無法測試會話資訊')
        console.groupEnd()
        return
      }

      // 獲取會話資訊
      const session = authService.getSession()
      console.log('會話資訊:', session)

      if (session) {
        console.log('會話來源:', session.source)
        console.log('過期時間:', session.expires_at)
        console.log('是否有效:', session.is_valid)
        console.log('剩餘時間:', Math.round(session.duration / 1000 / 60), '分鐘')
      }

      // 測試 token 過期檢查
      const isExpiringSoon = await authService.isTokenExpiringSoon(10)
      console.log('Token 是否即將過期 (10分鐘內):', isExpiringSoon)

      // 測試 token 過期時間獲取
      const expirationTime = await authService.getTokenExpirationTime()
      console.log('Token 過期時間:', expirationTime)

      // 測試是否需要刷新
      const shouldRefresh = await authService.shouldRefreshToken(10)
      console.log('是否需要刷新 Token:', shouldRefresh)

    } catch (error) {
      console.error('測試會話資訊時發生錯誤:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 測試 token 刷新
   */
  async testTokenRefresh() {
    console.group(`${this.logPrefix} 測試 Token 刷新`)
    
    try {
      const isAuth = authService.isAuthenticated()
      if (!isAuth) {
        console.warn('用戶未認證，無法測試 token 刷新')
        console.groupEnd()
        return
      }

      console.log('開始測試 token 刷新...')
      
      // 記錄刷新前的狀態
      const beforeSession = authService.getSession()
      console.log('刷新前會話:', beforeSession)

      // 執行刷新
      const result = await authService.checkAndRefreshToken()
      console.log('刷新結果:', result ? '成功' : '失敗')

      // 記錄刷新後的狀態
      const afterSession = authService.getSession()
      console.log('刷新後會話:', afterSession)

      if (beforeSession && afterSession) {
        const timeDiff = afterSession.expires_at.getTime() - beforeSession.expires_at.getTime()
        console.log('過期時間變化:', timeDiff / 1000 / 60, '分鐘')
      }

    } catch (error) {
      console.error('測試 token 刷新時發生錯誤:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 檢查 localStorage 中的 MSAL 資料
   */
  inspectMSALCache() {
    console.group(`${this.logPrefix} 檢查 MSAL 快取`)
    
    try {
      const msalKeys = Object.keys(localStorage).filter(key => 
        key.includes('msal') || 
        key.includes('accesstoken') || 
        key.includes('idtoken') ||
        key.includes('refreshtoken')
      )

      console.log('MSAL 相關的 localStorage 鍵:', msalKeys)

      msalKeys.forEach(key => {
        try {
          const value = localStorage.getItem(key)
          const parsed = JSON.parse(value)
          console.log(`${key}:`, parsed)
        } catch (e) {
          console.log(`${key}:`, value)
        }
      })

    } catch (error) {
      console.error('檢查 MSAL 快取時發生錯誤:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 獲取當前用戶資訊
   */
  getCurrentUserInfo() {
    console.group(`${this.logPrefix} 當前用戶資訊`)
    
    try {
      const user = authService.getCurrentUser()
      console.log('當前用戶:', user)

      if (user) {
        console.log('用戶 ID:', user.localAccountId)
        console.log('用戶名:', user.username)
        console.log('顯示名稱:', user.name)
        console.log('ID Token Claims:', user.idTokenClaims)
      }

      const authState = authService.getAuthState()
      console.log('認證狀態:', authState)

    } catch (error) {
      console.error('獲取用戶資訊時發生錯誤:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 執行完整的認證診斷
   */
  async runFullDiagnostic() {
    console.group(`${this.logPrefix} 完整認證診斷`)
    
    console.log('開始認證系統診斷...')
    
    // 基本資訊
    this.getCurrentUserInfo()
    
    // 會話資訊
    await this.testSessionInfo()
    
    // MSAL 快取
    this.inspectMSALCache()
    
    // Token 刷新測試
    await this.testTokenRefresh()
    
    console.log('認證系統診斷完成')
    console.groupEnd()
  }
}

// 創建全域實例
export const authDebugger = new AuthDebugger()

// 在開發環境下將調試器添加到 window 對象
if (import.meta.env.DEV) {
  window.authDebugger = authDebugger
  console.log('🔧 認證調試器已載入，使用 window.authDebugger 進行調試')
}

export default authDebugger
