{"version": 3, "file": "BrowserCrypto.mjs", "sources": ["../../src/crypto/BrowserCrypto.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.nonBrowserEnvironment", "BrowserAuthErrorCodes.cryptoNonExistent"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAaH;;;AAGG;AAEH;;AAEG;AACH;AACA,MAAM,oBAAoB,GAAG,mBAAmB,CAAC;AACjD;AACA,MAAM,aAAa,GAAG,SAAS,CAAC;AAChC;AACA,MAAM,cAAc,GAAG,IAAI,CAAC;AAC5B;AACA,MAAM,eAAe,GAAe,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACvE;AACA,MAAM,UAAU,GAAG,kBAAkB,CAAC;AACtC;AACA,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAEtC,MAAM,eAAe,GAAG,yBAAyB,CAAC;AAElD,MAAM,sBAAsB,GAA0B;AAClD,IAAA,IAAI,EAAE,oBAAoB;AAC1B,IAAA,IAAI,EAAE,aAAa;AACnB,IAAA,aAAa,EAAE,cAAc;AAC7B,IAAA,cAAc,EAAE,eAAe;CAClC,CAAC;AAEF;;AAEG;AACG,SAAU,uBAAuB,CACnC,wBAAiC,EAAA;IAEjC,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,MAAM,sBAAsB,CACxBA,qBAA2C,CAC9C,CAAC;AACL,KAAA;AACD,IAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAChB,QAAA,MAAM,sBAAsB,CAACC,iBAAuC,CAAC,CAAC;AACzE,KAAA;IACD,IAAI,CAAC,wBAAwB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;QACpD,MAAM,sBAAsB,CACxBA,iBAAuC,EACvC,eAAe,CAClB,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;;AAKG;AACI,eAAe,YAAY,CAC9B,UAAkB,EAClB,iBAAsC,EACtC,aAAsB,EAAA;IAEtB,iBAAiB,EAAE,mBAAmB,CAClC,iBAAiB,CAAC,YAAY,EAC9B,aAAa,CAChB,CAAC;AACF,IAAA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACxC,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAC9B,aAAa,EACb,IAAI,CACiB,CAAC;AAC9B,CAAC;AAED;;;AAGG;AACG,SAAU,eAAe,CAAC,UAAsB,EAAA;IAClD,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACrD,CAAC;AAED;;;AAGG;AACH,SAAS,eAAe,GAAA;AACpB,IAAA,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAA,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;;;;AAIG;SACa,aAAa,GAAA;AACzB,IAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACpC,IAAA,MAAM,QAAQ,GAAG,eAAe,EAAE,GAAG,KAAK,IAAI,eAAe,EAAE,GAAG,KAAK,CAAC,CAAC;;AAGzE,IAAA,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;;AAEjC,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;;IAE7C,MAAM,OAAO,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;;AAEzC,IAAA,MAAM,OAAO,GAAG,eAAe,EAAE,CAAC;IAElC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;IAC5B,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC;AAChC,IAAA,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IACjB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC;AACnC,IAAA,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;AAC1B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC;AAC1B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;AACpB,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;AAC3B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;AAC3B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC;AAC1B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;IAEpB,IAAI,IAAI,GAAG,EAAE,CAAC;AACd,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,QAAA,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAA,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1C,IAAI,IAAI,GAAG,CAAC;AACf,SAAA;AACJ,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;AAIG;AACI,eAAe,eAAe,CACjC,WAAoB,EACpB,MAAuB,EAAA;AAEvB,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CACnC,sBAAsB,EACtB,WAAW,EACX,MAAM,CACiB,CAAC;AAChC,CAAC;AAED;;;AAGG;AACI,eAAe,SAAS,CAAC,GAAc,EAAA;AAC1C,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CACjC,cAAc,EACd,GAAG,CACiB,CAAC;AAC7B,CAAC;AAED;;;;;AAKG;AACI,eAAe,SAAS,CAC3B,GAAe,EACf,WAAoB,EACpB,MAAuB,EAAA;AAEvB,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CACjC,cAAc,EACd,GAAG,EACH,sBAAsB,EACtB,WAAW,EACX,MAAM,CACa,CAAC;AAC5B,CAAC;AAED;;;;AAIG;AACI,eAAe,IAAI,CACtB,GAAc,EACd,IAAiB,EAAA;AAEjB,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAC5B,sBAAsB,EACtB,GAAG,EACH,IAAI,CACiB,CAAC;AAC9B,CAAC;AAED;;;AAGG;AACI,eAAe,UAAU,CAAC,SAAiB,EAAA;AAC9C,IAAA,MAAM,UAAU,GAAgB,MAAM,YAAY,CAAC,SAAS,CAAC,CAAC;AAC9D,IAAA,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;AAC7C,IAAA,OAAO,YAAY,CAAC,SAAS,CAAC,CAAC;AACnC;;;;"}