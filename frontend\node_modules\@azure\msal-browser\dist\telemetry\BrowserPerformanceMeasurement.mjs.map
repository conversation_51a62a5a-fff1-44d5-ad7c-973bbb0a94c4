{"version": 3, "file": "BrowserPerformanceMeasurement.mjs", "sources": ["../../src/telemetry/BrowserPerformanceMeasurement.ts"], "sourcesContent": [null], "names": [], "mappings": ";;AAAA;;;AAGG;MAOU,6BAA6B,CAAA;IAMtC,WAAY,CAAA,IAAY,EAAE,aAAqB,EAAA;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,6BAA6B,CAAC,eAAe,CAC5D,IAAI,EACJ,aAAa,CAChB,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,6BAA6B,CAAC,aAAa,CACxD,IAAI,EACJ,aAAa,CAChB,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,6BAA6B,CAAC,WAAW,CACpD,IAAI,EACJ,aAAa,CAChB,CAAC;KACL;AAEO,IAAA,OAAO,eAAe,CAAC,IAAY,EAAE,aAAqB,EAAA;AAC9D,QAAA,OAAO,CAAgB,aAAA,EAAA,IAAI,CAAI,CAAA,EAAA,aAAa,EAAE,CAAC;KAClD;AAEO,IAAA,OAAO,aAAa,CAAC,IAAY,EAAE,aAAqB,EAAA;AAC5D,QAAA,OAAO,CAAc,WAAA,EAAA,IAAI,CAAI,CAAA,EAAA,aAAa,EAAE,CAAC;KAChD;AAEO,IAAA,OAAO,WAAW,CAAC,IAAY,EAAE,aAAqB,EAAA;AAC1D,QAAA,OAAO,CAAY,SAAA,EAAA,IAAI,CAAI,CAAA,EAAA,aAAa,EAAE,CAAC;KAC9C;AAED,IAAA,OAAO,0BAA0B,GAAA;AAC7B,QAAA,QACI,OAAO,MAAM,KAAK,WAAW;AAC7B,YAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;AACzC,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,KAAK,UAAU;AAC7C,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,KAAK,UAAU;AAChD,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,UAAU;AACnD,YAAA,OAAO,MAAM,CAAC,WAAW,CAAC,aAAa,KAAK,UAAU;YACtD,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB,KAAK,UAAU,EAC3D;KACL;AAED;;;;AAIG;AACI,IAAA,OAAO,iBAAiB,CAC3B,aAAqB,EACrB,YAA8B,EAAA;AAE9B,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;AACA,gBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AACjC,oBAAA,MAAM,WAAW,GACb,6BAA6B,CAAC,eAAe,CACzC,WAAW,CAAC,IAAI,EAChB,aAAa,CAChB,CAAC;AACN,oBAAA,MAAM,qBAAqB,GACvB,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAC/B,WAAW,EACX,SAAS,CACZ,CAAC;AACN,oBAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,wBAAA,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAC9C,wBAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CACzB,6BAA6B,CAAC,aAAa,CACvC,WAAW,EACX,aAAa,CAChB,CACJ,CAAC;AACF,wBAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CACzB,6BAA6B,CAAC,WAAW,CACrC,WAAW,EACX,aAAa,CAChB,CACJ,CAAC;AACL,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;KACJ;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;gBACA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3C,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;KACJ;IAED,cAAc,GAAA;AACV,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;gBACA,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtC,gBAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CACtB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,CACf,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;KACJ;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,6BAA6B,CAAC,0BAA0B,EAAE,EAAE;YAC5D,IAAI;AACA,gBAAA,MAAM,qBAAqB,GACvB,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAC/B,IAAI,CAAC,WAAW,EAChB,SAAS,CACZ,CAAC;AACN,gBAAA,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClC,MAAM,UAAU,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACrD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACnD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC9C,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,oBAAA,OAAO,UAAU,CAAC;AACrB,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;AAEX,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AACJ;;;;"}