/**
 * Vue Router 路由配置
 * 包含認證保護和路由守衛
 */

import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { isSameRoute } from '@/utils/routerUtils'

// 定義路由
const routes = [
  {
    path: '/',
    name: 'home',
    redirect: (to) => {
      // 根據認證狀態決定重定向目標
      const authStore = useAuthStore()
      return authStore.state.isAuthenticated ? '/chat' : '/login'
    },
    meta: {
      title: 'BPM Chatbot'
    }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      title: '登入 - BPM Chatbot',
      requiresAuth: false,
      guestOnly: true, // 只有未登入用戶可存取
      description: 'Azure AD 員工身份驗證'
    }
  },
  {
    path: '/chat',
    name: 'chat',
    component: () => import('@/views/ChatView.vue'),
    meta: {
      title: 'BPM 智能助手',
      requiresAuth: true,
      icon: 'chat',
      description: '與 BPM 智能助手對話'
    },
    children: [
      {
        path: '',
        name: 'chat-default',
        component: () => import('@/components/ChatContainer.vue')
      },
      {
        path: 'session/:sessionId',
        name: 'chat-session',
        component: () => import('@/components/ChatContainer.vue'),
        props: true,
        meta: {
          title: '聊天會話 - BPM 智能助手',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('@/views/SettingsView.vue'),
    meta: {
      title: '設定 - BPM Chatbot',
      requiresAuth: true,
      icon: 'settings',
      description: '應用程式設定和偏好'
    },
    children: [
      {
        path: '',
        name: 'settings-redirect',
        redirect: '/settings/general'
      },
      {
        path: 'general',
        name: 'settings-general',
        component: () => import('@/views/settings/GeneralSettings.vue'),
        meta: {
          title: '一般設定 - BPM Chatbot',
          requiresAuth: true
        }
      },
      {
        path: 'models',
        name: 'settings-models',
        component: () => import('@/views/settings/ModelSettings.vue'),
        meta: {
          title: '模型設定 - BPM Chatbot',
          requiresAuth: true
        }
      },
      {
        path: 'privacy',
        name: 'settings-privacy',
        component: () => import('@/views/settings/PrivacySettings.vue'),
        meta: {
          title: '隱私設定 - BPM Chatbot',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/history',
    name: 'history',
    component: () => import('@/views/HistoryView.vue'),
    meta: {
      title: '聊天記錄 - BPM Chatbot',
      requiresAuth: true,
      icon: 'history',
      description: '查看和管理聊天記錄'
    }
  },
  {
    path: '/admin',
    name: 'admin',
    component: () => import('@/views/AdminView.vue'),
    meta: {
      title: '系統管理 - BPM Chatbot',
      requiresAuth: true,
      icon: 'admin',
      description: '系統管理和監控'
    },
    children: [
      {
        path: '',
        name: 'admin-redirect',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'admin-dashboard',
        component: () => import('@/views/admin/AdminDashboard.vue'),
        meta: {
          title: '管理儀表板 - BPM Chatbot',
          requiresAuth: true
        }
      },
      {
        path: 'users',
        name: 'admin-users',
        component: () => import('@/views/admin/UserManagement.vue'),
        meta: {
          title: '用戶管理 - BPM Chatbot',
          requiresAuth: true
        }
      },
      {
        path: 'models',
        name: 'admin-models',
        component: () => import('@/views/admin/ModelManagement.vue'),
        meta: {
          title: '模型管理 - BPM Chatbot',
          requiresAuth: true
        }
      },
      {
        path: 'logs',
        name: 'admin-logs',
        component: () => import('@/views/admin/SystemLogs.vue'),
        meta: {
          title: '系統日誌 - BPM Chatbot',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/profile',
    name: 'profile',
    component: () => import('@/views/ProfileView.vue'),
    meta: {
      title: '個人資料 - BPM Chatbot',
      requiresAuth: true,
      icon: 'profile',
      description: '查看和編輯個人資料'
    }
  },
  {
    path: '/help',
    name: 'help',
    component: () => import('@/views/HelpView.vue'),
    meta: {
      title: '說明 - BPM Chatbot',
      requiresAuth: false,
      icon: 'help',
      description: '使用說明和常見問題'
    }
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('@/views/AboutView.vue'),
    meta: {
      title: '關於 - BPM Chatbot',
      requiresAuth: false,
      icon: 'info',
      description: '關於 BPM 智能助手'
    }
  },
  {
    path: '/unauthorized',
    name: 'unauthorized',
    component: () => import('@/views/UnauthorizedView.vue'),
    meta: {
      title: '權限不足 - BPM Chatbot',
      requiresAuth: false,
      description: '您沒有足夠的權限存取此頁面'
    }
  },
  {
    path: '/maintenance',
    name: 'maintenance',
    component: () => import('@/views/MaintenanceView.vue'),
    meta: {
      title: '系統維護 - BPM Chatbot',
      requiresAuth: false,
      description: '系統正在維護中'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      title: '頁面不存在 - BPM Chatbot',
      description: '您要找的頁面不存在'
    }
  }
]

// 建立路由器實例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（如瀏覽器前進後退），恢復該位置
    if (savedPosition) {
      return savedPosition
    }

    // 如果有 hash 錨點，滾動到該元素
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }

    // 否則滾動到頂部
    return { top: 0, behavior: 'smooth' }
  }
})

// === 路由守衛 ===

/**
 * 全域前置守衛
 * 處理認證檢查、權限驗證、頁面標題等
 */
router.beforeEach(async (to, from, next) => {
  // 設定頁面標題
  document.title = to.meta.title || 'BPM Chatbot'

  // 獲取 Auth Store
  const authStore = useAuthStore()

  try {
    // 檢查會話狀態
    if (authStore.state.isAuthenticated) {
      const sessionValid = authStore.checkSession()
      if (!sessionValid) {
        // 會話已過期，重定向到登入頁面
        // 避免重複導航到相同位置
        if (to.path !== '/login') {
          next('/login')
          return
        }
      }

      // 自動檢查和刷新 Token
      await authStore.checkAndRefreshToken()
    }

    // 處理只允許訪客存取的頁面（如登入頁面）
    if (to.meta.guestOnly && authStore.state.isAuthenticated) {
      // 避免重複導航到相同位置
      if (to.path !== '/chat') {
        next('/chat')
        return
      }
    }

    // 處理需要認證的頁面
    if (to.meta.requiresAuth && !authStore.state.isAuthenticated) {
      // 檢查是否已經在登入頁面
      if (to.path === '/login') {
        next()
        return
      }

      // 保存目標頁面，登入後重定向
      const redirectPath = to.fullPath !== '/login' ? to.fullPath : null
      const loginRoute = {
        path: '/login',
        query: redirectPath ? { redirect: redirectPath } : {}
      }

      // 檢查是否要導航到相同的位置（更精確的比較）
      const currentPath = to.path
      const currentRedirect = to.query?.redirect
      const targetRedirect = loginRoute.query?.redirect

      const isSameLocation = currentPath === '/login' && currentRedirect === targetRedirect

      if (!isSameLocation) {
        next(loginRoute)
        return
      } else {
        // 如果是相同位置，直接允許導航
        next()
        return
      }
    }

    // 權限檢查已簡化：所有已登入用戶都有存取權限

    // 檢查是否是維護模式（可以通過環境變數控制）
    const isMaintenanceMode = import.meta.env.VITE_MAINTENANCE_MODE === 'true'
    if (isMaintenanceMode && to.name !== 'maintenance') {
      // 避免重複導航到維護頁面
      if (to.path !== '/maintenance') {
        next('/maintenance')
        return
      }
    }

    // 更新最後活動時間
    if (authStore.state.isAuthenticated) {
      authStore.updateLastActivity()
    }

    // 通過所有檢查，允許導航
    next()

  } catch (error) {
    console.error('路由守衛錯誤:', error)

    // 發生錯誤時的處理
    if (to.meta.requiresAuth) {
      authStore.setError('系統錯誤，請重新登入')
      // 避免重複導航到登入頁面
      if (to.path !== '/login') {
        next('/login')
      } else {
        next()
      }
    } else {
      next()
    }
  }
})

/**
 * 全域後置守衛
 * 處理路由跳轉完成後的邏輯
 */
router.afterEach((to, from, failure) => {
  // 如果路由跳轉失敗
  if (failure) {
    console.error('路由跳轉失敗:', failure)
    return
  }

  // 紀錄路由變化（用於分析）
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'GA_TRACKING_ID', {
      page_title: to.meta.title,
      page_location: window.location.href
    })
  }

  // 滾動到頂部處理已在 scrollBehavior 中處理

  // 關閉可能開啟的模態框或側邊欄
  const body = document.body
  if (body.classList.contains('modal-open')) {
    body.classList.remove('modal-open')
  }
})

/**
 * 路由錯誤處理
 */
router.onError((error, to, from) => {
  // 忽略重複導航錯誤
  if (error.message.includes('Avoided redundant navigation')) {
    console.debug('忽略重複導航:', error.message)
    return
  }

  console.error('路由錯誤:', {
    error,
    to: to?.fullPath,
    from: from?.fullPath,
    message: error.message,
    stack: error.stack
  })

  // 根據錯誤類型進行不同處理
  if (error.message.includes('ChunkLoadError') || error.message.includes('Loading chunk')) {
    // 代碼分割載入錯誤，通常是緩存問題
    console.warn('檢測到 ChunkLoadError，嘗試重新載入頁面')
    window.location.reload()
  } else if (error.message.includes('Network Error')) {
    // 網路錯誤
    const authStore = useAuthStore()
    authStore.setError('網路連線錯誤，請檢查網路狀態')
  }
})

// === 路由工具函數 ===

/**
 * 檢查路由是否需要認證
 */
export function isRouteAuthRequired(route) {
  const matchedRoute = router.resolve(route).matched[0]
  return matchedRoute?.meta?.requiresAuth === true
}

/**
 * 檢查用戶是否有存取路由的權限
 */
export function canUserAccessRoute(route) {
  const authStore = useAuthStore()
  const matchedRoute = router.resolve(route).matched[0]

  if (!matchedRoute) return false

  // 檢查認證要求
  if (matchedRoute.meta?.requiresAuth && !authStore.state.isAuthenticated) {
    return false
  }

  // 簡化權限檢查：所有已登入用戶都有存取權限
  return true
}

/**
 * 獲取可用的導航項目（基於用戶權限）
 */
export function getAvailableNavItems() {
  const authStore = useAuthStore()
  const navItems = [
    {
      name: 'chat',
      title: 'BPM 智能助手',
      path: '/chat',
      icon: 'chat',
      requiresAuth: true
    },
    {
      name: 'history',
      title: '聊天記錄',
      path: '/history',
      icon: 'history',
      requiresAuth: true
    },
    {
      name: 'settings',
      title: '設定',
      path: '/settings',
      icon: 'settings',
      requiresAuth: true
    },
    {
      name: 'profile',
      title: '個人資料',
      path: '/profile',
      icon: 'profile',
      requiresAuth: true
    },
    {
      name: 'admin',
      title: '系統管理',
      path: '/admin',
      icon: 'admin',
      requiresAuth: true
    },
    {
      name: 'help',
      title: '說明',
      path: '/help',
      icon: 'help',
      requiresAuth: false
    },
    {
      name: 'about',
      title: '關於',
      path: '/about',
      icon: 'info',
      requiresAuth: false
    }
  ]

  return navItems.filter(item => {
    // 檢查認證要求
    if (item.requiresAuth && !authStore.state.isAuthenticated) {
      return false
    }

    // 簡化權限檢查：所有已登入用戶都可看到所有導航項目
    return true
  })
}

/**
 * 安全導航函數
 * 在導航前檢查權限和認證狀態，避免重複導航
 */
export function safeNavigateTo(path, options = {}) {
  // 檢查是否要導航到當前位置
  const currentRoute = router.currentRoute.value
  if (currentRoute.path === path) {
    console.debug(`已在目標路由: ${path}，跳過導航`)
    return Promise.resolve()
  }

  if (canUserAccessRoute(path)) {
    try {
      if (options.replace) {
        return router.replace(path).catch(error => {
          if (error.message.includes('Avoided redundant navigation')) {
            console.debug('忽略重複導航錯誤:', error.message)
            return Promise.resolve()
          }
          throw error
        })
      } else {
        return router.push(path).catch(error => {
          if (error.message.includes('Avoided redundant navigation')) {
            console.debug('忽略重複導航錯誤:', error.message)
            return Promise.resolve()
          }
          throw error
        })
      }
    } catch (error) {
      console.error('導航失敗:', error)
      return Promise.reject(error)
    }
  } else {
    console.warn(`用戶無權存取路由: ${path}`)
    const authStore = useAuthStore()
    if (!authStore.state.isAuthenticated) {
      return safeNavigateTo('/login', { replace: true })
    } else {
      return safeNavigateTo('/unauthorized', { replace: true })
    }
  }
}

/**
 * 安全重定向函數
 * 專門處理認證相關的重定向，避免循環重定向
 */
export function safeRedirect(targetPath, fallbackPath = '/') {
  const currentRoute = router.currentRoute.value

  // 避免重定向到當前位置
  if (currentRoute.path === targetPath) {
    console.debug(`已在目標路由: ${targetPath}，跳過重定向`)
    return Promise.resolve()
  }

  // 避免循環重定向
  if (currentRoute.query.redirect === targetPath) {
    console.warn(`檢測到潛在的循環重定向，使用回退路由: ${fallbackPath}`)
    return safeNavigateTo(fallbackPath, { replace: true })
  }

  return safeNavigateTo(targetPath, { replace: true })
}

export default router