"""
員工服務模組
處理員工身份驗證、權限檢查和 Microsoft Graph API 整合
"""

import os
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import httpx
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from fastapi import HTTPException, status
import json

# 設定日誌
logger = logging.getLogger(__name__)

class Employee:
    """員工資料模型"""
    
    def __init__(self, data: Dict[str, Any]):
        self.id = data.get("id")
        self.user_principal_name = data.get("userPrincipalName")
        self.display_name = data.get("displayName")
        self.given_name = data.get("givenName")
        self.surname = data.get("surname")
        self.mail = data.get("mail")
        self.mobile_phone = data.get("mobilePhone")
        self.office_location = data.get("officeLocation")
        self.job_title = data.get("jobTitle")
        self.department = data.get("department")
        self.company_name = data.get("companyName")
        self.manager_id = data.get("manager", {}).get("id") if data.get("manager") else None
        self.is_active = data.get("accountEnabled", False)
        
        # 權限和群組（需要額外查詢）
        self.groups: List[str] = []
        self.roles: List[str] = []
        self.permissions: List[str] = []
        
        # 快取時間
        self.cached_at = datetime.now()
    
    @property
    def is_employee(self) -> bool:
        """檢查是否為有效員工"""
        return (
            self.is_active and
            self.mail and
            self.department and
            self.job_title
        )
    
    @property
    def email_domain(self) -> Optional[str]:
        """獲取 email domain"""
        if self.mail:
            return self.mail.split("@")[-1].lower()
        return None
    
    def has_permission(self, permission: str) -> bool:
        """檢查是否有特定權限"""
        return permission in self.permissions
    
    def in_group(self, group: str) -> bool:
        """檢查是否在特定群組"""
        return group in self.groups
    
    def has_role(self, role: str) -> bool:
        """檢查是否有特定角色"""
        return role in self.roles
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "id": self.id,
            "user_principal_name": self.user_principal_name,
            "display_name": self.display_name,
            "given_name": self.given_name,
            "surname": self.surname,
            "mail": self.mail,
            "mobile_phone": self.mobile_phone,
            "office_location": self.office_location,
            "job_title": self.job_title,
            "department": self.department,
            "company_name": self.company_name,
            "manager_id": self.manager_id,
            "is_active": self.is_active,
            "is_employee": self.is_employee,
            "email_domain": self.email_domain,
            "groups": self.groups,
            "roles": self.roles,
            "permissions": self.permissions,
            "cached_at": self.cached_at.isoformat()
        }

class EmployeeService:
    """員工服務類別"""
    
    def __init__(self):
        self.tenant_id = os.getenv("AAD_TENANT_ID")
        self.client_id = os.getenv("AAD_CLIENT_ID")
        self.client_secret = os.getenv("AAD_CLIENT_SECRET")
        
        if not all([self.tenant_id, self.client_id, self.client_secret]):
            raise ValueError("缺少必要的 Azure AD 配置")
        
        # Microsoft Graph API 基礎 URL
        self.graph_base_url = "https://graph.microsoft.com/v1.0"
        
        # 認證憑證
        self.credential = ClientSecretCredential(
            tenant_id=self.tenant_id,
            client_id=self.client_id,
            client_secret=self.client_secret
        )
        
        # 員工資料快取 (object_id -> Employee)
        self._employee_cache: Dict[str, Employee] = {}
        self._cache_ttl = timedelta(hours=1)  # 快取 1 小時
        
        # 允許的 email domains
        self.allowed_domains = self._load_allowed_domains()
        
        # BPM 相關群組和角色
        self.bpm_groups = self._load_bmp_groups()
        self.bmp_roles = self._load_bmp_roles()
    
    def _load_allowed_domains(self) -> List[str]:
        """載入允許的 email domains"""
        domains_str = os.getenv("ALLOWED_EMAIL_DOMAINS", "")
        if domains_str:
            return [domain.strip().lower() for domain in domains_str.split(",")]
        return []
    
    def _load_bmp_groups(self) -> List[str]:
        """載入 BPM 相關群組"""
        groups_str = os.getenv("BMP_GROUPS", "")
        if groups_str:
            return [group.strip() for group in groups_str.split(",")]
        return ["BPM Users", "All Employees"]  # 預設群組
    
    def _load_bmp_roles(self) -> List[str]:
        """載入 BPM 相關角色"""
        roles_str = os.getenv("BMP_ROLES", "")
        if roles_str:
            return [role.strip() for role in roles_str.split(",")]
        return ["Employee", "BPM User"]  # 預設角色
    
    async def _get_access_token(self) -> str:
        """獲取 Microsoft Graph API 存取 token"""
        try:
            token = self.credential.get_token("https://graph.microsoft.com/.default")
            return token.token
        except Exception as e:
            logger.error(f"獲取 Graph API token 失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="無法連接到員工目錄服務"
            )
    
    async def _make_graph_request(self, endpoint: str) -> Dict[str, Any]:
        """發送 Microsoft Graph API 請求"""
        access_token = await self._get_access_token()
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        url = f"{self.graph_base_url}{endpoint}"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 404:
                    return {}
                
                response.raise_for_status()
                return response.json()
                
        except httpx.RequestError as e:
            logger.error(f"Graph API 請求失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="員工目錄服務暫時無法使用"
            )
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 403:
                logger.error("Graph API 權限不足")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="員工目錄存取權限不足"
                )
            else:
                logger.error(f"Graph API HTTP 錯誤: {e}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="員工目錄服務發生錯誤"
                )
    
    def _is_cache_valid(self, employee: Employee) -> bool:
        """檢查快取是否有效"""
        return datetime.now() - employee.cached_at < self._cache_ttl
    
    async def get_employee_by_object_id(self, object_id: str) -> Optional[Employee]:
        """根據 object ID 獲取員工資訊"""
        
        # 檢查快取
        if object_id in self._employee_cache:
            employee = self._employee_cache[object_id]
            if self._is_cache_valid(employee):
                logger.debug(f"從快取獲取員工資訊: {object_id}")
                return employee
        
        try:
            # 從 Graph API 獲取員工基本資訊
            user_data = await self._make_graph_request(f"/users/{object_id}")
            
            if not user_data:
                logger.warning(f"找不到員工: {object_id}")
                return None
            
            # 建立員工物件
            employee = Employee(user_data)
            
            # 獲取群組資訊
            groups_data = await self._make_graph_request(f"/users/{object_id}/memberOf")
            if groups_data and "value" in groups_data:
                employee.groups = [
                    group.get("displayName", "") 
                    for group in groups_data["value"]
                    if group.get("@odata.type") == "#microsoft.graph.group"
                ]
            
            # 快取員工資訊
            self._employee_cache[object_id] = employee
            
            logger.info(f"獲取員工資訊成功: {employee.display_name} ({employee.mail})")
            return employee
            
        except Exception as e:
            logger.error(f"獲取員工資訊失敗: {object_id} - {e}")
            return None
    
    async def get_employee_by_upn(self, user_principal_name: str) -> Optional[Employee]:
        """根據 UPN 獲取員工資訊"""
        try:
            user_data = await self._make_graph_request(f"/users/{user_principal_name}")
            
            if not user_data:
                return None
            
            object_id = user_data.get("id")
            if object_id:
                return await self.get_employee_by_object_id(object_id)
            
            return None
            
        except Exception as e:
            logger.error(f"獲取員工資訊失敗: {user_principal_name} - {e}")
            return None
    
    async def validate_employee(self, object_id: str) -> Employee:
        """驗證員工身份並返回員工資訊"""
        employee = await self.get_employee_by_object_id(object_id)
        
        if not employee:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="找不到員工資訊"
            )
        
        if not employee.is_employee:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="帳號未啟用或缺少必要的員工資訊"
            )
        
        # 檢查 email domain
        if self.allowed_domains and employee.email_domain not in self.allowed_domains:
            logger.warning(f"不允許的 email domain: {employee.email_domain}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="此 email domain 不允許存取"
            )
        
        # 檢查是否在允許的群組中
        if self.bmp_groups:
            has_bmp_group = any(
                group in employee.groups 
                for group in self.bmp_groups
            )
            if not has_bmp_group:
                logger.warning(f"員工不在 BPM 群組中: {employee.display_name}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="您沒有使用 BPM Chatbot 的權限"
                )
        
        logger.info(f"員工驗證成功: {employee.display_name}")
        return employee
    
    async def check_bmp_permissions(self, employee: Employee) -> Dict[str, bool]:
        """檢查 BPM 相關權限"""
        permissions = {
            "can_use_chatbot": True,  # 基本聊天權限
            "can_access_advanced_features": False,  # 進階功能權限
            "can_view_analytics": False,  # 分析報告權限
            "can_manage_system": False  # 系統管理權限
        }
        
        # 根據群組設定權限
        if "BPM Administrators" in employee.groups:
            permissions.update({
                "can_access_advanced_features": True,
                "can_view_analytics": True,
                "can_manage_system": True
            })
        elif "BPM Power Users" in employee.groups:
            permissions.update({
                "can_access_advanced_features": True,
                "can_view_analytics": True
            })
        
        # 根據部門設定權限
        if employee.department in ["IT", "Human Resources"]:
            permissions["can_view_analytics"] = True
        
        return permissions
    
    def clear_cache(self, object_id: Optional[str] = None):
        """清除快取"""
        if object_id:
            self._employee_cache.pop(object_id, None)
            logger.info(f"清除員工快取: {object_id}")
        else:
            self._employee_cache.clear()
            logger.info("清除所有員工快取")
    
    async def search_employees(self, query: str, limit: int = 10) -> List[Employee]:
        """搜尋員工（管理功能）"""
        try:
            # 使用 Graph API 搜尋
            search_endpoint = f"/users?$search=\"displayName:{query}\" OR \"mail:{query}\"&$top={limit}"
            
            search_data = await self._make_graph_request(search_endpoint)
            
            employees = []
            if search_data and "value" in search_data:
                for user_data in search_data["value"]:
                    employee = Employee(user_data)
                    if employee.is_employee:
                        employees.append(employee)
            
            return employees
            
        except Exception as e:
            logger.error(f"搜尋員工失敗: {query} - {e}")
            return []

# 建立全域服務實例
employee_service = EmployeeService()