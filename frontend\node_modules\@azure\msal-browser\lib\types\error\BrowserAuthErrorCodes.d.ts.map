{"version": 3, "file": "BrowserAuthErrorCodes.d.ts", "sourceRoot": "", "sources": ["../../../src/error/BrowserAuthErrorCodes.ts"], "names": [], "mappings": "AAKA,eAAO,MAAM,cAAc,qBAAqB,CAAC;AACjD,eAAO,MAAM,iBAAiB,uBAAuB,CAAC;AACtD,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,cAAc,qBAAqB,CAAC;AACjD,eAAO,MAAM,aAAa,qBAAqB,CAAC;AAChD,eAAO,MAAM,iCAAiC,2CACF,CAAC;AAC7C,eAAO,MAAM,kBAAkB,0BAA0B,CAAC;AAC1D,eAAO,MAAM,4BAA4B,oCAAoC,CAAC;AAC9E,eAAO,MAAM,qBAAqB,4BAA4B,CAAC;AAC/D,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,aAAa,mBAAmB,CAAC;AAC9C,eAAO,MAAM,mBAAmB,0BAA0B,CAAC;AAC3D,eAAO,MAAM,oBAAoB,2BAA2B,CAAC;AAC7D,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,iBAAiB,wBAAwB,CAAC;AACvD,eAAO,MAAM,iBAAiB,wBAAwB,CAAC;AACvD,eAAO,MAAM,uBAAuB,8BAA8B,CAAC;AACnE,eAAO,MAAM,uBAAuB,8BAA8B,CAAC;AACnE,eAAO,MAAM,cAAc,qBAAqB,CAAC;AACjD,eAAO,MAAM,sBAAsB,8BAA8B,CAAC;AAClE,eAAO,MAAM,wBAAwB,iCAAiC,CAAC;AACvE,eAAO,MAAM,mCAAmC,8CACD,CAAC;AAChD,eAAO,MAAM,sBAAsB,8BAA8B,CAAC;AAClE,eAAO,MAAM,sBAAsB,+BAA+B,CAAC;AACnE,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,qBAAqB,4BAA4B,CAAC;AAC/D,eAAO,MAAM,eAAe,sBAAsB,CAAC;AACnD,eAAO,MAAM,qBAAqB,4BAA4B,CAAC;AAC/D,eAAO,MAAM,iBAAiB,wBAAwB,CAAC;AACvD,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,qBAAqB,6BAA6B,CAAC;AAChE,eAAO,MAAM,iBAAiB,yBAAyB,CAAC;AACxD,eAAO,MAAM,iBAAiB,yBAAyB,CAAC;AACxD,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,iCAAiC,0CACH,CAAC;AAC5C,eAAO,MAAM,gCAAgC,yCACH,CAAC;AAC3C,eAAO,MAAM,mBAAmB,yBAAyB,CAAC;AAC1D,eAAO,MAAM,sCAAsC,iDACD,CAAC;AACnD,eAAO,MAAM,sBAAsB,6BAA6B,CAAC;AACjE,eAAO,MAAM,2BAA2B,mCAAmC,CAAC;AAC5E,eAAO,MAAM,8BAA8B,sCACJ,CAAC;AACxC,eAAO,MAAM,oCAAoC,4CACJ,CAAC;AAC9C,eAAO,MAAM,wBAAwB,gCAAgC,CAAC;AACtE,eAAO,MAAM,mBAAmB,0BAA0B,CAAC;AAC3D,eAAO,MAAM,sBAAsB,8BAA8B,CAAC;AAClE,eAAO,MAAM,oBAAoB,4BAA4B,CAAC;AAC9D,eAAO,MAAM,oBAAoB,4BAA4B,CAAC"}