# Design Document

## Overview

BPM Chatbot 是一個專為 Business Process Management 簽核系統設計的智能問答機器人。系統採用前後端分離架構，前端使用 Vue.js 3 整合 Azure Active Directory (AAD) 認證，後端使用 Python 3.12 FastAPI 處理業務邏輯並透過 AWS Lambda MCP server 與 LightRAG 整合。系統支援多模型選擇（ChatGPT、Claude、Gemini），使用 Redis 作為資料存儲，確保只有公司員工能存取並只處理 BPM 相關問題。

## Steering Document Alignment

### Technical Standards (tech.md)
採用現代化的技術棧和最佳實踐：
- 前端：Vue.js 3 + TypeScript + Vite + Azure AD SDK
- 後端：Python 3.12 + FastAPI + Pydantic + Redis
- 認證：Azure Active Directory (AAD) 整合
- 資料庫：Redis 用於會話和快取
- 模型服務：透過 AWS Lambda MCP 整合多模型

### Project Structure (structure.md)
採用標準的前後端分離專案結構，加入認證和多模型支援。

## Code Reuse Analysis

### 新專案架構設計
建立企業級認證和多模型架構：

### 計劃建立的可重用組件
- **AAD Client**: Azure AD 認證整合
- **MCP Client**: AWS Lambda MCP server 連接器  
- **Model Manager**: 多模型選擇和路由
- **Redis Client**: 資料存取和會話管理
- **Employee Validator**: 員工身份驗證服務
- **Session Manager**: 用戶會話狀態管理

### 企業整合考量
- **AAD Integration**: 與現有企業 AAD 整合
- **Employee Directory**: 驗證員工身份和權限
- **Audit Logging**: 完整的用戶操作記錄
- **Model Usage Tracking**: 各模型使用情況統計

## Architecture

系統採用微服務架構設計，整合企業認證和多模型支援：

### Modular Design Principles
- **Authentication First**: 所有操作需要有效的 AAD token
- **Multi-Model Support**: 統一介面支援多個 AI 模型
- **Session Persistence**: Redis 持久化用戶會話
- **Enterprise Security**: 符合企業安全標準

```mermaid
graph TD
    A[Vue.js Frontend + AAD] --> B[FastAPI Backend]
    B --> C[Employee Validator]
    B --> D[Redis Session Store]
    B --> E[Model Router]
    E --> F[AWS Lambda MCP]
    F --> G[LightRAG Server]
    B --> H[BPM Filter Service]
    A --> I[AAD Authentication]
    I --> J[Employee Directory]
```

### System Architecture with Authentication

```mermaid
graph TB
    subgraph "Frontend Layer (AAD Protected)"
        A1[Login Component]
        A2[Chat Interface]
        A3[Model Selector]
        A4[User Profile]
    end
    
    subgraph "Authentication Layer"
        B1[AAD Token Validation]
        B2[Employee Verification]
        B3[Session Management]
    end
    
    subgraph "API Layer"
        C1[Chat Endpoint]
        C2[Model Selection]
        C3[User Profile API]
        C4[Auth Middleware]
    end
    
    subgraph "Service Layer"
        D1[Chat Service]
        D2[Model Router]
        D3[MCP Client]
        D4[Redis Client]
    end
    
    subgraph "External Services"
        E1[AWS Lambda MCP]
        E2[LightRAG Server]
        E3[OpenAI API]
        E4[Claude API]
        E5[Gemini API]
        E6[Redis Database]
    end
    
    A1 --> B1
    A2 --> C1
    A3 --> C2
    B1 --> B2
    B2 --> B3
    C1 --> D1
    C2 --> D2
    D1 --> D3
    D2 --> D3
    D3 --> E1
    E1 --> E2
    D2 --> E3
    D2 --> E4
    D2 --> E5
    D4 --> E6
```

## Components and Interfaces

### Frontend Components

#### LoginComponent.vue
- **Purpose:** Azure AD 登入介面和狀態管理
- **Interfaces:** 
  - `login(): Promise<void>`
  - `logout(): void`
  - `refreshToken(): Promise<string>`
- **Dependencies:** Azure AD SDK (@azure/msal-browser)
- **Features:** 自動 token 刷新、登入狀態持久化

#### ChatContainer.vue  
- **Purpose:** 主要聊天介面容器，需要認證後才能存取
- **Interfaces:** 
  - `sendMessage(message: string, model: string): Promise<void>`
  - `clearHistory(): void`
  - `changeModel(model: ModelType): void`
- **Dependencies:** MessageList, MessageInput, ModelSelector, AuthGuard
- **State Management:** Pinia store with AAD user context

#### ModelSelector.vue
- **Purpose:** AI 模型選擇組件
- **Interfaces:**
  - `selectModel(model: ModelType): void`
  - `getAvailableModels(): ModelType[]`
- **Dependencies:** User permissions, Model availability API
- **Features:** 模型效能顯示、使用統計

#### MessageList.vue
- **Purpose:** 顯示對話歷史，包含模型資訊
- **Interfaces:**
  - `scrollToBottom(): void`
  - `copyMessage(messageId: string): void`
  - `showModelInfo(messageId: string): void`
- **Dependencies:** Message component, Redis session data
- **Features:** 顯示每則訊息使用的模型

### Backend Components

#### AuthController
- **Purpose:** 處理認證相關的 HTTP 請求
- **Interfaces:**
  - `POST /api/auth/validate`
  - `GET /api/auth/profile`
  - `POST /api/auth/refresh`
- **Dependencies:** AAD Token Validator, Employee Service
- **Features:** Token 驗證、員工身份確認

#### ChatController
- **Purpose:** 處理聊天相關的 HTTP 請求（需認證）
- **Interfaces:**
  - `POST /api/chat/message`
  - `GET /api/chat/history`
  - `POST /api/chat/clear-history`
- **Dependencies:** ChatService, AuthMiddleware
- **Security:** 每個請求驗證 AAD token

#### ModelController
- **Purpose:** 模型選擇和管理
- **Interfaces:**
  - `GET /api/models/available`
  - `POST /api/models/select`
  - `GET /api/models/usage-stats`
- **Dependencies:** ModelService, User permissions
- **Features:** 模型可用性檢查、使用統計

#### ChatService
- **Purpose:** 核心業務邏輯處理（認證後）
- **Interfaces:**
  - `process_message(message: str, user_id: str, model: str) -> ChatResponse`
  - `get_chat_history(user_id: str) -> List[Message]`
- **Dependencies:** FilterService, MCPClient, RedisClient, ModelRouter
- **Features:** 用戶會話管理、訊息歷史

#### EmployeeService
- **Purpose:** 員工身份驗證和權限管理
- **Interfaces:**
  - `validate_employee(aad_token: str) -> Employee`
  - `check_permissions(user_id: str) -> Permissions`
- **Dependencies:** AAD Graph API, Employee directory
- **Features:** 員工資料驗證、權限檢查

#### ModelRouter
- **Purpose:** 多模型路由和負載平衡
- **Interfaces:**
  - `route_to_model(message: str, model: str, user_context: dict) -> ModelResponse`
  - `get_model_health() -> Dict[str, bool]`
- **Dependencies:** OpenAI, Claude, Gemini clients, MCP Client
- **Features:** 模型選擇、錯誤轉移、負載平衡

#### MCPClient
- **Purpose:** 與 AWS Lambda MCP server 整合
- **Interfaces:**
  - `query_lightrag(question: str, context: dict) -> MCPResponse`
  - `health_check() -> bool`
- **Dependencies:** AWS Lambda SDK, Retry mechanism
- **Features:** Lambda 函數調用、錯誤處理、重試機制

#### RedisClient
- **Purpose:** Redis 資料存取和會話管理
- **Interfaces:**
  - `save_message(user_id: str, message: Message) -> bool`
  - `get_chat_history(user_id: str) -> List[Message]`
  - `set_user_session(user_id: str, session_data: dict) -> bool`
- **Dependencies:** Redis connection pool
- **Features:** 會話持久化、快取管理、過期清理

## Data Models

### Frontend Models

#### User Interface
```typescript
interface User {
  id: string
  email: string
  name: string
  department: string
  isEmployee: boolean
  permissions: string[]
  aadToken: string
}
```

#### Message Interface
```typescript
interface Message {
  id: string
  content: string
  type: 'user' | 'assistant' | 'system'
  timestamp: Date
  userId: string
  model: ModelType
  sessionId: string
  isLoading?: boolean
  error?: string
}
```

#### ModelType Enum
```typescript
enum ModelType {
  CHATGPT = 'chatgpt',
  CLAUDE = 'claude', 
  GEMINI = 'gemini'
}

interface ModelInfo {
  type: ModelType
  name: string
  description: string
  available: boolean
  responseTime: number
}
```

### Backend Models

#### Employee Model
```python
class Employee(BaseModel):
    id: str
    email: str
    name: str
    department: str
    is_active: bool
    permissions: List[str]
    
class AuthRequest(BaseModel):
    aad_token: str
    
class AuthResponse(BaseModel):
    user: Employee
    session_id: str
    expires_at: datetime
```

#### ChatRequest Model
```python
class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1, max_length=1000)
    model: ModelType
    session_id: str
    
class ChatResponse(BaseModel):
    response: str
    is_bpm_related: bool
    source: str
    model_used: ModelType
    processing_time: float
    timestamp: datetime
```

#### MCPResponse Model
```python
class MCPResponse(BaseModel):
    answer: str
    confidence: float
    sources: List[str]
    lambda_execution_time: float
    
class ModelResponse(BaseModel):
    content: str
    model: ModelType
    tokens_used: int
    cost: float
    response_time: float
```

### Redis Data Structures

#### Session Data
```python
# Redis Key: session:{user_id}
{
    "user_id": "string",
    "session_id": "string", 
    "created_at": "timestamp",
    "last_activity": "timestamp",
    "selected_model": "ModelType",
    "message_count": "integer"
}
```

#### Chat History
```python
# Redis Key: chat_history:{user_id}
# Redis Type: List of JSON strings
[
    {
        "id": "message_id",
        "content": "string",
        "type": "user|assistant",
        "model": "ModelType",
        "timestamp": "ISO datetime"
    }
]
```

## Error Handling

### Error Scenarios

1. **AAD 認證失敗**
   - **Handling:** 重導向到登入頁面，清除本地 token
   - **User Impact:** 顯示「請重新登入」訊息

2. **非員工身份**
   - **Handling:** 拒絕存取，記錄安全事件
   - **User Impact:** 顯示「僅限公司員工使用」

3. **模型服務不可用**
   - **Handling:** 自動切換到可用模型或顯示錯誤
   - **User Impact:** 通知用戶模型暫時無法使用

4. **AWS Lambda MCP 錯誤**
   - **Handling:** 重試機制，記錄錯誤日誌
   - **User Impact:** 顯示「知識庫查詢失敗」

5. **Redis 連線失敗**
   - **Handling:** 降級到無狀態模式
   - **User Impact:** 警告「聊天記錄可能無法保存」

### Frontend Error Handling
```typescript
class ErrorHandler {
  static handle(error: ApiError): string {
    switch (error.code) {
      case 'AUTH_FAILED':
        authStore.logout()
        return '認證失敗，請重新登入'
      case 'NOT_EMPLOYEE':
        return '此服務僅限公司員工使用'
      case 'MODEL_UNAVAILABLE':
        return '所選模型暫時無法使用，請選擇其他模型'
      case 'MCP_ERROR':
        return '知識庫連線異常，請稍後再試'
      case 'REDIS_ERROR':
        return '會話服務異常，聊天記錄可能無法保存'
      default:
        return '系統發生錯誤，請聯絡 IT 支援'
    }
  }
}
```

### Backend Error Handling
```python
class BPMChatException(Exception):
    def __init__(self, message: str, code: str, status_code: int = 400):
        self.message = message
        self.code = code
        self.status_code = status_code

# AAD 認證中介軟體
async def verify_aad_token(request: Request, call_next):
    try:
        token = request.headers.get("Authorization")
        if not token:
            raise BPMChatException("Missing AAD token", "AUTH_REQUIRED", 401)
        
        # 驗證 AAD token 和員工身份
        employee = await employee_service.validate_employee(token)
        request.state.user = employee
        
        response = await call_next(request)
        return response
    except Exception as e:
        return JSONResponse(
            status_code=401,
            content={"error": "UNAUTHORIZED", "message": "認證失敗"}
        )
```

## Testing Strategy

### Unit Testing

#### Frontend Testing (Vitest + Vue Test Utils)
- **AAD Integration**: 模擬 AAD 登入流程
- **Model Selection**: 測試模型切換功能
- **Chat Components**: 測試認證狀態下的聊天功能
- **Error Handling**: 測試各種認證和模型錯誤

#### Backend Testing (pytest + pytest-asyncio)
- **Authentication Middleware**: 測試 AAD token 驗證
- **Employee Validation**: 測試員工身份確認
- **Model Routing**: 測試多模型路由邏輯
- **Redis Integration**: 測試資料存取和會話管理
- **MCP Client**: 模擬 AWS Lambda 回應

### Integration Testing

#### Authentication Flow Tests
```python
async def test_full_auth_flow():
    # 測試完整認證流程
    # 1. AAD token 驗證
    # 2. 員工身份確認  
    # 3. 會話建立
    # 4. 聊天權限確認
    pass

async def test_model_switching():
    # 測試模型切換功能
    # 1. 選擇不同模型
    # 2. 驗證路由正確
    # 3. 確認回應格式
    pass
```

### End-to-End Testing

#### Complete User Journey (Playwright)
- **AAD Login to Chat**: 從 AAD 登入到成功聊天的完整流程
- **Model Selection**: 測試不同模型的選擇和使用
- **Session Persistence**: 測試會話在重新整理後的持續性
- **Error Recovery**: 測試各種錯誤情況的用戶體驗

## Security Considerations

### Authentication & Authorization
- **AAD Integration**: 使用 Microsoft Graph API 驗證員工身份
- **Token Validation**: 每個 API 請求驗證 AAD token 有效性
- **Session Security**: Redis 會話資料加密存儲
- **Permission Model**: 基於部門和角色的權限控制

### API Security
- **CORS Policy**: 嚴格的跨域資源共享設定
- **Rate Limiting**: 防止 API 濫用和 DoS 攻擊
- **Request Logging**: 記錄所有認證和聊天請求
- **Model API Keys**: 安全管理各 AI 模型的 API 金鑰

### Data Protection
- **PII Handling**: 妥善處理員工個人識別資訊
- **Chat History**: 聊天記錄定期清理政策
- **Audit Trail**: 完整的用戶活動記錄
- **Encryption**: 敏感資料傳輸和存儲加密

### Infrastructure Security
- **AWS Lambda**: MCP server 的 IAM 權限最小化
- **Redis Security**: 認證和 TLS 加密連線
- **Environment Variables**: 所有機密資訊環境變數管理
- **Container Security**: Docker 映像安全掃描