"""
API 回應工具模組
提供統一的 API 回應格式和工具函數
"""

from typing import TypeVar, Generic, Optional, Any, Dict
from pydantic import BaseModel, Field

T = TypeVar('T')

class APIResponse(BaseModel, Generic[T]):
    """統一的 API 回應格式"""
    
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="回應訊息")
    data: Optional[T] = Field(default=None, description="回應資料")
    error: Optional[str] = Field(default=None, description="錯誤代碼")
    timestamp: str = Field(..., description="時間戳")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": {"result": "example"},
                "error": None,
                "timestamp": "2024-01-01T10:00:00Z"
            }
        }

def create_success_response(
    data: Optional[T] = None,
    message: str = "操作成功"
) -> APIResponse[T]:
    """建立成功回應"""
    from datetime import datetime
    
    return APIResponse[T](
        success=True,
        message=message,
        data=data,
        error=None,
        timestamp=datetime.utcnow().isoformat()
    )

def create_error_response(
    error: str,
    message: str = "操作失敗",
    data: Optional[T] = None
) -> APIResponse[T]:
    """建立錯誤回應"""
    from datetime import datetime
    
    return APIResponse[T](
        success=False,
        message=message,
        data=data,
        error=error,
        timestamp=datetime.utcnow().isoformat()
    )