"""
會話管理服務
實作用戶會話建立、更新、清理邏輯
"""

import os
import uuid
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, status

from .redis_client import redis_client

# 設定日誌
logger = logging.getLogger(__name__)

class SessionService:
    """會話管理服務類別"""
    
    def __init__(self):
        self.redis = redis_client
        
        # 會話配置
        self.default_ttl = int(os.getenv("SESSION_DEFAULT_TTL", "1800"))  # 30 minutes
        self.max_ttl = int(os.getenv("SESSION_MAX_TTL", "86400"))  # 24 hours
        self.cleanup_interval = int(os.getenv("SESSION_CLEANUP_INTERVAL", "3600"))  # 1 hour
        
        # 會話統計
        self.session_stats = {
            "created": 0,
            "active": 0,
            "expired": 0,
            "deleted": 0
        }
    
    async def create_session(
        self,
        user_id: str,
        employee_id: str,
        session_data: Dict[str, Any],
        ttl_seconds: Optional[int] = None
    ) -> str:
        """
        建立新會話
        
        Args:
            user_id: 用戶 ID
            employee_id: 員工 ID  
            session_data: 會話資料
            ttl_seconds: 生存時間（秒）
        
        Returns:
            會話 ID
        """
        session_id = str(uuid.uuid4())
        ttl = min(ttl_seconds or self.default_ttl, self.max_ttl)
        
        # 準備會話資訊
        session_info = {
            "session_id": session_id,
            "user_id": user_id,
            "employee_id": employee_id,
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(seconds=ttl)).isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "is_active": True,
            "ttl_seconds": ttl,
            **session_data
        }
        
        try:
            # 儲存會話資料
            session_key = self.redis.get_session_key(session_id)
            success = await self.redis.set_json(session_key, session_info, ttl)
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="會話建立失敗"
                )
            
            # 建立用戶到會話的索引
            user_sessions_key = self.redis.get_user_key(user_id, "sessions")
            await self.redis.sadd(user_sessions_key, session_id)
            await self.redis.expire(user_sessions_key, ttl)
            
            # 更新統計
            self.session_stats["created"] += 1
            self.session_stats["active"] += 1
            
            logger.info(f"會話建立成功: {session_id} (用戶: {user_id})")
            return session_id
            
        except Exception as e:
            logger.error(f"建立會話失敗: {user_id} - {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="會話建立失敗"
            )
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        獲取會話資訊
        
        Args:
            session_id: 會話 ID
        
        Returns:
            會話資訊字典或 None
        """
        try:
            session_key = self.redis.get_session_key(session_id)
            session_info = await self.redis.get_json(session_key)
            
            if not session_info:
                return None
            
            # 檢查會話是否過期
            expires_at = datetime.fromisoformat(session_info["expires_at"])
            if datetime.utcnow() > expires_at:
                logger.debug(f"會話已過期: {session_id}")
                await self.delete_session(session_id)
                return None
            
            return session_info
            
        except Exception as e:
            logger.error(f"獲取會話失敗: {session_id} - {e}")
            return None
    
    async def update_session_activity(self, session_id: str) -> bool:
        """
        更新會話活動時間
        
        Args:
            session_id: 會話 ID
        
        Returns:
            更新是否成功
        """
        try:
            session_info = await self.get_session(session_id)
            
            if not session_info:
                return False
            
            # 更新活動時間
            session_info["last_activity"] = datetime.utcnow().isoformat()
            
            # 重新設定會話資料（續期）
            session_key = self.redis.get_session_key(session_id)
            ttl = session_info.get("ttl_seconds", self.default_ttl)
            
            success = await self.redis.set_json(session_key, session_info, ttl)
            
            if success:
                logger.debug(f"會話活動時間已更新: {session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新會話活動失敗: {session_id} - {e}")
            return False
    
    async def extend_session(self, session_id: str, extend_seconds: int) -> bool:
        """
        延長會話時間
        
        Args:
            session_id: 會話 ID
            extend_seconds: 延長秒數
        
        Returns:
            延長是否成功
        """
        try:
            session_info = await self.get_session(session_id)
            
            if not session_info:
                return False
            
            # 計算新的過期時間
            current_expires = datetime.fromisoformat(session_info["expires_at"])
            new_expires = current_expires + timedelta(seconds=extend_seconds)
            max_expires = datetime.utcnow() + timedelta(seconds=self.max_ttl)
            
            # 限制最大延長時間
            final_expires = min(new_expires, max_expires)
            session_info["expires_at"] = final_expires.isoformat()
            
            # 計算新的 TTL
            new_ttl = int((final_expires - datetime.utcnow()).total_seconds())
            session_info["ttl_seconds"] = new_ttl
            
            # 更新會話資料
            session_key = self.redis.get_session_key(session_id)
            success = await self.redis.set_json(session_key, session_info, new_ttl)
            
            if success:
                logger.info(f"會話時間已延長: {session_id} (+{extend_seconds}s)")
            
            return success
            
        except Exception as e:
            logger.error(f"延長會話時間失敗: {session_id} - {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """
        刪除會話
        
        Args:
            session_id: 會話 ID
        
        Returns:
            刪除是否成功
        """
        try:
            # 獲取會話資訊以取得 user_id
            session_info = await self.get_session(session_id)
            
            # 刪除會話主體
            session_key = self.redis.get_session_key(session_id)
            deleted = await self.redis.delete(session_key)
            
            # 從用戶會話索引中移除
            if session_info and "user_id" in session_info:
                user_id = session_info["user_id"]
                user_sessions_key = self.redis.get_user_key(user_id, "sessions")
                await self.redis.srem(user_sessions_key, session_id)
            
            if deleted:
                # 更新統計
                self.session_stats["deleted"] += 1
                if self.session_stats["active"] > 0:
                    self.session_stats["active"] -= 1
                
                logger.info(f"會話刪除成功: {session_id}")
            
            return bool(deleted)
            
        except Exception as e:
            logger.error(f"刪除會話失敗: {session_id} - {e}")
            return False
    
    async def get_user_sessions(self, user_id: str) -> List[str]:
        """
        獲取用戶所有會話 ID
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            會話 ID 列表
        """
        try:
            user_sessions_key = self.redis.get_user_key(user_id, "sessions")
            session_ids = await self.redis.smembers(user_sessions_key)
            return list(session_ids)
            
        except Exception as e:
            logger.error(f"獲取用戶會話失敗: {user_id} - {e}")
            return []
    
    async def get_user_active_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        獲取用戶所有活躍會話
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            活躍會話資訊列表
        """
        session_ids = await self.get_user_sessions(user_id)
        active_sessions = []
        
        for session_id in session_ids:
            session_info = await self.get_session(session_id)
            if session_info:
                active_sessions.append(session_info)
        
        return active_sessions
    
    async def delete_user_sessions(self, user_id: str) -> int:
        """
        刪除用戶所有會話
        
        Args:
            user_id: 用戶 ID
        
        Returns:
            刪除的會話數量
        """
        session_ids = await self.get_user_sessions(user_id)
        deleted_count = 0
        
        for session_id in session_ids:
            if await self.delete_session(session_id):
                deleted_count += 1
        
        # 清除用戶會話索引
        user_sessions_key = self.redis.get_user_key(user_id, "sessions")
        await self.redis.delete(user_sessions_key)
        
        logger.info(f"用戶會話清除完成: {user_id}, 刪除數量: {deleted_count}")
        return deleted_count
    
    async def cleanup_expired_sessions(self) -> int:
        """
        清理過期會話
        
        Returns:
            清理的會話數量
        """
        try:
            # 獲取所有會話鍵
            session_pattern = self.redis.get_session_key("*")
            session_keys = await self.redis.keys(session_pattern)
            
            cleaned_count = 0
            
            for session_key in session_keys:
                session_info = await self.redis.get_json(session_key)
                
                if session_info:
                    try:
                        expires_at = datetime.fromisoformat(session_info["expires_at"])
                        
                        if datetime.utcnow() > expires_at:
                            session_id = session_info["session_id"]
                            if await self.delete_session(session_id):
                                cleaned_count += 1
                                self.session_stats["expired"] += 1
                                
                    except (KeyError, ValueError) as e:
                        # 無效的會話資料，直接刪除
                        await self.redis.delete(session_key)
                        cleaned_count += 1
                        logger.warning(f"刪除無效會話資料: {session_key} - {e}")
            
            if cleaned_count > 0:
                logger.info(f"過期會話清理完成，清理數量: {cleaned_count}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理過期會話失敗: {e}")
            return 0
    
    async def is_session_valid(self, session_id: str) -> bool:
        """
        檢查會話是否有效
        
        Args:
            session_id: 會話 ID
        
        Returns:
            會話是否有效
        """
        session_info = await self.get_session(session_id)
        return session_info is not None
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """
        獲取會話統計資訊
        
        Returns:
            統計資訊字典
        """
        try:
            # 實時統計活躍會話數
            session_pattern = self.redis.get_session_key("*")
            active_keys = await self.redis.keys(session_pattern)
            actual_active = len(active_keys)
            
            # 更新統計
            self.session_stats["active"] = actual_active
            
            return {
                **self.session_stats,
                "cleanup_interval": self.cleanup_interval,
                "default_ttl": self.default_ttl,
                "max_ttl": self.max_ttl,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"獲取會話統計失敗: {e}")
            return self.session_stats
    
    def reset_stats(self):
        """重置統計資訊"""
        self.session_stats = {
            "created": 0,
            "active": 0,
            "expired": 0,
            "deleted": 0
        }
        logger.info("會話統計資訊已重置")

# 建立全域服務實例
session_service = SessionService()