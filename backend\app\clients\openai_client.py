"""
OpenAI 客戶端
實作 ChatGPT API 整合，包含錯誤處理和重試機制
"""

import os
import logging
import asyncio
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
import openai
from openai import OpenAI, AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# 設定日誌
logger = logging.getLogger(__name__)

class OpenAIConfig:
    """OpenAI 配置類"""
    
    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.model = os.getenv("CHATGPT_MODEL", "gpt-4")
        self.max_tokens = int(os.getenv("CHATGPT_MAX_TOKENS", "2000"))
        self.temperature = float(os.getenv("CHATGPT_TEMPERATURE", "0.7"))
        self.top_p = float(os.getenv("CHATGPT_TOP_P", "1.0"))
        self.frequency_penalty = float(os.getenv("CHATGPT_FREQUENCY_PENALTY", "0.0"))
        self.presence_penalty = float(os.getenv("CHATGPT_PRESENCE_PENALTY", "0.0"))
        
        # 請求配置
        self.timeout = float(os.getenv("CHATGPT_TIMEOUT", "30.0"))
        self.max_retries = int(os.getenv("CHATGPT_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("CHATGPT_RETRY_DELAY", "1.0"))
        
        # 速率限制
        self.max_requests_per_minute = int(os.getenv("CHATGPT_MAX_RPM", "60"))
        self.max_tokens_per_minute = int(os.getenv("CHATGPT_MAX_TPM", "90000"))
        
        # 驗證 API Key
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY 環境變數未設定")
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "frequency_penalty": self.frequency_penalty,
            "presence_penalty": self.presence_penalty,
            "timeout": self.timeout,
            "max_retries": self.max_retries
        }

class OpenAIError(Exception):
    """OpenAI 客戶端錯誤"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code

class OpenAIClient:
    """OpenAI 客戶端類"""
    
    def __init__(self, config: Optional[OpenAIConfig] = None):
        self.config = config or OpenAIConfig()
        
        # 初始化客戶端
        self.sync_client = OpenAI(
            api_key=self.config.api_key,
            timeout=self.config.timeout
        )
        
        self.async_client = AsyncOpenAI(
            api_key=self.config.api_key,
            timeout=self.config.timeout
        )
        
        # 統計資料
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "last_request_time": None
        }
        
        logger.info(f"OpenAI 客戶端初始化完成，模型: {self.config.model}")
    
    async def health_check(self) -> bool:
        """健康檢查"""
        try:
            # 發送簡單的測試請求
            start_time = datetime.utcnow()
            
            response = await self.async_client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                temperature=0
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 更新統計
            self.stats["last_request_time"] = end_time
            
            logger.debug(f"OpenAI 健康檢查成功，回應時間: {response_time:.1f}ms")
            return True
            
        except Exception as e:
            logger.warning(f"OpenAI 健康檢查失敗: {e}")
            return False
    
    @retry(
        retry=retry_if_exception_type((openai.RateLimitError, openai.APITimeoutError, openai.InternalServerError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成回應
        
        Args:
            messages: 對話訊息列表
            user_id: 用戶 ID
            session_id: 會話 ID
            **kwargs: 其他參數
        
        Returns:
            回應結果字典
        """
        start_time = datetime.utcnow()
        self.stats["requests_count"] += 1
        
        try:
            # 準備請求參數
            request_params = {
                "model": kwargs.get("model", self.config.model),
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
                "frequency_penalty": kwargs.get("frequency_penalty", self.config.frequency_penalty),
                "presence_penalty": kwargs.get("presence_penalty", self.config.presence_penalty),
                "user": user_id or "anonymous"
            }
            
            # 如果啟用串流模式
            if kwargs.get("stream", False):
                return await self._generate_stream_response(request_params, start_time)
            
            # 發送請求
            logger.debug(f"發送 OpenAI 請求，用戶: {user_id}, 會話: {session_id}")
            
            response = await self.async_client.chat.completions.create(**request_params)
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 解析回應
            content = response.choices[0].message.content
            finish_reason = response.choices[0].finish_reason
            
            # 統計資訊
            usage = response.usage
            prompt_tokens = usage.prompt_tokens if usage else 0
            completion_tokens = usage.completion_tokens if usage else 0
            total_tokens = usage.total_tokens if usage else 0
            
            # 計算成本（粗略估算）
            cost = self._calculate_cost(prompt_tokens, completion_tokens, request_params["model"])
            
            # 更新統計
            self._update_stats(True, response_time, total_tokens, cost)
            
            result = {
                "content": content,
                "finish_reason": finish_reason,
                "model": request_params["model"],
                "usage": {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens
                },
                "response_time_ms": response_time,
                "cost_usd": cost,
                "timestamp": end_time.isoformat(),
                "metadata": {
                    "user_id": user_id,
                    "session_id": session_id,
                    "request_params": self._sanitize_params(request_params)
                }
            }
            
            logger.debug(f"OpenAI 回應成功，Token: {total_tokens}, 時間: {response_time:.1f}ms, 成本: ${cost:.4f}")
            return result
            
        except openai.RateLimitError as e:
            error_msg = f"OpenAI 速率限制: {e}"
            logger.warning(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "rate_limit_exceeded", 429)
            
        except openai.APITimeoutError as e:
            error_msg = f"OpenAI 請求超時: {e}"
            logger.warning(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "timeout", 408)
            
        except openai.InvalidRequestError as e:
            error_msg = f"OpenAI 請求無效: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "invalid_request", 400)
            
        except openai.AuthenticationError as e:
            error_msg = f"OpenAI 認證失敗: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "authentication_failed", 401)
            
        except openai.PermissionDeniedError as e:
            error_msg = f"OpenAI 權限被拒: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "permission_denied", 403)
            
        except openai.NotFoundError as e:
            error_msg = f"OpenAI 資源未找到: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "not_found", 404)
            
        except openai.UnprocessableEntityError as e:
            error_msg = f"OpenAI 實體無法處理: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "unprocessable_entity", 422)
            
        except openai.InternalServerError as e:
            error_msg = f"OpenAI 伺服器錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "internal_server_error", 500)
            
        except Exception as e:
            error_msg = f"OpenAI 未知錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "unknown_error", 500)
    
    async def _generate_stream_response(
        self, 
        request_params: Dict[str, Any], 
        start_time: datetime
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成串流回應"""
        try:
            request_params["stream"] = True
            
            stream = await self.async_client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield {
                        "content": chunk.choices[0].delta.content,
                        "finish_reason": chunk.choices[0].finish_reason,
                        "is_final": chunk.choices[0].finish_reason is not None
                    }
            
            # 最終統計更新
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            self._update_stats(True, response_time, 0, 0.0)  # 串流模式無法精確計算 token
            
        except Exception as e:
            error_msg = f"OpenAI 串流錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise OpenAIError(error_msg, "stream_error", 500)
    
    def _calculate_cost(self, prompt_tokens: int, completion_tokens: int, model: str) -> float:
        """計算成本（基於 2024 年定價）"""
        # GPT-4 定價（每 1K tokens）
        pricing = {
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-32k": {"input": 0.06, "output": 0.12},
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
            "gpt-3.5-turbo-16k": {"input": 0.003, "output": 0.004}
        }
        
        # 預設使用 GPT-4 定價
        model_pricing = pricing.get(model, pricing["gpt-4"])
        
        input_cost = (prompt_tokens / 1000) * model_pricing["input"]
        output_cost = (completion_tokens / 1000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def _update_stats(self, success: bool, response_time: float, tokens: int = 0, cost: float = 0.0):
        """更新統計資料"""
        if success:
            self.stats["success_count"] += 1
            self.stats["total_tokens"] += tokens
            self.stats["total_cost"] += cost
        else:
            self.stats["error_count"] += 1
        
        # 更新平均回應時間
        total_requests = self.stats["success_count"] + self.stats["error_count"]
        if total_requests > 0:
            current_avg = self.stats["average_response_time"]
            self.stats["average_response_time"] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
    
    def _sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """清理參數（移除敏感資訊）"""
        sanitized = params.copy()
        
        # 移除或隱藏敏感資訊
        if "user" in sanitized:
            sanitized["user"] = "***"
        
        # 截斷過長的訊息內容
        if "messages" in sanitized:
            sanitized["messages"] = [
                {
                    "role": msg["role"],
                    "content": msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                }
                for msg in sanitized["messages"]
            ]
        
        return sanitized
    
    async def generate_embedding(self, text: str, model: str = "text-embedding-ada-002") -> Dict[str, Any]:
        """生成文本嵌入"""
        try:
            start_time = datetime.utcnow()
            
            response = await self.async_client.embeddings.create(
                model=model,
                input=text
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            embedding = response.data[0].embedding
            usage = response.usage
            
            return {
                "embedding": embedding,
                "model": model,
                "usage": {
                    "prompt_tokens": usage.prompt_tokens,
                    "total_tokens": usage.total_tokens
                },
                "response_time_ms": response_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"OpenAI 嵌入生成失敗: {e}"
            logger.error(error_msg)
            raise OpenAIError(error_msg, "embedding_error", 500)
    
    async def moderate_content(self, text: str) -> Dict[str, Any]:
        """內容審核"""
        try:
            start_time = datetime.utcnow()
            
            response = await self.async_client.moderations.create(input=text)
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            result = response.results[0]
            
            return {
                "flagged": result.flagged,
                "categories": dict(result.categories),
                "category_scores": dict(result.category_scores),
                "response_time_ms": response_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"OpenAI 內容審核失敗: {e}"
            logger.error(error_msg)
            raise OpenAIError(error_msg, "moderation_error", 500)
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取統計資料"""
        success_rate = 0.0
        if self.stats["requests_count"] > 0:
            success_rate = self.stats["success_count"] / self.stats["requests_count"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "error_rate": 1.0 - success_rate,
            "average_tokens_per_request": (
                self.stats["total_tokens"] / self.stats["success_count"]
                if self.stats["success_count"] > 0 else 0
            ),
            "average_cost_per_request": (
                self.stats["total_cost"] / self.stats["success_count"]
                if self.stats["success_count"] > 0 else 0
            )
        }
    
    def reset_stats(self):
        """重置統計資料"""
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "last_request_time": None
        }
    
    def get_available_models(self) -> List[str]:
        """獲取可用模型列表"""
        return [
            "gpt-4",
            "gpt-4-32k",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]
    
    def validate_message_format(self, messages: List[Dict[str, str]]) -> bool:
        """驗證訊息格式"""
        if not isinstance(messages, list) or len(messages) == 0:
            return False
        
        valid_roles = {"system", "user", "assistant"}
        
        for message in messages:
            if not isinstance(message, dict):
                return False
            
            if "role" not in message or "content" not in message:
                return False
            
            if message["role"] not in valid_roles:
                return False
            
            if not isinstance(message["content"], str) or len(message["content"].strip()) == 0:
                return False
        
        return True

# 全域客戶端實例（懶加載）
_openai_client: Optional[OpenAIClient] = None

def get_openai_client() -> OpenAIClient:
    """獲取 OpenAI 客戶端實例"""
    global _openai_client
    if _openai_client is None:
        _openai_client = OpenAIClient()
    return _openai_client