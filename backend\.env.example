# BPM Chatbot Backend 環境配置範例
# 複製此檔案為 .env 並填入實際值

# 應用程式設定
APP_NAME=BPM_Chatbot
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# 伺服器設定
HOST=0.0.0.0
PORT=8000
WORKERS=1
RELOAD=true

# 資料庫設定
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20
REDIS_TIMEOUT=5

# JWT 設定
JWT_SECRET_KEY=your-super-secret-jwt-key-here-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Azure AD 設定
AZURE_AD_TENANT_ID=your-tenant-id
AZURE_AD_CLIENT_ID=your-client-id
AZURE_AD_CLIENT_SECRET=your-client-secret
AZURE_AD_REDIRECT_URI=http://localhost:5173/auth/callback

# OpenAI 設定
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7
OPENAI_TIMEOUT=30

# Anthropic Claude 設定
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4000
ANTHROPIC_TEMPERATURE=0.7
ANTHROPIC_TIMEOUT=30

# Google Gemini 設定
GOOGLE_API_KEY=your-google-api-key
GOOGLE_MODEL=gemini-pro
GOOGLE_MAX_TOKENS=2048
GOOGLE_TEMPERATURE=0.7
GOOGLE_TIMEOUT=30

# 安全性設定
CORS_ORIGINS=http://localhost:5173,http://localhost:8080
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=*

# 速率限制設定
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_ENABLED=true

# 監控設定
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PROMETHEUS_ENABLED=false

# 檔案上傳設定
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=txt,pdf,doc,docx,csv
UPLOAD_FOLDER=uploads

# 快取設定
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# BPM 過濾器設定
BMP_FILTER_ENABLED=true
BMP_FILTER_THRESHOLD=0.6
BMP_FILTER_STRICT_MODE=false

# 訊息設定
MAX_MESSAGE_LENGTH=5000
MAX_CONVERSATION_LENGTH=100
AUTO_CLEANUP_ENABLED=true
CLEANUP_AFTER_DAYS=30

# MCP (Model Control Plane) 設定
MCP_ENABLED=false
MCP_ENDPOINT=http://localhost:9000
MCP_API_KEY=your-mcp-api-key

# 開發者設定
TESTING=false
MOCK_EXTERNAL_APIS=false
API_DOCS_ENABLED=true
ADMIN_EMAIL=<EMAIL>

# 生產環境設定（僅在生產環境使用）
# SECURE_SSL_REDIRECT=true
# SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
# SECURE_BROWSER_XSS_FILTER=true
# SECURE_CONTENT_TYPE_NOSNIFF=true