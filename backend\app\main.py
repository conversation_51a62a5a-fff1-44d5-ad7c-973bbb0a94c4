"""
BPM Chatbot FastAPI 主應用程式

智能 BPM 問答系統 - 專門回答 Business Process Management 相關問題
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from typing import Dict, Any

from app.config.settings import get_settings
from app.api import health

# 獲取設定
settings = get_settings()

# 應用程式設定
app = FastAPI(
    title=settings.app_name,
    description="智能 BPM 問答系統 - 專門回答 Business Process Management 相關問題",
    version=settings.app_version,
    docs_url="/docs" if settings.api_docs_enabled else None,
    redoc_url="/redoc" if settings.api_docs_enabled else None,
    openapi_url="/openapi.json" if settings.api_docs_enabled else None
)

# CORS 中介軟體設定
cors_config = settings.get_cors_config()
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_config["allow_origins"],
    allow_credentials=cors_config["allow_credentials"],
    allow_methods=cors_config["allow_methods"],
    allow_headers=cors_config["allow_headers"],
)

# 包含健康檢查路由
app.include_router(health.router)

# 基本健康檢查端點（保持向後相容）
@app.get("/health-basic", tags=["健康檢查"])
async def basic_health_check() -> Dict[str, Any]:
    """
    基本系統健康檢查端點（向後相容）
    """
    return {
        "status": "healthy",
        "service": "BPM Chatbot API",
        "version": settings.app_version,
        "environment": settings.app_environment
    }

# 根端點
@app.get("/", tags=["根目錄"])
async def root() -> Dict[str, str]:
    """
    API 根端點
    """
    return {
        "message": "歡迎使用 BPM Chatbot API",
        "docs": "/docs",
        "health": "/health"
    }

# 全域例外處理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    全域例外處理器
    """
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "系統發生錯誤，請聯絡管理員",
            "detail": str(exc) if os.getenv("DEBUG", "false").lower() == "true" else None
        }
    )

# 應用程式啟動事件
@app.on_event("startup")
async def startup_event():
    """
    應用程式啟動時執行的初始化作業
    """
    print("BPM Chatbot API starting...")
    print(f"Docs: http://localhost:8000/docs")
    print(f"Health: http://localhost:8000/health")

# 應用程式關閉事件
@app.on_event("shutdown")
async def shutdown_event():
    """
    應用程式關閉時執行的清理作業
    """
    print("BPM Chatbot API shutting down...")

# 開發模式啟動
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("RELOAD", "true").lower() == "true",
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )