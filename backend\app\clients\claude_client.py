"""
Claude 客戶端
實作 Anthropic Claude API 整合，包含錯誤處理和重試機制
"""

import os
import logging
import asyncio
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
import anthropic
from anthropic import Anthropic, AsyncAnthropic
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# 設定日誌
logger = logging.getLogger(__name__)

class ClaudeConfig:
    """Claude 配置類"""
    
    def __init__(self):
        self.api_key = os.getenv("ANTHROPIC_API_KEY")
        self.model = os.getenv("CLAUDE_MODEL", "claude-3-sonnet-20240229")
        self.max_tokens = int(os.getenv("CLAUDE_MAX_TOKENS", "2000"))
        self.temperature = float(os.getenv("CLAUDE_TEMPERATURE", "0.7"))
        self.top_p = float(os.getenv("CLAUDE_TOP_P", "1.0"))
        self.top_k = int(os.getenv("CLAUDE_TOP_K", "250"))
        
        # 請求配置
        self.timeout = float(os.getenv("CLAUDE_TIMEOUT", "30.0"))
        self.max_retries = int(os.getenv("CLAUDE_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("CLAUDE_RETRY_DELAY", "1.0"))
        
        # 速率限制
        self.max_requests_per_minute = int(os.getenv("CLAUDE_MAX_RPM", "60"))
        self.max_tokens_per_minute = int(os.getenv("CLAUDE_MAX_TPM", "40000"))
        
        # 驗證 API Key
        if not self.api_key:
            raise ValueError("ANTHROPIC_API_KEY 環境變數未設定")
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "top_k": self.top_k,
            "timeout": self.timeout,
            "max_retries": self.max_retries
        }

class ClaudeError(Exception):
    """Claude 客戶端錯誤"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code

class ClaudeClient:
    """Claude 客戶端類"""
    
    def __init__(self, config: Optional[ClaudeConfig] = None):
        self.config = config or ClaudeConfig()
        
        # 初始化客戶端
        self.sync_client = Anthropic(
            api_key=self.config.api_key,
            timeout=self.config.timeout
        )
        
        self.async_client = AsyncAnthropic(
            api_key=self.config.api_key,
            timeout=self.config.timeout
        )
        
        # 統計資料
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "last_request_time": None
        }
        
        logger.info(f"Claude 客戶端初始化完成，模型: {self.config.model}")
    
    async def health_check(self) -> bool:
        """健康檢查"""
        try:
            # 發送簡單的測試請求
            start_time = datetime.utcnow()
            
            response = await self.async_client.messages.create(
                model=self.config.model,
                max_tokens=10,
                temperature=0,
                messages=[{"role": "user", "content": "Hello"}]
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 更新統計
            self.stats["last_request_time"] = end_time
            
            logger.debug(f"Claude 健康檢查成功，回應時間: {response_time:.1f}ms")
            return True
            
        except Exception as e:
            logger.warning(f"Claude 健康檢查失敗: {e}")
            return False
    
    @retry(
        retry=retry_if_exception_type((anthropic.RateLimitError, anthropic.APITimeoutError, anthropic.InternalServerError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成回應
        
        Args:
            messages: 對話訊息列表
            user_id: 用戶 ID
            session_id: 會話 ID
            **kwargs: 其他參數
        
        Returns:
            回應結果字典
        """
        start_time = datetime.utcnow()
        self.stats["requests_count"] += 1
        
        try:
            # 轉換訊息格式（Claude 使用不同的格式）
            claude_messages = self._convert_messages_format(messages)
            
            # 準備請求參數
            request_params = {
                "model": kwargs.get("model", self.config.model),
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
                "top_k": kwargs.get("top_k", self.config.top_k),
                "messages": claude_messages
            }
            
            # 處理系統訊息
            system_message = self._extract_system_message(messages)
            if system_message:
                request_params["system"] = system_message
            
            # 如果啟用串流模式
            if kwargs.get("stream", False):
                return await self._generate_stream_response(request_params, start_time)
            
            # 發送請求
            logger.debug(f"發送 Claude 請求，用戶: {user_id}, 會話: {session_id}")
            
            response = await self.async_client.messages.create(**request_params)
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 解析回應
            content = ""
            if response.content:
                content = response.content[0].text if response.content[0].type == "text" else ""
            
            finish_reason = response.stop_reason
            
            # 統計資訊
            usage = response.usage
            input_tokens = usage.input_tokens if usage else 0
            output_tokens = usage.output_tokens if usage else 0
            total_tokens = input_tokens + output_tokens
            
            # 計算成本
            cost = self._calculate_cost(input_tokens, output_tokens, request_params["model"])
            
            # 更新統計
            self._update_stats(True, response_time, total_tokens, cost)
            
            result = {
                "content": content,
                "finish_reason": finish_reason,
                "model": request_params["model"],
                "usage": {
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "total_tokens": total_tokens
                },
                "response_time_ms": response_time,
                "cost_usd": cost,
                "timestamp": end_time.isoformat(),
                "metadata": {
                    "user_id": user_id,
                    "session_id": session_id,
                    "request_params": self._sanitize_params(request_params)
                }
            }
            
            logger.debug(f"Claude 回應成功，Token: {total_tokens}, 時間: {response_time:.1f}ms, 成本: ${cost:.4f}")
            return result
            
        except anthropic.RateLimitError as e:
            error_msg = f"Claude 速率限制: {e}"
            logger.warning(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "rate_limit_exceeded", 429)
            
        except anthropic.APITimeoutError as e:
            error_msg = f"Claude 請求超時: {e}"
            logger.warning(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "timeout", 408)
            
        except anthropic.BadRequestError as e:
            error_msg = f"Claude 請求無效: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "invalid_request", 400)
            
        except anthropic.AuthenticationError as e:
            error_msg = f"Claude 認證失敗: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "authentication_failed", 401)
            
        except anthropic.PermissionDeniedError as e:
            error_msg = f"Claude 權限被拒: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "permission_denied", 403)
            
        except anthropic.NotFoundError as e:
            error_msg = f"Claude 資源未找到: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "not_found", 404)
            
        except anthropic.InternalServerError as e:
            error_msg = f"Claude 伺服器錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "internal_server_error", 500)
            
        except Exception as e:
            error_msg = f"Claude 未知錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "unknown_error", 500)
    
    async def _generate_stream_response(
        self, 
        request_params: Dict[str, Any], 
        start_time: datetime
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成串流回應"""
        try:
            request_params["stream"] = True
            
            async with self.async_client.messages.stream(**request_params) as stream:
                async for chunk in stream:
                    if chunk.type == "content_block_delta":
                        yield {
                            "content": chunk.delta.text,
                            "finish_reason": None,
                            "is_final": False
                        }
                    elif chunk.type == "message_stop":
                        yield {
                            "content": "",
                            "finish_reason": "stop",
                            "is_final": True
                        }
            
            # 最終統計更新
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            self._update_stats(True, response_time, 0, 0.0)  # 串流模式無法精確計算 token
            
        except Exception as e:
            error_msg = f"Claude 串流錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise ClaudeError(error_msg, "stream_error", 500)
    
    def _convert_messages_format(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """轉換訊息格式為 Claude 格式"""
        claude_messages = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            # Claude 不支援 system 角色，在這裡過濾掉
            if role != "system":
                claude_messages.append({
                    "role": role,
                    "content": content
                })
        
        return claude_messages
    
    def _extract_system_message(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """提取系統訊息"""
        for message in messages:
            if message.get("role") == "system":
                return message.get("content", "")
        return None
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int, model: str) -> float:
        """計算成本（基於 2024 年定價）"""
        # Claude 定價（每 1M tokens）
        pricing = {
            "claude-3-opus-20240229": {"input": 15.0, "output": 75.0},
            "claude-3-sonnet-20240229": {"input": 3.0, "output": 15.0},
            "claude-3-haiku-20240307": {"input": 0.25, "output": 1.25},
            "claude-instant-1.2": {"input": 0.8, "output": 2.4}
        }
        
        # 預設使用 Claude 3 Sonnet 定價
        model_pricing = pricing.get(model, pricing["claude-3-sonnet-20240229"])
        
        input_cost = (input_tokens / 1000000) * model_pricing["input"]
        output_cost = (output_tokens / 1000000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def _update_stats(self, success: bool, response_time: float, tokens: int = 0, cost: float = 0.0):
        """更新統計資料"""
        if success:
            self.stats["success_count"] += 1
            self.stats["total_tokens"] += tokens
            self.stats["total_cost"] += cost
        else:
            self.stats["error_count"] += 1
        
        # 更新平均回應時間
        total_requests = self.stats["success_count"] + self.stats["error_count"]
        if total_requests > 0:
            current_avg = self.stats["average_response_time"]
            self.stats["average_response_time"] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
    
    def _sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """清理參數（移除敏感資訊）"""
        sanitized = params.copy()
        
        # 截斷過長的訊息內容
        if "messages" in sanitized:
            sanitized["messages"] = [
                {
                    "role": msg["role"],
                    "content": msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                }
                for msg in sanitized["messages"]
            ]
        
        # 截斷系統訊息
        if "system" in sanitized and len(sanitized["system"]) > 100:
            sanitized["system"] = sanitized["system"][:100] + "..."
        
        return sanitized
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """情感分析"""
        try:
            start_time = datetime.utcnow()
            
            prompt = f"""請分析以下文本的情感極性，返回 JSON 格式結果：

文本：{text}

請返回包含以下欄位的 JSON：
- sentiment: "positive", "negative", 或 "neutral"
- confidence: 0-1 之間的信心分數
- emotions: 檢測到的具體情感列表
- reasoning: 分析理由

JSON 結果："""
            
            response = await self.async_client.messages.create(
                model=self.config.model,
                max_tokens=500,
                temperature=0.1,
                messages=[{"role": "user", "content": prompt}]
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 解析 JSON 回應
            content = response.content[0].text if response.content else ""
            
            try:
                result = json.loads(content)
                result["response_time_ms"] = response_time
                result["timestamp"] = end_time.isoformat()
                return result
            except json.JSONDecodeError:
                # 如果不是有效的 JSON，返回原始內容
                return {
                    "sentiment": "unknown",
                    "confidence": 0.0,
                    "raw_response": content,
                    "response_time_ms": response_time,
                    "timestamp": end_time.isoformat()
                }
            
        except Exception as e:
            error_msg = f"Claude 情感分析失敗: {e}"
            logger.error(error_msg)
            raise ClaudeError(error_msg, "sentiment_analysis_error", 500)
    
    async def summarize_text(self, text: str, max_length: int = 200) -> Dict[str, Any]:
        """文本摘要"""
        try:
            start_time = datetime.utcnow()
            
            prompt = f"""請為以下文本生成簡潔摘要，限制在 {max_length} 字以內：

{text}

摘要："""
            
            response = await self.async_client.messages.create(
                model=self.config.model,
                max_tokens=max_length + 100,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            summary = response.content[0].text if response.content else ""
            
            return {
                "summary": summary,
                "original_length": len(text),
                "summary_length": len(summary),
                "compression_ratio": len(summary) / len(text) if len(text) > 0 else 0,
                "response_time_ms": response_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"Claude 文本摘要失敗: {e}"
            logger.error(error_msg)
            raise ClaudeError(error_msg, "summarization_error", 500)
    
    async def translate_text(self, text: str, target_language: str = "繁體中文") -> Dict[str, Any]:
        """文本翻譯"""
        try:
            start_time = datetime.utcnow()
            
            prompt = f"""請將以下文本翻譯成{target_language}：

{text}

翻譯："""
            
            response = await self.async_client.messages.create(
                model=self.config.model,
                max_tokens=len(text) * 2,  # 預估翻譯長度
                temperature=0.2,
                messages=[{"role": "user", "content": prompt}]
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            translation = response.content[0].text if response.content else ""
            
            return {
                "translation": translation,
                "target_language": target_language,
                "original_text": text,
                "response_time_ms": response_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"Claude 文本翻譯失敗: {e}"
            logger.error(error_msg)
            raise ClaudeError(error_msg, "translation_error", 500)
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取統計資料"""
        success_rate = 0.0
        if self.stats["requests_count"] > 0:
            success_rate = self.stats["success_count"] / self.stats["requests_count"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "error_rate": 1.0 - success_rate,
            "average_tokens_per_request": (
                self.stats["total_tokens"] / self.stats["success_count"]
                if self.stats["success_count"] > 0 else 0
            ),
            "average_cost_per_request": (
                self.stats["total_cost"] / self.stats["success_count"]
                if self.stats["success_count"] > 0 else 0
            )
        }
    
    def reset_stats(self):
        """重置統計資料"""
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "last_request_time": None
        }
    
    def get_available_models(self) -> List[str]:
        """獲取可用模型列表"""
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-instant-1.2"
        ]
    
    def validate_message_format(self, messages: List[Dict[str, str]]) -> bool:
        """驗證訊息格式"""
        if not isinstance(messages, list) or len(messages) == 0:
            return False
        
        valid_roles = {"system", "user", "assistant"}
        
        for message in messages:
            if not isinstance(message, dict):
                return False
            
            if "role" not in message or "content" not in message:
                return False
            
            if message["role"] not in valid_roles:
                return False
            
            if not isinstance(message["content"], str) or len(message["content"].strip()) == 0:
                return False
        
        return True

# 全域客戶端實例（懶加載）
_claude_client: Optional[ClaudeClient] = None

def get_claude_client() -> ClaudeClient:
    """獲取 Claude 客戶端實例"""
    global _claude_client
    if _claude_client is None:
        _claude_client = ClaudeClient()
    return _claude_client