# 認證系統故障排除指南

## 問題描述

原始錯誤：`TypeError: tokenCache.getAccessTokensByFilter is not a function`

這個錯誤發生在嘗試從 MSAL token cache 獲取詳細會話資訊時。

## 解決方案

### 1. 問題原因

- MSAL 瀏覽器版本中的 `getAccessTokensByFilter` 方法可能不直接暴露給開發者
- 不同版本的 MSAL 可能有不同的 API 結構
- 直接訪問內部 cache 方法可能不穩定

### 2. 修復措施

我們實施了多層次的 token 資訊獲取策略：

#### 方法 1: 靜默 Token 請求
```javascript
const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
if (response && response.expiresOn) {
  tokenExpiresOn = new Date(response.expiresOn)
}
```

#### 方法 2: localStorage 檢查
```javascript
const cacheKeys = Object.keys(localStorage).filter(key =>
  key.includes('accesstoken') &&
  key.includes(currentAccount.homeAccountId) &&
  key.includes(this.msalInstance.config.auth.clientId)
)
```

#### 方法 3: ID Token Claims
```javascript
if (claims.exp) {
  const idTokenExpiry = new Date(claims.exp * 1000)
  tokenExpiresOn = new Date(idTokenExpiry.getTime() - 30 * 60 * 1000)
}
```

### 3. 新增功能

#### 改進的會話管理
- `getTokenExpirationTime()`: 可靠地獲取 token 過期時間
- `shouldRefreshToken()`: 智能判斷是否需要刷新 token
- `isTokenExpiringSoon()`: 改進的過期檢查邏輯

#### 調試工具
- 新增 `authDebugger` 工具類
- 提供完整的認證系統診斷功能
- 在開發環境下可通過 `window.authDebugger` 訪問

## 使用調試工具

### 在瀏覽器控制台中執行：

```javascript
// 測試會話資訊
await window.authDebugger.testSessionInfo()

// 測試 token 刷新
await window.authDebugger.testTokenRefresh()

// 檢查 MSAL 快取
window.authDebugger.inspectMSALCache()

// 完整診斷
await window.authDebugger.runFullDiagnostic()
```

## 預防措施

### 1. 版本管理
- 固定 MSAL 版本以避免 API 變更
- 定期檢查 MSAL 更新和變更日誌

### 2. 錯誤處理
- 實施多層次的回退機制
- 優雅地處理 API 不可用的情況

### 3. 監控
- 添加詳細的日誌記錄
- 監控認證失敗率和 token 刷新成功率

## 測試建議

### 1. 手動測試
1. 登入應用程式
2. 在控制台執行 `await window.authDebugger.runFullDiagnostic()`
3. 檢查輸出是否包含有效的會話資訊

### 2. 自動化測試
- 添加單元測試覆蓋新的 token 管理邏輯
- 模擬不同的 MSAL 狀態進行測試

### 3. 壓力測試
- 測試長時間會話的 token 刷新
- 測試網路中斷後的恢復能力

## 相關文件

- [MSAL.js 官方文檔](https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications)
- [Azure AD 認證最佳實踐](https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-authentication-flows)

## 聯絡支援

如果問題持續存在，請提供以下資訊：
1. 瀏覽器版本和類型
2. MSAL 版本
3. 完整的錯誤堆疊
4. `window.authDebugger.runFullDiagnostic()` 的輸出
