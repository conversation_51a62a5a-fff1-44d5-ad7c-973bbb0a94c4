"""
測試配置文件
定義共用的測試夾具 (fixtures) 和設定
"""

import pytest
import asyncio
from unittest.mock import Async<PERSON><PERSON>, <PERSON><PERSON>, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import uuid

# 設定測試環境變數
import os
os.environ["TESTING"] = "true"
os.environ["REDIS_URL"] = "redis://localhost:6379/15"  # 測試專用 DB
os.environ["CHAT_ENABLE_BMP_FILTER"] = "true"

from app.models.message import Message, MessageRole, MessageStatus
from app.models.session import Session, SessionStatus
from app.models.employee import Employee
from app.models.auth import AuthRequest, AuthResponse
from app.models.model import ModelType, ModelConfig, ModelHealth


@pytest.fixture(scope="session")
def event_loop():
    """創建事件循環，支援異步測試"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_redis():
    """模擬 Redis 客戶端"""
    redis_mock = AsyncMock()
    redis_mock.get.return_value = None
    redis_mock.set.return_value = True
    redis_mock.delete.return_value = 1
    redis_mock.exists.return_value = False
    redis_mock.expire.return_value = True
    redis_mock.hget.return_value = None
    redis_mock.hset.return_value = True
    redis_mock.hgetall.return_value = {}
    redis_mock.keys.return_value = []
    return redis_mock


@pytest.fixture
def sample_employee():
    """範例員工資料"""
    return Employee(
        employee_id="emp_001",
        user_id="user_001",
        email="<EMAIL>",
        display_name="測試員工",
        department="IT部門",
        job_title="軟體工程師",
        is_active=True,
        permissions=["user"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def sample_session(sample_employee):
    """範例會話資料"""
    return Session(
        session_id=str(uuid.uuid4()),
        user_id=sample_employee.user_id,
        employee_id=sample_employee.employee_id,
        status=SessionStatus.ACTIVE,
        created_at=datetime.now(),
        last_activity=datetime.now(),
        message_count=0,
        preferred_model=ModelType.CHATGPT,
        metadata={}
    )


@pytest.fixture
def sample_message(sample_session):
    """範例訊息資料"""
    return Message(
        message_id=str(uuid.uuid4()),
        session_id=sample_session.session_id,
        content="這是一個測試訊息",
        role=MessageRole.USER,
        timestamp=datetime.now(),
        status=MessageStatus.COMPLETED,
        metadata={}
    )


@pytest.fixture
def sample_auth_request():
    """範例認證請求"""
    return AuthRequest(
        access_token="test_access_token",
        id_token="test_id_token",
        client_id="test_client_id"
    )


@pytest.fixture
def sample_auth_response(sample_employee):
    """範例認證回應"""
    return AuthResponse(
        success=True,
        employee=sample_employee,
        session_id=str(uuid.uuid4()),
        message="認證成功"
    )


@pytest.fixture
def mock_model_config():
    """模型配置模擬"""
    return {
        ModelType.CHATGPT: ModelConfig(
            model_type=ModelType.CHATGPT,
            enabled=True,
            weight=1.0,
            max_requests_per_minute=60,
            max_concurrent_requests=10,
            timeout_seconds=30
        ),
        ModelType.CLAUDE: ModelConfig(
            model_type=ModelType.CLAUDE,
            enabled=True,
            weight=0.8,
            max_requests_per_minute=40,
            max_concurrent_requests=8,
            timeout_seconds=30
        ),
        ModelType.GEMINI: ModelConfig(
            model_type=ModelType.GEMINI,
            enabled=False,
            weight=0.5,
            max_requests_per_minute=30,
            max_concurrent_requests=5,
            timeout_seconds=45
        )
    }


@pytest.fixture
def mock_model_health():
    """模型健康狀態模擬"""
    return {
        ModelType.CHATGPT: ModelHealth(
            status="healthy",
            response_time_ms=1200,
            success_rate=0.98,
            concurrent_requests=2,
            error_count=1,
            last_check=datetime.now()
        ),
        ModelType.CLAUDE: ModelHealth(
            status="healthy",
            response_time_ms=1500,
            success_rate=0.96,
            concurrent_requests=1,
            error_count=2,
            last_check=datetime.now()
        ),
        ModelType.GEMINI: ModelHealth(
            status="unhealthy",
            response_time_ms=5000,
            success_rate=0.60,
            concurrent_requests=0,
            error_count=10,
            last_check=datetime.now()
        )
    }


@pytest.fixture
def mock_openai_client():
    """模擬 OpenAI 客戶端"""
    client = AsyncMock()
    client.chat_completion.return_value = {
        "content": "這是 ChatGPT 的回應",
        "model": "gpt-3.5-turbo",
        "usage": {
            "prompt_tokens": 20,
            "completion_tokens": 30,
            "total_tokens": 50
        }
    }
    client.is_healthy.return_value = True
    client.get_health.return_value = {
        "status": "healthy",
        "response_time": 1200,
        "error_rate": 0.02
    }
    return client


@pytest.fixture
def mock_claude_client():
    """模擬 Claude 客戶端"""
    client = AsyncMock()
    client.chat_completion.return_value = {
        "content": "這是 Claude 的回應",
        "model": "claude-3",
        "usage": {
            "prompt_tokens": 22,
            "completion_tokens": 35,
            "total_tokens": 57
        }
    }
    client.is_healthy.return_value = True
    client.get_health.return_value = {
        "status": "healthy", 
        "response_time": 1500,
        "error_rate": 0.04
    }
    return client


@pytest.fixture
def mock_gemini_client():
    """模擬 Gemini 客戶端"""
    client = AsyncMock()
    client.chat_completion.return_value = {
        "content": "這是 Gemini 的回應",
        "model": "gemini-pro",
        "usage": {
            "prompt_tokens": 18,
            "completion_tokens": 25,
            "total_tokens": 43
        }
    }
    client.is_healthy.return_value = False
    client.get_health.return_value = {
        "status": "unhealthy",
        "response_time": 5000,
        "error_rate": 0.40
    }
    return client


@pytest.fixture
def mock_bmp_filter():
    """模擬 BPM 過濾器"""
    filter_mock = Mock()
    filter_mock.is_bpm_related.return_value = True
    filter_mock.get_relevance_score.return_value = 0.95
    filter_mock.suggest_keywords.return_value = ["流程", "管理", "BPM"]
    return filter_mock


@pytest.fixture
def mock_mcp_client():
    """模擬 MCP 客戶端"""
    client = AsyncMock()
    client.query_knowledge.return_value = {
        "results": ["相關的 BPM 知識"],
        "sources": ["document1.pdf", "document2.pdf"],
        "confidence": 0.87
    }
    client.is_connected.return_value = True
    return client


@pytest.fixture
def mock_logger():
    """模擬日誌記錄器"""
    return Mock()


# 清理函數
@pytest.fixture(autouse=True)
def cleanup_test_environment():
    """自動清理測試環境"""
    yield
    # 測試結束後的清理工作
    pass


# 性能測試標記
pytest.mark.slow = pytest.mark.skipif(
    os.getenv("SKIP_SLOW_TESTS", "false").lower() == "true",
    reason="跳過執行時間較長的測試"
)