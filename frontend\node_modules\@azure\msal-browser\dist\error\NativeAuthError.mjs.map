{"version": 3, "file": "NativeAuthError.mjs", "sources": ["../../src/error/NativeAuthError.ts"], "sourcesContent": [null], "names": ["NativeAuthErrorCodes.userSwitch", "NativeStatusCodes.PERSISTENT_ERROR", "NativeStatusCodes.DISABLED", "NativeAuthErrorCodes.contentError", "NativeStatusCodes.ACCOUNT_UNAVAILABLE", "NativeStatusCodes.USER_INTERACTION_REQUIRED", "NativeStatusCodes.USER_CANCEL", "BrowserAuthErrorCodes.userCancelled", "NativeStatusCodes.NO_NETWORK", "BrowserAuthErrorCodes.noNetworkConnectivity"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAyBH,MAAM,oBAAoB,GAAG,CAAC,UAAU,CAAC;AAE5B,MAAA,uBAAuB,GAAG;AACnC,IAAA,CAACA,UAA+B,GAC5B,oKAAoK;EAC1K;AAEI,MAAO,eAAgB,SAAQ,SAAS,CAAA;AAG1C,IAAA,WAAA,CAAY,SAAiB,EAAE,WAAoB,EAAE,GAAa,EAAA;AAC9D,QAAA,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAE9B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC9B,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;KAClB;AACJ,CAAA;AAED;;AAEG;AACG,SAAU,sBAAsB,CAAC,KAAsB,EAAA;IACzD,IACI,KAAK,CAAC,GAAG;QACT,KAAK,CAAC,GAAG,CAAC,MAAM;SACf,KAAK,CAAC,GAAG,CAAC,MAAM,KAAKC,gBAAkC;YACpD,KAAK,CAAC,GAAG,CAAC,MAAM,KAAKC,QAA0B,CAAC,EACtD;AACE,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;IAED,IACI,KAAK,CAAC,GAAG;QACT,KAAK,CAAC,GAAG,CAAC,KAAK;AACf,QAAA,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,oBAAoB,EAC1C;AACE,QAAA,OAAO,IAAI,CAAC;AACf,KAAA;IAED,QAAQ,KAAK,CAAC,SAAS;QACnB,KAAKC,YAAiC;AAClC,YAAA,OAAO,IAAI,CAAC;AAChB,QAAA;AACI,YAAA,OAAO,KAAK,CAAC;AACpB,KAAA;AACL,CAAC;AAED;;;;;;AAMG;SACa,qBAAqB,CACjC,IAAY,EACZ,WAAoB,EACpB,GAAa,EAAA;AAEb,IAAA,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;QACnB,QAAQ,GAAG,CAAC,MAAM;YACd,KAAKC,mBAAqC;AACtC,gBAAA,OAAO,kCAAkC,CACrC,iCAAiC,CAAC,wBAAwB,CAC7D,CAAC;YACN,KAAKC,yBAA2C;AAC5C,gBAAA,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YAC/D,KAAKC,WAA6B;AAC9B,gBAAA,OAAO,sBAAsB,CACzBC,aAAmC,CACtC,CAAC;YACN,KAAKC,UAA4B;AAC7B,gBAAA,OAAO,sBAAsB,CACzBC,qBAA2C,CAC9C,CAAC;AACT,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,IAAI,eAAe,CACtB,IAAI,EACJ,uBAAuB,CAAC,IAAI,CAAC,IAAI,WAAW,EAC5C,GAAG,CACN,CAAC;AACN;;;;"}