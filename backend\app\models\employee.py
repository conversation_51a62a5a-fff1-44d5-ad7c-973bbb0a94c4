"""
員工相關資料模型
定義員工資訊、權限和群組的資料結構
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum

class EmployeeStatus(str, Enum):
    """員工狀態列舉"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"

class DepartmentType(str, Enum):
    """部門類型列舉"""
    IT = "IT"
    HR = "Human Resources"
    FINANCE = "Finance"
    OPERATIONS = "Operations"
    SALES = "Sales"
    MARKETING = "Marketing"
    LEGAL = "Legal"
    OTHER = "Other"

class EmployeeRole(str, Enum):
    """員工角色列舉"""
    EMPLOYEE = "Employee"
    MANAGER = "Manager"
    DIRECTOR = "Director"
    VP = "Vice President"
    CEO = "Chief Executive Officer"
    ADMIN = "Administrator"

class EmployeeBase(BaseModel):
    """員工基礎資料模型"""
    
    object_id: str = Field(..., description="Azure AD Object ID")
    user_principal_name: str = Field(..., description="用戶主體名稱")
    display_name: str = Field(..., description="顯示名稱")
    given_name: Optional[str] = Field(default=None, description="名字")
    surname: Optional[str] = Field(default=None, description="姓氏")
    email: EmailStr = Field(..., description="電子郵件")
    mobile_phone: Optional[str] = Field(default=None, description="手機號碼")
    office_location: Optional[str] = Field(default=None, description="辦公室位置")
    job_title: Optional[str] = Field(default=None, description="職位")
    department: Optional[str] = Field(default=None, description="部門")
    company_name: Optional[str] = Field(default=None, description="公司名稱")
    manager_id: Optional[str] = Field(default=None, description="主管 ID")
    
    @validator('email')
    def validate_email_domain(cls, v):
        """驗證 email domain"""
        if v:
            domain = v.split('@')[-1].lower()
            # 這裡可以加入允許的 domain 檢查
            return v
        return v

class Employee(EmployeeBase):
    """完整員工資料模型"""
    
    employee_id: Optional[str] = Field(default=None, description="員工編號")
    status: EmployeeStatus = Field(default=EmployeeStatus.ACTIVE, description="員工狀態")
    hire_date: Optional[datetime] = Field(default=None, description="入職日期")
    termination_date: Optional[datetime] = Field(default=None, description="離職日期")
    last_login: Optional[datetime] = Field(default=None, description="最後登入時間")
    
    # 群組和權限
    groups: List[str] = Field(default=[], description="所屬群組列表")
    roles: List[str] = Field(default=[], description="角色列表")
    permissions: List[str] = Field(default=[], description="權限列表")
    
    # BPM 相關權限
    bmp_permissions: Dict[str, bool] = Field(default={}, description="BPM 相關權限")
    
    # 快取相關
    cached_at: datetime = Field(default_factory=datetime.utcnow, description="快取時間")
    cache_expires_at: Optional[datetime] = Field(default=None, description="快取過期時間")
    
    @property
    def is_active(self) -> bool:
        """檢查員工是否啟用"""
        return self.status == EmployeeStatus.ACTIVE
    
    @property
    def is_employee(self) -> bool:
        """檢查是否為有效員工"""
        return (
            self.is_active and
            self.email and
            self.department and
            self.job_title
        )
    
    @property
    def email_domain(self) -> str:
        """獲取 email domain"""
        return self.email.split('@')[-1].lower() if self.email else ""
    
    @property
    def full_name(self) -> str:
        """獲取全名"""
        if self.given_name and self.surname:
            return f"{self.given_name} {self.surname}"
        return self.display_name
    
    def has_permission(self, permission: str) -> bool:
        """檢查是否有特定權限"""
        return permission in self.permissions
    
    def in_group(self, group: str) -> bool:
        """檢查是否在特定群組"""
        return group in self.groups
    
    def has_role(self, role: str) -> bool:
        """檢查是否有特定角色"""
        return role in self.roles
    
    def has_bmp_permission(self, permission: str) -> bool:
        """檢查是否有特定 BPM 權限"""
        return self.bmp_permissions.get(permission, False)
    
    class Config:
        schema_extra = {
            "example": {
                "object_id": "12345678-1234-1234-1234-123456789abc",
                "user_principal_name": "<EMAIL>",
                "display_name": "John Doe",
                "given_name": "John",
                "surname": "Doe",
                "email": "<EMAIL>",
                "mobile_phone": "******-0123",
                "office_location": "New York Office",
                "job_title": "Senior Developer",
                "department": "IT",
                "company_name": "Company Inc.",
                "manager_id": "87654321-4321-4321-4321-210987654321",
                "employee_id": "EMP001",
                "status": "active",
                "hire_date": "2020-01-15T00:00:00Z",
                "groups": ["BPM Users", "IT Department"],
                "roles": ["Employee", "Developer"],
                "permissions": ["chat", "view_history"],
                "bmp_permissions": {
                    "can_use_chatbot": True,
                    "can_access_advanced_features": False
                }
            }
        }

class EmployeeCreateRequest(BaseModel):
    """建立員工請求模型"""
    
    user_principal_name: str = Field(..., description="用戶主體名稱")
    display_name: str = Field(..., description="顯示名稱")
    email: EmailStr = Field(..., description="電子郵件")
    job_title: str = Field(..., description="職位")
    department: str = Field(..., description="部門")
    manager_id: Optional[str] = Field(default=None, description="主管 ID")
    
    class Config:
        schema_extra = {
            "example": {
                "user_principal_name": "<EMAIL>",
                "display_name": "Jane Doe",
                "email": "<EMAIL>",
                "job_title": "Product Manager",
                "department": "Operations",
                "manager_id": "87654321-4321-4321-4321-210987654321"
            }
        }

class EmployeeUpdateRequest(BaseModel):
    """更新員工請求模型"""
    
    display_name: Optional[str] = Field(default=None, description="顯示名稱")
    job_title: Optional[str] = Field(default=None, description="職位")
    department: Optional[str] = Field(default=None, description="部門")
    office_location: Optional[str] = Field(default=None, description="辦公室位置")
    mobile_phone: Optional[str] = Field(default=None, description="手機號碼")
    manager_id: Optional[str] = Field(default=None, description="主管 ID")
    status: Optional[EmployeeStatus] = Field(default=None, description="員工狀態")
    
    class Config:
        schema_extra = {
            "example": {
                "job_title": "Senior Product Manager",
                "department": "Operations",
                "office_location": "San Francisco Office"
            }
        }

class EmployeeSearchRequest(BaseModel):
    """員工搜尋請求模型"""
    
    query: str = Field(..., min_length=2, description="搜尋關鍵字")
    department: Optional[str] = Field(default=None, description="部門篩選")
    status: Optional[EmployeeStatus] = Field(default=None, description="狀態篩選")
    limit: int = Field(default=10, ge=1, le=100, description="結果數量限制")
    offset: int = Field(default=0, ge=0, description="結果偏移量")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "john",
                "department": "IT",
                "status": "active",
                "limit": 10,
                "offset": 0
            }
        }

class EmployeeSearchResponse(BaseModel):
    """員工搜尋回應模型"""
    
    employees: List[Employee] = Field(..., description="員工列表")
    total: int = Field(..., description="總數量")
    offset: int = Field(..., description="偏移量")
    limit: int = Field(..., description="限制數量")
    
    class Config:
        schema_extra = {
            "example": {
                "employees": [
                    {
                        "object_id": "12345678-1234-1234-1234-123456789abc",
                        "display_name": "John Doe",
                        "email": "<EMAIL>",
                        "job_title": "Senior Developer",
                        "department": "IT"
                    }
                ],
                "total": 1,
                "offset": 0,
                "limit": 10
            }
        }

class EmployeePermission(BaseModel):
    """員工權限模型"""
    
    permission_id: str = Field(..., description="權限 ID")
    permission_name: str = Field(..., description="權限名稱")
    description: str = Field(..., description="權限描述")
    category: str = Field(..., description="權限類別")
    
    class Config:
        schema_extra = {
            "example": {
                "permission_id": "bmp.chat.send_message",
                "permission_name": "發送聊天訊息",
                "description": "允許用戶發送聊天訊息",
                "category": "chat"
            }
        }

class EmployeeGroup(BaseModel):
    """員工群組模型"""
    
    group_id: str = Field(..., description="群組 ID")
    group_name: str = Field(..., description="群組名稱")
    description: Optional[str] = Field(default=None, description="群組描述")
    member_count: int = Field(..., description="成員數量")
    
    class Config:
        schema_extra = {
            "example": {
                "group_id": "bmp-users",
                "group_name": "BPM Users",
                "description": "BPM 系統用戶群組",
                "member_count": 150
            }
        }

class EmployeeProfile(BaseModel):
    """員工個人檔案模型"""
    
    employee: Employee = Field(..., description="員工基本資訊")
    manager: Optional[Employee] = Field(default=None, description="主管資訊")
    direct_reports: List[Employee] = Field(default=[], description="直屬下屬")
    recent_activity: List[Dict[str, Any]] = Field(default=[], description="最近活動")
    
    class Config:
        schema_extra = {
            "example": {
                "employee": {
                    "display_name": "John Doe",
                    "email": "<EMAIL>",
                    "job_title": "Senior Developer",
                    "department": "IT"
                },
                "manager": {
                    "display_name": "Jane Smith",
                    "email": "<EMAIL>",
                    "job_title": "IT Manager"
                },
                "direct_reports": [],
                "recent_activity": [
                    {
                        "activity": "login",
                        "timestamp": "2024-01-01T10:00:00Z"
                    }
                ]
            }
        }