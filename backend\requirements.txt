# FastAPI 和 ASGI 伺服器
# 舊版本: fastapi==0.104.1, uvicorn==0.24.0
fastapi==0.116.1
uvicorn[standard]==0.35.0

# 資料驗證和設定  
# 舊版本: pydantic==2.5.0, pydantic-settings==2.1.0
pydantic==2.11.7
pydantic-settings==2.10.1

# HTTP 客戶端
# 舊版本: httpx==0.25.2, requests==2.31.0
httpx==0.28.1
requests==2.32.5

# 認證和安全
# 舊版本: python-jose==3.3.0, python-multipart==0.0.6, cryptography==41.0.8
python-jose[cryptography]==3.5.0
python-multipart==0.0.20
cryptography==45.0.6

# Azure AD 整合
# 舊版本: azure-identity==1.15.0, azure-keyvault-secrets==4.7.0, msal==1.25.0
azure-identity==1.24.0
azure-keyvault-secrets==4.10.0
msal==1.33.0

# Redis 資料庫
# 舊版本: redis==5.0.1, aioredis==2.0.1
redis==6.4.0
aioredis==2.0.1  # 已是最新版本

# AI 服務客戶端 - 重大更新！
# 舊版本: openai==1.3.7, anthropic==0.7.7, google-generativeai==0.3.2
openai==1.100.2
anthropic==0.64.0
google-generativeai==0.8.5

# AWS 服務
# 舊版本: boto3==1.34.0, botocore==1.34.0
boto3==1.40.12
botocore==1.40.12

# 日誌和監控
# 舊版本: structlog==23.2.0, python-json-logger==2.0.7
structlog==25.4.0
python-json-logger==3.3.0

# 工具庫
# 舊版本: python-dotenv==1.0.0, typing-extensions==4.8.0, email-validator==2.1.0
python-dotenv==1.1.1
typing-extensions==4.14.1
email-validator==2.2.0

# 測試相關 (開發環境)
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==6.2.1

# 代碼品質
black==25.1.0
flake8==7.3.0
mypy==1.17.1
psutil