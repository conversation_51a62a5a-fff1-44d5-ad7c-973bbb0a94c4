/**
 * Azure AD 認證服務
 * 處理 AAD 登入、登出、token 管理和自動刷新機制
 * 支援員工身份驗證和會話管理
 */

import { PublicClientApplication, InteractionRequiredAuthError, BrowserCacheLocation } from '@azure/msal-browser'
import { configService } from '@/config/settings'

export class AuthService {
  constructor() {
    this.msalInstance = null
    this.account = null
    this.isInitialized = false
    this.initPromise = null
    this.loginRequest = {
      scopes: ["openid", "profile", "User.Read", "email"],
      prompt: "select_account"
    }

    // 初始化 MSAL
    this.initialize()
  }

  /**
   * 初始化 MSAL 配置
   */
  async initialize() {
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._initializeMsal()
    return this.initPromise
  }

  async _initializeMsal() {
    try {
      const config = configService.getAzureAdConfig()

      if (!config.clientId || !config.tenantId) {
        throw new Error('Azure AD 配置不完整：缺少 ClientId 或 TenantId')
      }

      const msalConfig = {
        auth: {
          clientId: config.clientId,
          authority: `https://login.microsoftonline.com/${config.tenantId}`,
          redirectUri: config.redirectUri || window.location.origin + '/auth/callback',
          postLogoutRedirectUri: config.postLogoutRedirectUri || window.location.origin,
          navigateToLoginRequestUrl: true
        },
        cache: {
          cacheLocation: BrowserCacheLocation.LocalStorage,
          storeAuthStateInCookie: false
        },
        system: {
          allowNativeBroker: false,
          loggerOptions: {
            loggerCallback: (level, message, containsPii) => {
              if (containsPii) {
                return
              }

              if (configService.isDebugEnabled()) {
                console.log(`[MSAL ${level}]`, message)
              }
            },
            piiLoggingEnabled: false,
            logLevel: configService.isDebugEnabled() ? 'Verbose' : 'Warning'
          }
        }
      }

      this.msalInstance = new PublicClientApplication(msalConfig)
      await this.msalInstance.initialize()

      // 處理重定向回應
      const response = await this.msalInstance.handleRedirectPromise()
      if (response) {
        this.account = response.account
        console.log('Azure AD 登入成功:', response.account.username)
      } else {
        // 檢查是否有已存在的帳戶
        const accounts = this.msalInstance.getAllAccounts()
        if (accounts.length > 0) {
          this.account = accounts[0]
          console.log('發現已存在的 Azure AD 會話:', this.account.username)
        }
      }

      this.isInitialized = true
      console.log('Azure AD MSAL 初始化完成')

    } catch (error) {
      console.error('Azure AD MSAL 初始化失敗:', error)
      throw error
    }
  }

  /**
   * 檢查是否已認證
   */
  isAuthenticated() {
    return !!(this.account && this.isInitialized)
  }

  /**
   * 獲取當前用戶
   */
  getCurrentUser() {
    return this.account
  }

  /**
   * 登入
   */
  async login(email) {
    try {
      await this.initialize()

      if (!this.msalInstance) {
        throw new Error('MSAL 未初始化')
      }

      const loginRequest = {
        ...this.loginRequest,
        loginHint: email
      }

      // 嘗試靜默登入
      try {
        const silentRequest = {
          ...loginRequest,
          account: this.account
        }

        if (this.account) {
          const response = await this.msalInstance.acquireTokenSilent(silentRequest)
          this.account = response.account
          return this.mapAccountToUser(response.account)
        }
      } catch (error) {
        // 靜默登入失敗，繼續交互式登入
        console.log('靜默登入失敗，轉為交互式登入')
      }

      // 交互式登入
      const response = await this.msalInstance.loginPopup(loginRequest)
      this.account = response.account
      console.log(this.account)
      console.log('Azure AD 登入成功:', this.account.username)
      return this.mapAccountToUser(this.account)

    } catch (error) {
      console.error('Azure AD 登入失敗:', error)

      if (error.name === 'InteractionInProgressError') {
        throw new Error('登入程序進行中，請稍候')
      } else if (error.errorCode === 'user_cancelled') {
        throw new Error('用戶取消登入')
      } else if (error.errorCode === 'consent_required') {
        throw new Error('需要管理員同意權限')
      } else {
        throw new Error(`登入失敗: ${error.message}`)
      }
    }
  }

  /**
   * 登出
   */
  async logout() {
    try {
      await this.initialize()

      if (!this.msalInstance || !this.account) {
        this.clearLocalSession()
        return
      }

      const logoutRequest = {
        account: this.account,
        postLogoutRedirectUri: configService.getAzureAdConfig().postLogoutRedirectUri
      }

      // 清除本地會話
      this.clearLocalSession()

      // 執行 Azure AD 登出
      await this.msalInstance.logoutPopup(logoutRequest)

      console.log('Azure AD 登出成功')

    } catch (error) {
      console.error('Azure AD 登出失敗:', error)
      // 即使登出失敗也清除本地會話
      this.clearLocalSession()
      throw error
    }
  }

  /**
   * 獲取 Access Token
   */
  async getAccessToken() {
    try {
      await this.initialize()

      if (!this.msalInstance || !this.account) {
        throw new Error('用戶未登入')
      }

      const tokenRequest = {
        scopes: this.loginRequest.scopes,
        account: this.account
      }

      try {
        const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
        return response.accessToken
      } catch (error) {
        if (error instanceof InteractionRequiredAuthError) {
          // 需要交互式獲取 token
          const response = await this.msalInstance.acquireTokenPopup(tokenRequest)
          return response.accessToken
        }
        throw error
      }

    } catch (error) {
      console.error('獲取 Access Token 失敗:', error)
      throw new Error('無法獲取訪問令牌')
    }
  }

  /**
   * 檢查會話有效性
   */
  checkSession() {
    if (!this.isInitialized || !this.account) {
      return false
    }

    // 檢查 account 是否仍然有效
    const accounts = this.msalInstance?.getAllAccounts() || []
    return accounts.some(acc => acc.homeAccountId === this.account.homeAccountId)
  }

  /**
   * 檢查並刷新 Token
   */
  async checkAndRefreshToken() {
    try {
      if (!this.isAuthenticated()) {
        console.debug('用戶未認證，無法刷新 token')
        return null
      }

      // 檢查是否需要刷新 token
      const needsRefresh = await this.shouldRefreshToken(10) // 提前10分鐘刷新

      if (needsRefresh) {
        console.log('Token 即將過期，嘗試刷新...')
        try {
          // 嘗試強制刷新 token
          const newToken = await this.forceRefreshToken()
          console.log('Token 刷新成功')
          return newToken
        } catch (refreshError) {
          console.warn('強制刷新 token 失敗:', refreshError.message)
          // 刷新失敗，嘗試獲取現有 token
          const token = await this.getAccessToken()
          return token
        }
      } else {
        // 不需要刷新，直接獲取現有 token
        const token = await this.getAccessToken()
        return token
      }
    } catch (error) {
      console.warn('Token 檢查和刷新失敗:', error)
      // Token 刷新失敗，清除會話
      this.clearLocalSession()
      return null
    }
  }

  /**
   * 獲取用戶資訊
   */
  async getUserInfo() {
    if (!this.account) {
      return null
    }

    return this.mapAccountToUser(this.account)
  }

  /**
   * 映射 Azure AD Account 到用戶物件
   */
  mapAccountToUser(account) {
    if (!account) return null

    const claims = account.idTokenClaims || {}

    return {
      objectId: account.localAccountId,
      userPrincipalName: account.username,
      displayName: claims.name || account.name,
      givenName: claims.given_name,
      surname: claims.family_name,
      email: claims.email || claims.preferred_username,
      jobTitle: claims.jobTitle,
      department: claims.department,
      companyName: claims.companyName,
      officeLocation: claims.officeLocation,
      roles: ['user'], // 簡化：所有用戶都是一般用戶
      groups: ['BPM-Users'], // 簡化：所有用戶都在 BPM 用戶群組
      tenantId: account.tenantId,
      isAuthenticated: true,
      userType: 'general' // 標記為一般用戶
    }
  }

  /**
   * 清除本地會話
   */
  clearLocalSession() {
    this.account = null
    console.log('本地 Azure AD 會話已清除')
  }

  /**
   * 處理認證錯誤
   */
  handleAuthError(error) {
    console.error('認證錯誤:', error)

    if (error.errorCode === 'user_cancelled') {
      return '用戶取消了登入程序'
    } else if (error.errorCode === 'consent_required') {
      return '需要管理員同意應用程式權限'
    } else if (error.errorCode === 'interaction_in_progress') {
      return '登入程序進行中，請稍候'
    } else if (error.name === 'InteractionRequiredAuthError') {
      return '需要重新登入'
    } else {
      return `認證失敗: ${error.message}`
    }
  }

  /**
   * 獲取會話資訊
   * @returns {Object|null} 會話物件包含 created_at, expires_at 等
   */
  getSession() {
    if (!this.account) {
      return null
    }

    // 從 MSAL 獲取 token 過期時間
    const accounts = this.msalInstance?.getAllAccounts() || []
    const currentAccount = accounts.find(acc => acc.homeAccountId === this.account.homeAccountId)

    if (!currentAccount) {
      return null
    }

    // 嘗試通過靜默請求獲取 token 資訊
    let tokenExpiresOn = null
    let sessionCreatedAt = null

    try {
      // 方法1: 嘗試靜默獲取 token 來獲取過期時間
      const tokenRequest = {
        scopes: this.loginRequest.scopes,
        account: currentAccount,
        forceRefresh: false // 不強制刷新，只是為了獲取資訊
      }

      // 使用 Promise 包裝來避免阻塞
      this.msalInstance.acquireTokenSilent(tokenRequest)
        .then(response => {
          if (response && response.expiresOn) {
            tokenExpiresOn = new Date(response.expiresOn)
            console.log('從靜默請求獲取到 token 過期時間:', tokenExpiresOn)
          }
        })
        .catch(error => {
          console.debug('靜默獲取 token 資訊失敗:', error.message)
        })
    } catch (error) {
      console.debug('無法通過靜默請求獲取 token 資訊:', error)
    }

    // 方法2: 嘗試從 localStorage 獲取 token 資訊
    if (!tokenExpiresOn) {
      try {
        // MSAL 在 localStorage 中存儲 token 資訊
        const cacheKeys = Object.keys(localStorage).filter(key =>
          key.includes('accesstoken') &&
          key.includes(currentAccount.homeAccountId) &&
          key.includes(this.msalInstance.config.auth.clientId)
        )

        if (cacheKeys.length > 0) {
          // 獲取最新的 access token
          const latestKey = cacheKeys.sort().pop()
          const tokenData = localStorage.getItem(latestKey)

          if (tokenData) {
            const parsedToken = JSON.parse(tokenData)
            if (parsedToken.expires_on) {
              tokenExpiresOn = new Date(parseInt(parsedToken.expires_on) * 1000)
              console.log('從 localStorage 獲取到 token 過期時間:', tokenExpiresOn)
            }
            if (parsedToken.cached_at) {
              sessionCreatedAt = new Date(parseInt(parsedToken.cached_at) * 1000)
            }
          }
        }
      } catch (error) {
        console.debug('無法從 localStorage 獲取 token 資訊:', error)
      }
    }

    // 方法3: 嘗試從 account 的 idTokenClaims 獲取資訊
    if (!tokenExpiresOn && currentAccount.idTokenClaims) {
      try {
        const claims = currentAccount.idTokenClaims
        if (claims.exp) {
          // ID token 的過期時間，通常比 access token 長
          const idTokenExpiry = new Date(claims.exp * 1000)
          // 估算 access token 過期時間（通常比 ID token 早過期）
          tokenExpiresOn = new Date(idTokenExpiry.getTime() - 30 * 60 * 1000) // 提前30分鐘
          console.log('從 ID token 估算 access token 過期時間:', tokenExpiresOn)
        }
        if (claims.iat) {
          sessionCreatedAt = new Date(claims.iat * 1000)
        }
      } catch (error) {
        console.debug('無法從 ID token claims 獲取資訊:', error)
      }
    }

    const now = new Date()

    // 如果成功獲取到過期時間
    if (tokenExpiresOn) {
      const isValid = tokenExpiresOn > now
      const duration = tokenExpiresOn.getTime() - now.getTime()

      return {
        created_at: sessionCreatedAt || new Date(now.getTime() - 30 * 60 * 1000),
        expires_at: tokenExpiresOn,
        last_activity: now,
        duration: Math.max(duration, 0),
        is_valid: isValid,
        token_expires_on: tokenExpiresOn,
        refresh_threshold: 10 * 60 * 1000, // 10分鐘前刷新
        session_id: currentAccount.homeAccountId,
        source: 'msal_cache' // 標記資料來源
      }
    }

    // 回退方案：基本會話資訊
    const estimatedExpiry = new Date(now.getTime() + 60 * 60 * 1000) // 估計1小時後過期

    return {
      created_at: sessionCreatedAt || now,
      expires_at: estimatedExpiry,
      last_activity: now,
      duration: 60 * 60 * 1000,
      is_valid: true,
      token_expires_on: estimatedExpiry,
      refresh_threshold: 10 * 60 * 1000,
      session_id: currentAccount.homeAccountId,
      source: 'estimated' // 標記為估算值
    }
  }

  /**
   * 檢查會話是否有效
   * @returns {boolean} 會話有效性
   */
  isSessionValid() {
    const session = this.getSession()
    if (!session) {
      return false
    }

    const now = new Date()
    return session.expires_at > now && session.is_valid
  }

  /**
   * 刷新會話
   * @returns {Promise<Object>} 新的會話資訊
   */
  async refreshSession() {
    try {
      if (!this.account) {
        throw new Error('無法刷新會話：用戶未登入')
      }

      // 嘗試靜默獲取新 token
      const tokenRequest = {
        scopes: this.loginRequest.scopes,
        account: this.account,
        forceRefresh: true
      }

      const response = await this.msalInstance.acquireTokenSilent(tokenRequest)

      // 更新帳戶資訊
      this.account = response.account

      console.log('會話刷新成功')
      return this.getSession()

    } catch (error) {
      console.error('會話刷新失敗:', error)

      // 如果靜默刷新失敗，嘗試交互式刷新
      if (error instanceof InteractionRequiredAuthError) {
        try {
          const response = await this.msalInstance.acquireTokenPopup({
            scopes: this.loginRequest.scopes,
            account: this.account
          })

          this.account = response.account
          console.log('交互式會話刷新成功')
          return this.getSession()

        } catch (popupError) {
          console.error('交互式會話刷新也失敗:', popupError)
          throw new Error('會話刷新失敗，請重新登入')
        }
      }

      throw new Error('會話刷新失敗: ' + error.message)
    }
  }

  /**
   * 檢查 token 是否即將過期
   * @param {number} minutesBefore - 提前幾分鐘檢查
   * @returns {Promise<boolean>} 是否即將過期
   */
  async isTokenExpiringSoon(minutesBefore = 10) {
    try {
      if (!this.account) {
        return true // 沒有帳戶，認為已過期
      }

      // 首先從會話資訊獲取過期時間
      const session = this.getSession()
      if (session && session.expires_at) {
        const now = new Date()
        const timeDiff = session.expires_at.getTime() - now.getTime()
        const minutesRemaining = timeDiff / (1000 * 60)

        console.log(`Token 剩餘時間: ${minutesRemaining.toFixed(1)} 分鐘`)
        return minutesRemaining <= minutesBefore
      }

      // 如果會話資訊不可用，嘗試靜默獲取 token
      const tokenRequest = {
        scopes: this.loginRequest.scopes,
        account: this.account
      }

      try {
        const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
        if (response && response.expiresOn) {
          const expiresOn = new Date(response.expiresOn)
          const now = new Date()
          const timeDiff = expiresOn.getTime() - now.getTime()
          const minutesRemaining = timeDiff / (1000 * 60)

          console.log(`Token 剩餘時間 (從靜默請求): ${minutesRemaining.toFixed(1)} 分鐘`)
          return minutesRemaining <= minutesBefore
        }
      } catch (silentError) {
        console.debug('靜默獲取 token 失敗:', silentError.message)
        // 靜默獲取失敗通常意味著需要重新認證
        return true
      }

      // 如果都無法獲取，保守地認為即將過期
      return true

    } catch (error) {
      console.warn('檢查 token 過期狀態失敗:', error)
      return true
    }
  }

  /**
   * 強制刷新 token
   * @returns {Promise<string>} 新的 access token
   */
  async forceRefreshToken() {
    try {
      if (!this.account) {
        throw new Error('用戶未登入')
      }

      const tokenRequest = {
        scopes: this.loginRequest.scopes,
        account: this.account,
        forceRefresh: true // 強制刷新
      }

      const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
      this.account = response.account

      console.log('Token 強制刷新成功')
      return response.accessToken

    } catch (error) {
      console.error('強制刷新 token 失敗:', error)

      // 靜默刷新失敗，嘗試交互式刷新
      if (error instanceof InteractionRequiredAuthError) {
        const response = await this.msalInstance.acquireTokenPopup({
          scopes: this.loginRequest.scopes,
          account: this.account
        })

        this.account = response.account
        console.log('交互式 Token 刷新成功')
        return response.accessToken
      }

      throw new Error('無法強制刷新 token: ' + error.message)
    }
  }

  /**
   * 獲取 token 過期時間的可靠方法
   * @returns {Promise<Date|null>} token 過期時間
   */
  async getTokenExpirationTime() {
    try {
      if (!this.account) {
        return null
      }

      // 方法1: 嘗試靜默獲取 token 來獲取準確的過期時間
      try {
        const tokenRequest = {
          scopes: this.loginRequest.scopes,
          account: this.account,
          forceRefresh: false
        }

        const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
        if (response && response.expiresOn) {
          return new Date(response.expiresOn)
        }
      } catch (error) {
        console.debug('靜默獲取 token 過期時間失敗:', error.message)
      }

      // 方法2: 從會話資訊獲取
      const session = this.getSession()
      if (session && session.expires_at && session.source === 'msal_cache') {
        return session.expires_at
      }

      return null
    } catch (error) {
      console.warn('獲取 token 過期時間失敗:', error)
      return null
    }
  }

  /**
   * 檢查 token 是否需要刷新
   * @param {number} thresholdMinutes - 提前刷新的分鐘數
   * @returns {Promise<boolean>} 是否需要刷新
   */
  async shouldRefreshToken(thresholdMinutes = 10) {
    try {
      const expirationTime = await this.getTokenExpirationTime()
      if (!expirationTime) {
        return true // 無法獲取過期時間，保守地要求刷新
      }

      const now = new Date()
      const timeDiff = expirationTime.getTime() - now.getTime()
      const minutesRemaining = timeDiff / (1000 * 60)

      return minutesRemaining <= thresholdMinutes
    } catch (error) {
      console.warn('檢查是否需要刷新 token 失敗:', error)
      return true
    }
  }

  /**
   * 獲取認證狀態
   */
  getAuthState() {
    return {
      isAuthenticated: this.isAuthenticated(),
      isInitialized: this.isInitialized,
      user: this.getCurrentUser(),
      hasValidSession: this.checkSession(),
      session: this.getSession(),
      isLoading: false
    }
  }


}

// 創建全域實例
const authService = new AuthService()

// 導出服務實例
export default authService