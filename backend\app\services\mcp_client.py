"""
MCP (Model Context Protocol) 客戶端模組
專門處理與 AWS Lambda MCP 伺服器的連接和通信
支援 LightRAG 知識庫查詢功能
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import httpx
from fastapi import HTTPException, status
import asyncio
import backoff
from urllib.parse import urljoin

# 設定日誌
logger = logging.getLogger(__name__)

class MCPConnectionError(Exception):
    """MCP 連接異常"""
    pass

class MCPTimeoutError(Exception):
    """MCP 超時異常"""
    pass

class MCPAuthenticationError(Exception):
    """MCP 認證異常"""
    pass

class MCPClient:
    """MCP 客戶端類別"""
    
    def __init__(self):
        # Lambda MCP 伺服器配置
        self.lambda_endpoint = os.getenv("LAMBDA_MCP_ENDPOINT")
        self.lambda_api_key = os.getenv("LAMBDA_MCP_API_KEY")
        self.lambda_function_name = os.getenv("LAMBDA_MCP_FUNCTION", "lightrag-mcp-server")
        
        # AWS 配置
        self.aws_region = os.getenv("AWS_REGION", "us-east-1")
        self.aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        self.aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        
        # MCP 協定配置
        self.mcp_version = "1.0"
        self.client_name = "BPM-Chatbot-Client"
        self.client_version = "1.0.0"
        
        # 超時配置
        self.connection_timeout = int(os.getenv("MCP_CONNECTION_TIMEOUT", "10"))
        self.read_timeout = int(os.getenv("MCP_READ_TIMEOUT", "30"))
        self.total_timeout = int(os.getenv("MCP_TOTAL_TIMEOUT", "45"))
        
        # 會話管理
        self.session_id = None
        self.session_created_at = None
        self.session_expires_at = None
        
        # HTTP 客戶端配置
        self.client_config = {
            "timeout": httpx.Timeout(
                connect=self.connection_timeout,
                read=self.read_timeout,
                write=10.0,
                pool=self.total_timeout
            ),
            "limits": httpx.Limits(
                max_keepalive_connections=5,
                max_connections=10,
                keepalive_expiry=30.0
            ),
            "headers": {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": f"{self.client_name}/{self.client_version}",
                "X-MCP-Version": self.mcp_version
            },
            "follow_redirects": True,
            "verify": True
        }
        
        # 設定認證標頭
        if self.lambda_api_key:
            self.client_config["headers"]["Authorization"] = f"Bearer {self.lambda_api_key}"
        
        # 連接狀態
        self.is_connected = False
        self.last_health_check = None
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        
        # 驗證配置
        self._validate_configuration()
    
    def _validate_configuration(self) -> None:
        """驗證 MCP 客戶端配置"""
        if not self.lambda_endpoint:
            logger.warning("LAMBDA_MCP_ENDPOINT 環境變數未設定")
            
        missing_config = []
        
        if not self.lambda_endpoint:
            missing_config.append("LAMBDA_MCP_ENDPOINT")
            
        if missing_config:
            logger.error(f"MCP 客戶端配置不完整，缺少: {', '.join(missing_config)}")
    
    async def connect(self) -> bool:
        """
        建立與 MCP 伺服器的連接
        
        Returns:
            連接是否成功
        """
        if self.is_connected and self._is_session_valid():
            logger.debug("MCP 連接已存在且有效")
            return True
        
        try:
            self.connection_attempts += 1
            logger.info(f"嘗試連接 MCP 伺服器 (第 {self.connection_attempts} 次)")
            
            # 建立新會話
            session_result = await self._create_session()
            
            if session_result:
                self.is_connected = True
                self.connection_attempts = 0
                logger.info("MCP 伺服器連接成功")
                return True
            else:
                raise MCPConnectionError("無法建立 MCP 會話")
                
        except Exception as e:
            logger.error(f"MCP 伺服器連接失敗: {e}")
            self.is_connected = False
            
            if self.connection_attempts >= self.max_connection_attempts:
                logger.error(f"已達最大連接嘗試次數 ({self.max_connection_attempts})")
                raise MCPConnectionError(f"無法連接到 MCP 伺服器: {e}")
            
            return False
    
    async def _create_session(self) -> bool:
        """建立 MCP 會話"""
        if not self.lambda_endpoint:
            raise MCPConnectionError("MCP 端點未配置")
        
        try:
            # MCP 會話初始化請求
            init_request = {
                "jsonrpc": "2.0",
                "id": f"init_{int(datetime.utcnow().timestamp())}",
                "method": "initialize",
                "params": {
                    "protocolVersion": self.mcp_version,
                    "clientInfo": {
                        "name": self.client_name,
                        "version": self.client_version
                    },
                    "capabilities": {
                        "resources": {},
                        "tools": {},
                        "prompts": {},
                        "experimental": {}
                    }
                }
            }
            
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    urljoin(self.lambda_endpoint, "initialize"),
                    json=init_request
                )
                
                response.raise_for_status()
                init_response = response.json()
            
            # 處理初始化回應
            if "error" in init_response:
                logger.error(f"MCP 初始化錯誤: {init_response['error']}")
                return False
            
            result = init_response.get("result", {})
            server_info = result.get("serverInfo", {})
            
            logger.info(f"MCP 伺服器資訊: {server_info.get('name', 'Unknown')} v{server_info.get('version', 'Unknown')}")
            
            # 設定會話資訊
            self.session_id = f"session_{int(datetime.utcnow().timestamp())}"
            self.session_created_at = datetime.utcnow()
            self.session_expires_at = self.session_created_at + timedelta(hours=1)  # 1小時過期
            
            return True
            
        except httpx.TimeoutException:
            logger.error("MCP 初始化超時")
            raise MCPTimeoutError("MCP 伺服器連接超時")
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                logger.error("MCP 認證失敗")
                raise MCPAuthenticationError("MCP 伺服器認證失敗")
            else:
                logger.error(f"MCP HTTP 錯誤: {e.response.status_code}")
                raise MCPConnectionError(f"MCP 伺服器回應錯誤: {e.response.status_code}")
        except Exception as e:
            logger.error(f"MCP 初始化失敗: {e}")
            return False
    
    def _is_session_valid(self) -> bool:
        """檢查會話是否有效"""
        if not self.session_expires_at:
            return False
        
        return datetime.utcnow() < self.session_expires_at
    
    @backoff.on_exception(
        backoff.expo,
        (httpx.RequestError, httpx.HTTPStatusError, MCPTimeoutError),
        max_tries=3,
        max_time=60
    )
    async def query_lightrag(
        self,
        query: str,
        index_name: Optional[str] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        查詢 LightRAG 知識庫
        
        Args:
            query: 查詢字串
            index_name: 索引名稱
            limit: 結果數量限制
            similarity_threshold: 相似度閾值
            filters: 過濾條件
        
        Returns:
            查詢結果
        """
        
        # 確保連接有效
        if not await self.connect():
            raise MCPConnectionError("無法建立 MCP 連接")
        
        try:
            # 建構 LightRAG 查詢請求
            query_request = {
                "jsonrpc": "2.0",
                "id": f"query_{int(datetime.utcnow().timestamp())}",
                "method": "lightrag/search",
                "params": {
                    "query": query,
                    "index": index_name or os.getenv("LIGHTRAG_INDEX", "bmp_knowledge"),
                    "limit": limit,
                    "similarity_threshold": similarity_threshold,
                    "filters": filters or {},
                    "session_id": self.session_id
                }
            }
            
            logger.debug(f"發送 LightRAG 查詢: {query[:100]}...")
            
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    urljoin(self.lambda_endpoint, "query"),
                    json=query_request
                )
                
                response.raise_for_status()
                query_response = response.json()
            
            # 處理查詢回應
            if "error" in query_response:
                error = query_response["error"]
                logger.error(f"LightRAG 查詢錯誤: {error}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"知識庫查詢失敗: {error.get('message', '未知錯誤')}"
                )
            
            result = query_response.get("result", {})
            
            # 格式化結果
            formatted_result = {
                "query": query,
                "total_results": result.get("total", 0),
                "results": result.get("documents", []),
                "search_time_ms": result.get("search_time_ms", 0),
                "similarity_threshold": similarity_threshold,
                "index_name": result.get("index", index_name),
                "timestamp": datetime.utcnow().isoformat(),
                "session_id": self.session_id
            }
            
            logger.info(
                f"LightRAG 查詢完成: '{query[:50]}...' "
                f"-> {formatted_result['total_results']} 個結果"
            )
            
            return formatted_result
            
        except httpx.TimeoutException:
            logger.error("LightRAG 查詢超時")
            raise MCPTimeoutError("知識庫查詢超時")
        except httpx.HTTPStatusError as e:
            logger.error(f"LightRAG HTTP 錯誤: {e.response.status_code}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"知識庫服務錯誤: {e.response.status_code}"
            )
        except Exception as e:
            logger.error(f"LightRAG 查詢失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="知識庫查詢過程發生錯誤"
            )
    
    @backoff.on_exception(
        backoff.expo,
        (httpx.RequestError, httpx.HTTPStatusError),
        max_tries=2,
        max_time=30
    )
    async def get_document_by_id(self, document_id: str) -> Dict[str, Any]:
        """
        根據 ID 獲取文件
        
        Args:
            document_id: 文件 ID
        
        Returns:
            文件內容
        """
        
        if not await self.connect():
            raise MCPConnectionError("無法建立 MCP 連接")
        
        try:
            doc_request = {
                "jsonrpc": "2.0",
                "id": f"doc_{int(datetime.utcnow().timestamp())}",
                "method": "lightrag/get_document",
                "params": {
                    "document_id": document_id,
                    "session_id": self.session_id
                }
            }
            
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    urljoin(self.lambda_endpoint, "document"),
                    json=doc_request
                )
                
                response.raise_for_status()
                doc_response = response.json()
            
            if "error" in doc_response:
                logger.error(f"文件獲取錯誤: {doc_response['error']}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文件不存在或無法存取"
                )
            
            return doc_response.get("result", {})
            
        except Exception as e:
            logger.error(f"文件獲取失敗: {document_id} - {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="文件獲取失敗"
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        檢查 MCP 客戶端和伺服器健康狀態
        
        Returns:
            健康檢查結果
        """
        health_result = {
            "client_status": "unknown",
            "server_status": "unknown",
            "connection_status": "disconnected",
            "session_valid": False,
            "last_check": datetime.utcnow().isoformat(),
            "endpoint": self.lambda_endpoint
        }
        
        try:
            # 檢查客戶端狀態
            health_result["client_status"] = "healthy"
            health_result["connection_status"] = "connected" if self.is_connected else "disconnected"
            health_result["session_valid"] = self._is_session_valid()
            
            # 嘗試連接並檢查伺服器
            if await self.connect():
                ping_request = {
                    "jsonrpc": "2.0",
                    "id": "health_check",
                    "method": "ping",
                    "params": {
                        "session_id": self.session_id
                    }
                }
                
                async with httpx.AsyncClient(**self.client_config) as client:
                    response = await client.post(
                        urljoin(self.lambda_endpoint, "health"),
                        json=ping_request
                    )
                    
                    if response.status_code == 200:
                        health_result["server_status"] = "healthy"
                    else:
                        health_result["server_status"] = f"unhealthy (HTTP {response.status_code})"
            
            self.last_health_check = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"健康檢查失敗: {e}")
            health_result["server_status"] = f"error: {str(e)}"
        
        return health_result
    
    async def disconnect(self) -> None:
        """斷開 MCP 連接"""
        try:
            if self.is_connected and self.session_id:
                # 發送斷開連接請求
                disconnect_request = {
                    "jsonrpc": "2.0",
                    "id": "disconnect",
                    "method": "shutdown",
                    "params": {
                        "session_id": self.session_id
                    }
                }
                
                async with httpx.AsyncClient(**self.client_config) as client:
                    await client.post(
                        urljoin(self.lambda_endpoint, "shutdown"),
                        json=disconnect_request
                    )
                
                logger.info("MCP 連接已正常關閉")
        
        except Exception as e:
            logger.warning(f"關閉 MCP 連接時發生錯誤: {e}")
        
        finally:
            # 清理狀態
            self.is_connected = False
            self.session_id = None
            self.session_created_at = None
            self.session_expires_at = None
            self.connection_attempts = 0
    
    def get_connection_info(self) -> Dict[str, Any]:
        """獲取連接資訊"""
        return {
            "endpoint": self.lambda_endpoint,
            "function_name": self.lambda_function_name,
            "aws_region": self.aws_region,
            "is_connected": self.is_connected,
            "session_id": self.session_id,
            "session_created_at": self.session_created_at.isoformat() if self.session_created_at else None,
            "session_expires_at": self.session_expires_at.isoformat() if self.session_expires_at else None,
            "session_valid": self._is_session_valid(),
            "connection_attempts": self.connection_attempts,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "client_version": self.client_version,
            "mcp_version": self.mcp_version
        }

# 建立全域客戶端實例
mcp_client = MCPClient()

# 清理函數（用於應用程式關閉時）
async def cleanup_mcp_client():
    """清理 MCP 客戶端資源"""
    if mcp_client:
        await mcp_client.disconnect()
        logger.info("MCP 客戶端資源已清理")