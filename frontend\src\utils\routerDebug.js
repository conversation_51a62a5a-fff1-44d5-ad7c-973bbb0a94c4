/**
 * 路由調試工具
 * 用於測試和調試路由導航問題
 */

import router from '@/router'
import { safeNavigate, safeRedirect, navigateToLogin } from '@/utils/routerUtils'

export class RouterDebugger {
  constructor() {
    this.logPrefix = '[RouterDebug]'
    this.navigationHistory = []
    this.setupNavigationTracking()
  }

  /**
   * 設置導航追蹤
   */
  setupNavigationTracking() {
    // 追蹤路由變化
    router.afterEach((to, from) => {
      this.navigationHistory.push({
        timestamp: new Date(),
        from: from?.fullPath || 'initial',
        to: to.fullPath,
        type: 'navigation'
      })
      
      // 只保留最近 50 條記錄
      if (this.navigationHistory.length > 50) {
        this.navigationHistory = this.navigationHistory.slice(-50)
      }
    })

    // 追蹤導航錯誤
    router.onError((error, to, from) => {
      this.navigationHistory.push({
        timestamp: new Date(),
        from: from?.fullPath || 'unknown',
        to: to?.fullPath || 'unknown',
        type: 'error',
        error: error.message
      })
    })
  }

  /**
   * 測試重複導航問題
   */
  async testRedundantNavigation() {
    console.group(`${this.logPrefix} 測試重複導航`)
    
    try {
      const currentPath = router.currentRoute.value.fullPath
      console.log('當前路徑:', currentPath)
      
      // 測試導航到相同位置
      console.log('測試導航到相同位置...')
      await safeNavigate(currentPath)
      console.log('✅ 重複導航測試通過')
      
      // 測試帶查詢參數的重複導航
      const pathWithQuery = currentPath + (currentPath.includes('?') ? '&test=1' : '?test=1')
      console.log('測試帶查詢參數的導航:', pathWithQuery)
      await safeNavigate(pathWithQuery)
      
      // 再次導航到相同位置
      console.log('再次測試相同位置導航...')
      await safeNavigate(pathWithQuery)
      console.log('✅ 帶查詢參數的重複導航測試通過')
      
    } catch (error) {
      console.error('❌ 重複導航測試失敗:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 測試登入重定向
   */
  async testLoginRedirect() {
    console.group(`${this.logPrefix} 測試登入重定向`)
    
    try {
      const currentPath = router.currentRoute.value.fullPath
      console.log('當前路徑:', currentPath)
      
      // 測試登入重定向
      console.log('測試登入重定向...')
      await navigateToLogin(currentPath)
      console.log('✅ 登入重定向測試通過')
      
      // 測試重複的登入重定向
      console.log('測試重複的登入重定向...')
      await navigateToLogin(currentPath)
      console.log('✅ 重複登入重定向測試通過')
      
    } catch (error) {
      console.error('❌ 登入重定向測試失敗:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 測試安全重定向
   */
  async testSafeRedirect() {
    console.group(`${this.logPrefix} 測試安全重定向`)
    
    try {
      const currentPath = router.currentRoute.value.path
      
      // 測試重定向到當前位置
      console.log('測試重定向到當前位置...')
      await safeRedirect(currentPath)
      console.log('✅ 當前位置重定向測試通過')
      
      // 測試循環重定向檢測
      console.log('測試循環重定向檢測...')
      // 模擬循環重定向場景
      const testRoute = {
        path: '/test',
        query: { redirect: '/test' }
      }
      
      // 暫時修改當前路由來模擬循環
      const originalRoute = router.currentRoute.value
      Object.defineProperty(router, 'currentRoute', {
        value: { value: testRoute },
        configurable: true
      })
      
      await safeRedirect('/test', '/fallback')
      console.log('✅ 循環重定向檢測測試通過')
      
      // 恢復原始路由
      Object.defineProperty(router, 'currentRoute', {
        value: { value: originalRoute },
        configurable: true
      })
      
    } catch (error) {
      console.error('❌ 安全重定向測試失敗:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 顯示導航歷史
   */
  showNavigationHistory() {
    console.group(`${this.logPrefix} 導航歷史`)
    
    if (this.navigationHistory.length === 0) {
      console.log('暫無導航記錄')
    } else {
      console.table(this.navigationHistory.map(entry => ({
        時間: entry.timestamp.toLocaleTimeString(),
        類型: entry.type,
        從: entry.from,
        到: entry.to,
        錯誤: entry.error || '-'
      })))
    }
    
    console.groupEnd()
  }

  /**
   * 清除導航歷史
   */
  clearNavigationHistory() {
    this.navigationHistory = []
    console.log(`${this.logPrefix} 導航歷史已清除`)
  }

  /**
   * 獲取當前路由資訊
   */
  getCurrentRouteInfo() {
    console.group(`${this.logPrefix} 當前路由資訊`)
    
    const route = router.currentRoute.value
    console.log('路徑:', route.path)
    console.log('完整路徑:', route.fullPath)
    console.log('路由名稱:', route.name)
    console.log('查詢參數:', route.query)
    console.log('路由參數:', route.params)
    console.log('Meta 資訊:', route.meta)
    
    console.groupEnd()
  }

  /**
   * 執行完整的路由診斷
   */
  async runFullDiagnostic() {
    console.group(`${this.logPrefix} 完整路由診斷`)
    
    console.log('開始路由系統診斷...')
    
    // 當前路由資訊
    this.getCurrentRouteInfo()
    
    // 導航歷史
    this.showNavigationHistory()
    
    // 測試重複導航
    await this.testRedundantNavigation()
    
    // 測試登入重定向
    await this.testLoginRedirect()
    
    // 測試安全重定向
    await this.testSafeRedirect()
    
    console.log('路由系統診斷完成')
    console.groupEnd()
  }

  /**
   * 監控路由錯誤
   */
  startErrorMonitoring() {
    console.log(`${this.logPrefix} 開始監控路由錯誤`)
    
    const originalOnError = router.onError
    router.onError = (error, to, from) => {
      console.warn(`${this.logPrefix} 檢測到路由錯誤:`, {
        error: error.message,
        to: to?.fullPath,
        from: from?.fullPath
      })
      
      // 調用原始錯誤處理器
      if (originalOnError) {
        originalOnError(error, to, from)
      }
    }
  }
}

// 創建全域實例
export const routerDebugger = new RouterDebugger()

// 在開發環境下將調試器添加到 window 對象
if (import.meta.env.DEV) {
  window.routerDebugger = routerDebugger
  console.log('🔧 路由調試器已載入，使用 window.routerDebugger 進行調試')
}

export default routerDebugger
