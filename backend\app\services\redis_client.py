"""
Redis 客戶端服務
提供 Redis 連線和基本操作
"""

import os
import json
import logging
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta
import redis.asyncio as redis
from redis.asyncio import Redis
from fastapi import HTTPException, status

# 設定日誌
logger = logging.getLogger(__name__)

class RedisClient:
    """Redis 客戶端類別"""
    
    def __init__(self):
        # Redis 連線配置
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_db = int(os.getenv("REDIS_DB", "0"))
        self.redis_password = os.getenv("REDIS_PASSWORD")
        
        # 連線池配置
        self.max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
        self.retry_on_timeout = True
        self.health_check_interval = 30
        
        # 建立連線池
        self._redis_pool: Optional[Redis] = None
        
        # Key 前綴配置
        self.session_prefix = "session:"
        self.user_prefix = "user:"
        self.chat_prefix = "chat:"
        self.employee_prefix = "employee:"
        self.cache_prefix = "cache:"
    
    async def initialize(self) -> None:
        """初始化 Redis 連線"""
        try:
            self._redis_pool = redis.from_url(
                self.redis_url,
                db=self.redis_db,
                password=self.redis_password,
                max_connections=self.max_connections,
                retry_on_timeout=self.retry_on_timeout,
                health_check_interval=self.health_check_interval,
                decode_responses=True,
                encoding='utf-8'
            )
            
            # 測試連線
            await self._redis_pool.ping()
            logger.info("Redis 連線建立成功")
            
        except Exception as e:
            logger.error(f"Redis 連線失敗: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="資料庫服務暫時無法使用"
            )
    
    async def close(self) -> None:
        """關閉 Redis 連線"""
        if self._redis_pool:
            await self._redis_pool.close()
            logger.info("Redis 連線已關閉")
    
    @property
    def redis(self) -> Redis:
        """獲取 Redis 連線"""
        if not self._redis_pool:
            raise RuntimeError("Redis 尚未初始化")
        return self._redis_pool
    
    # === 基本操作 ===
    
    async def get(self, key: str) -> Optional[str]:
        """獲取值"""
        try:
            return await self.redis.get(key)
        except Exception as e:
            logger.error(f"Redis GET 操作失敗: {key} - {e}")
            return None
    
    async def set(self, key: str, value: str, ttl_seconds: Optional[int] = None) -> bool:
        """設定值"""
        try:
            if ttl_seconds:
                await self.redis.setex(key, ttl_seconds, value)
            else:
                await self.redis.set(key, value)
            return True
        except Exception as e:
            logger.error(f"Redis SET 操作失敗: {key} - {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """刪除鍵"""
        try:
            result = await self.redis.delete(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis DELETE 操作失敗: {key} - {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """檢查鍵是否存在"""
        try:
            result = await self.redis.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis EXISTS 操作失敗: {key} - {e}")
            return False
    
    async def expire(self, key: str, ttl_seconds: int) -> bool:
        """設定過期時間"""
        try:
            result = await self.redis.expire(key, ttl_seconds)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis EXPIRE 操作失敗: {key} - {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """獲取剩餘生存時間"""
        try:
            return await self.redis.ttl(key)
        except Exception as e:
            logger.error(f"Redis TTL 操作失敗: {key} - {e}")
            return -1
    
    # === 列表操作 ===
    
    async def lpush(self, key: str, *values: str) -> int:
        """從左側推入列表"""
        try:
            return await self.redis.lpush(key, *values)
        except Exception as e:
            logger.error(f"Redis LPUSH 操作失敗: {key} - {e}")
            return 0
    
    async def rpush(self, key: str, *values: str) -> int:
        """從右側推入列表"""
        try:
            return await self.redis.rpush(key, *values)
        except Exception as e:
            logger.error(f"Redis RPUSH 操作失敗: {key} - {e}")
            return 0
    
    async def lrange(self, key: str, start: int, end: int) -> List[str]:
        """獲取列表範圍"""
        try:
            return await self.redis.lrange(key, start, end)
        except Exception as e:
            logger.error(f"Redis LRANGE 操作失敗: {key} - {e}")
            return []
    
    async def ltrim(self, key: str, start: int, end: int) -> bool:
        """修剪列表"""
        try:
            await self.redis.ltrim(key, start, end)
            return True
        except Exception as e:
            logger.error(f"Redis LTRIM 操作失敗: {key} - {e}")
            return False
    
    async def llen(self, key: str) -> int:
        """獲取列表長度"""
        try:
            return await self.redis.llen(key)
        except Exception as e:
            logger.error(f"Redis LLEN 操作失敗: {key} - {e}")
            return 0
    
    # === 集合操作 ===
    
    async def sadd(self, key: str, *members: str) -> int:
        """添加到集合"""
        try:
            return await self.redis.sadd(key, *members)
        except Exception as e:
            logger.error(f"Redis SADD 操作失敗: {key} - {e}")
            return 0
    
    async def srem(self, key: str, *members: str) -> int:
        """從集合移除"""
        try:
            return await self.redis.srem(key, *members)
        except Exception as e:
            logger.error(f"Redis SREM 操作失敗: {key} - {e}")
            return 0
    
    async def smembers(self, key: str) -> set:
        """獲取集合成員"""
        try:
            return await self.redis.smembers(key)
        except Exception as e:
            logger.error(f"Redis SMEMBERS 操作失敗: {key} - {e}")
            return set()
    
    async def sismember(self, key: str, member: str) -> bool:
        """檢查是否為集合成員"""
        try:
            return await self.redis.sismember(key, member)
        except Exception as e:
            logger.error(f"Redis SISMEMBER 操作失敗: {key} - {e}")
            return False
    
    # === JSON 操作輔助方法 ===
    
    async def get_json(self, key: str) -> Optional[Dict[str, Any]]:
        """獲取 JSON 值"""
        try:
            value = await self.get(key)
            if value:
                return json.loads(value)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失敗: {key} - {e}")
            return None
        except Exception as e:
            logger.error(f"獲取 JSON 失敗: {key} - {e}")
            return None
    
    async def set_json(self, key: str, value: Dict[str, Any], ttl_seconds: Optional[int] = None) -> bool:
        """設定 JSON 值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return await self.set(key, json_str, ttl_seconds)
        except json.JSONEncodeError as e:
            logger.error(f"JSON 序列化失敗: {key} - {e}")
            return False
        except Exception as e:
            logger.error(f"設定 JSON 失敗: {key} - {e}")
            return False
    
    # === 批量操作 ===
    
    async def keys(self, pattern: str) -> List[str]:
        """搜尋鍵（謹慎使用）"""
        try:
            return await self.redis.keys(pattern)
        except Exception as e:
            logger.error(f"Redis KEYS 操作失敗: {pattern} - {e}")
            return []
    
    async def mget(self, *keys: str) -> List[Optional[str]]:
        """批量獲取"""
        try:
            return await self.redis.mget(*keys)
        except Exception as e:
            logger.error(f"Redis MGET 操作失敗: {keys} - {e}")
            return [None] * len(keys)
    
    async def mset(self, mapping: Dict[str, str]) -> bool:
        """批量設定"""
        try:
            await self.redis.mset(mapping)
            return True
        except Exception as e:
            logger.error(f"Redis MSET 操作失敗: {e}")
            return False
    
    # === 高級操作 ===
    
    async def pipeline(self):
        """建立管道"""
        try:
            return self.redis.pipeline()
        except Exception as e:
            logger.error(f"建立 Redis 管道失敗: {e}")
            raise
    
    async def transaction(self, func, *watches):
        """執行事務"""
        try:
            return await self.redis.transaction(func, *watches)
        except Exception as e:
            logger.error(f"Redis 事務執行失敗: {e}")
            raise
    
    # === 管理操作 ===
    
    async def flushdb(self) -> bool:
        """清空當前資料庫"""
        try:
            await self.redis.flushdb()
            logger.warning("Redis 當前資料庫已清空")
            return True
        except Exception as e:
            logger.error(f"清空 Redis 資料庫失敗: {e}")
            return False
    
    async def info(self) -> Dict[str, Any]:
        """獲取 Redis 資訊"""
        try:
            return await self.redis.info()
        except Exception as e:
            logger.error(f"獲取 Redis 資訊失敗: {e}")
            return {}
    
    async def ping(self) -> bool:
        """測試連線"""
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis PING 失敗: {e}")
            return False
    
    # === 輔助方法 ===
    
    def build_key(self, prefix: str, *parts: str) -> str:
        """構建鍵名"""
        key_parts = [prefix] + list(parts)
        return ":".join(str(part) for part in key_parts if part)
    
    def get_session_key(self, session_id: str) -> str:
        """獲取會話鍵"""
        return self.build_key(self.session_prefix, session_id)
    
    def get_user_key(self, user_id: str, suffix: str = "") -> str:
        """獲取用戶鍵"""
        return self.build_key(self.user_prefix, user_id, suffix)
    
    def get_chat_key(self, session_id: str) -> str:
        """獲取聊天鍵"""
        return self.build_key(self.chat_prefix, session_id)
    
    def get_employee_key(self, employee_id: str) -> str:
        """獲取員工鍵"""
        return self.build_key(self.employee_prefix, employee_id)
    
    def get_cache_key(self, key: str) -> str:
        """獲取快取鍵"""
        return self.build_key(self.cache_prefix, key)

# 建立全域客戶端實例
redis_client = RedisClient()


async def get_redis_client() -> RedisClient:
    """獲取 Redis 客戶端實例"""
    if not redis_client._redis_pool:
        await redis_client.initialize()
    return redis_client