/**
 * 全域導航攔截器
 * 統一處理所有路由導航，避免重複導航錯誤
 */

import router from '@/router'
import { isSameRoute } from '@/utils/routerUtils'

class NavigationInterceptor {
  constructor() {
    this.pendingNavigations = new Set()
    this.lastNavigation = null
    this.setupInterceptor()
  }

  /**
   * 設置導航攔截器
   */
  setupInterceptor() {
    // 攔截 router.push
    const originalPush = router.push.bind(router)
    router.push = (to) => {
      return this.interceptNavigation('push', to, originalPush)
    }

    // 攔截 router.replace
    const originalReplace = router.replace.bind(router)
    router.replace = (to) => {
      return this.interceptNavigation('replace', to, originalReplace)
    }

    // 監聽導航完成
    router.afterEach((to, from) => {
      this.onNavigationComplete(to, from)
    })

    // 監聽導航錯誤
    router.onError((error, to, from) => {
      this.onNavigationError(error, to, from)
    })
  }

  /**
   * 攔截導航請求
   */
  async interceptNavigation(method, to, originalMethod) {
    try {
      const navigationKey = this.getNavigationKey(method, to)
      const currentRoute = router.currentRoute.value
      
      // 檢查是否為重複導航
      if (this.isDuplicateNavigation(to, currentRoute)) {
        console.debug(`[NavigationInterceptor] 跳過重複導航: ${navigationKey}`)
        return Promise.resolve()
      }

      // 檢查是否有相同的導航正在進行
      if (this.pendingNavigations.has(navigationKey)) {
        console.debug(`[NavigationInterceptor] 導航已在進行中: ${navigationKey}`)
        return Promise.resolve()
      }

      // 記錄導航開始
      this.pendingNavigations.add(navigationKey)
      this.lastNavigation = { method, to, timestamp: Date.now() }

      console.debug(`[NavigationInterceptor] 開始導航: ${navigationKey}`)

      // 執行原始導航
      const result = await originalMethod(to)
      
      return result

    } catch (error) {
      // 處理導航錯誤
      if (error.message && error.message.includes('Avoided redundant navigation')) {
        console.debug(`[NavigationInterceptor] 忽略重複導航錯誤: ${error.message}`)
        return Promise.resolve()
      }
      
      console.error(`[NavigationInterceptor] 導航失敗:`, error)
      throw error
    }
  }

  /**
   * 導航完成處理
   */
  onNavigationComplete(to, from) {
    // 清除所有待處理的導航
    this.pendingNavigations.clear()
    console.debug(`[NavigationInterceptor] 導航完成: ${to.fullPath}`)
  }

  /**
   * 導航錯誤處理
   */
  onNavigationError(error, to, from) {
    // 清除相關的待處理導航
    const navigationKey = this.getNavigationKey('push', to)
    this.pendingNavigations.delete(navigationKey)
    
    if (error.message && error.message.includes('Avoided redundant navigation')) {
      console.debug(`[NavigationInterceptor] 忽略重複導航錯誤: ${error.message}`)
    } else {
      console.error(`[NavigationInterceptor] 導航錯誤:`, error)
    }
  }

  /**
   * 檢查是否為重複導航
   */
  isDuplicateNavigation(to, currentRoute) {
    const targetRoute = typeof to === 'string' ? { path: to, query: {} } : to
    
    // 使用精確的路由比較
    return isSameRoute(currentRoute, targetRoute)
  }

  /**
   * 生成導航鍵
   */
  getNavigationKey(method, to) {
    const targetRoute = typeof to === 'string' ? { path: to, query: {} } : to
    const queryString = new URLSearchParams(targetRoute.query || {}).toString()
    return `${method}:${targetRoute.path}${queryString ? '?' + queryString : ''}`
  }

  /**
   * 獲取待處理的導航數量
   */
  getPendingNavigationCount() {
    return this.pendingNavigations.size
  }

  /**
   * 清除所有待處理的導航
   */
  clearPendingNavigations() {
    this.pendingNavigations.clear()
    console.debug('[NavigationInterceptor] 已清除所有待處理的導航')
  }

  /**
   * 獲取最後一次導航資訊
   */
  getLastNavigation() {
    return this.lastNavigation
  }

  /**
   * 檢查是否有導航正在進行
   */
  hasNavigationInProgress() {
    return this.pendingNavigations.size > 0
  }

  /**
   * 強制執行導航（跳過重複檢查）
   */
  async forceNavigate(method, to) {
    try {
      const originalMethod = method === 'replace' ? router.replace : router.push
      return await originalMethod.call(router, to)
    } catch (error) {
      if (error.message && error.message.includes('Avoided redundant navigation')) {
        console.debug(`[NavigationInterceptor] 強制導航時忽略重複錯誤: ${error.message}`)
        return Promise.resolve()
      }
      throw error
    }
  }

  /**
   * 重置攔截器狀態
   */
  reset() {
    this.pendingNavigations.clear()
    this.lastNavigation = null
    console.debug('[NavigationInterceptor] 攔截器狀態已重置')
  }

  /**
   * 獲取調試資訊
   */
  getDebugInfo() {
    return {
      pendingNavigations: Array.from(this.pendingNavigations),
      lastNavigation: this.lastNavigation,
      hasNavigationInProgress: this.hasNavigationInProgress()
    }
  }
}

// 創建全域實例
export const navigationInterceptor = new NavigationInterceptor()

// 在開發環境下將攔截器添加到 window 對象
if (import.meta.env.DEV) {
  window.navigationInterceptor = navigationInterceptor
  console.log('🔧 導航攔截器已載入，使用 window.navigationInterceptor 進行調試')
}

export default navigationInterceptor
