#!/usr/bin/env python
"""
測試執行腳本
執行所有單元測試並生成覆蓋率報告
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_tests(test_type="unit", coverage=True, verbose=True, pattern=None):
    """
    執行測試
    
    Args:
        test_type: 測試類型 ('unit', 'integration', 'e2e', 'all')
        coverage: 是否生成覆蓋率報告
        verbose: 詳細輸出
        pattern: 測試檔案模式
    """
    cmd = ["python", "-m", "pytest"]
    
    # 設定測試目錄
    if test_type == "unit":
        cmd.append("tests/unit")
    elif test_type == "integration":
        cmd.append("tests/integration")
    elif test_type == "e2e":
        cmd.append("tests/e2e")
    elif test_type == "all":
        cmd.append("tests")
    else:
        raise ValueError(f"不支援的測試類型: {test_type}")
    
    # 添加選項
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term-missing"])
    
    if pattern:
        cmd.extend(["-k", pattern])
    
    # 添加標記
    cmd.extend(["-m", f"{test_type}"])
    
    print(f"執行命令: {' '.join(cmd)}")
    print("=" * 50)
    
    # 執行測試
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n測試被中斷")
        return False
    except Exception as e:
        print(f"執行測試時發生錯誤: {e}")
        return False


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="執行 BPM Chatbot 後端測試")
    
    parser.add_argument(
        "--type", "-t",
        choices=["unit", "integration", "e2e", "all"],
        default="unit",
        help="測試類型 (預設: unit)"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="不生成覆蓋率報告"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="安靜模式"
    )
    
    parser.add_argument(
        "--pattern", "-k",
        help="測試檔案或函數模式"
    )
    
    parser.add_argument(
        "--fast",
        action="store_true",
        help="跳過執行較慢的測試"
    )
    
    args = parser.parse_args()
    
    # 設定環境變數
    os.environ["TESTING"] = "true"
    if args.fast:
        os.environ["SKIP_SLOW_TESTS"] = "true"
    
    print("BPM Chatbot 後端測試執行器")
    print("=" * 50)
    print(f"測試類型: {args.type}")
    print(f"覆蓋率報告: {'否' if args.no_coverage else '是'}")
    print(f"詳細輸出: {'否' if args.quiet else '是'}")
    if args.pattern:
        print(f"測試模式: {args.pattern}")
    print("=" * 50)
    
    success = run_tests(
        test_type=args.type,
        coverage=not args.no_coverage,
        verbose=not args.quiet,
        pattern=args.pattern
    )
    
    if success:
        print("\n✅ 所有測試通過！")
        if not args.no_coverage:
            print("📊 覆蓋率報告已生成在 htmlcov/ 目錄")
    else:
        print("\n❌ 測試失敗！")
        sys.exit(1)


if __name__ == "__main__":
    main()