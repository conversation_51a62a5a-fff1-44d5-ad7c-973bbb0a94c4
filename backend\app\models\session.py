"""
會話資料模型
定義會話的資料結構和序列化邏輯
"""

import json
import uuid
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from enum import Enum

class SessionStatus(str, Enum):
    """會話狀態列舉"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    TERMINATED = "terminated"

class SessionType(str, Enum):
    """會話類型列舉"""
    CHAT = "chat"
    ADMIN = "admin"
    API = "api"
    SYSTEM = "system"

class SessionBase(BaseModel):
    """會話基礎模型"""
    
    user_id: str = Field(..., description="用戶 ID")
    employee_id: str = Field(..., description="員工 ID")
    session_type: SessionType = Field(default=SessionType.CHAT, description="會話類型")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="建立時間")
    expires_at: datetime = Field(..., description="過期時間")
    
    @validator('expires_at')
    def validate_expires_at(cls, v, values):
        """驗證過期時間"""
        created_at = values.get('created_at', datetime.utcnow())
        if v <= created_at:
            raise ValueError("過期時間必須晚於建立時間")
        
        # 限制最大會話時間為 24 小時
        max_expires = created_at + timedelta(hours=24)
        if v > max_expires:
            raise ValueError("會話時間不能超過 24 小時")
        
        return v

class Session(SessionBase):
    """完整會話模型"""
    
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="會話 ID")
    status: SessionStatus = Field(default=SessionStatus.ACTIVE, description="會話狀態")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="最後活動時間")
    
    # 客戶端資訊
    client_info: Optional[Dict[str, Any]] = Field(default={}, description="客戶端資訊")
    
    # 會話配置
    ttl_seconds: int = Field(default=1800, description="生存時間（秒）")
    max_messages: int = Field(default=1000, description="最大訊息數")
    
    # 統計資訊
    message_count: int = Field(default=0, description="訊息數量")
    total_tokens: int = Field(default=0, description="總 Token 數")
    
    # AI 模型偏好
    preferred_model: Optional[str] = Field(default=None, description="偏好的 AI 模型")
    model_settings: Optional[Dict[str, Any]] = Field(default={}, description="模型設定")
    
    # 會話標籤和分類
    tags: List[str] = Field(default=[], description="會話標籤")
    category: Optional[str] = Field(default=None, description="會話分類")
    
    # 附加資料
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")
    
    @property
    def is_active(self) -> bool:
        """檢查會話是否活躍"""
        return (
            self.status == SessionStatus.ACTIVE and
            datetime.utcnow() < self.expires_at
        )
    
    @property
    def is_expired(self) -> bool:
        """檢查會話是否過期"""
        return datetime.utcnow() >= self.expires_at
    
    @property
    def remaining_time(self) -> timedelta:
        """獲取剩餘時間"""
        if self.is_expired:
            return timedelta(0)
        return self.expires_at - datetime.utcnow()
    
    @property
    def remaining_seconds(self) -> int:
        """獲取剩餘秒數"""
        return max(0, int(self.remaining_time.total_seconds()))
    
    @property
    def duration(self) -> timedelta:
        """獲取會話持續時間"""
        return self.last_activity - self.created_at
    
    @property
    def idle_time(self) -> timedelta:
        """獲取閒置時間"""
        return datetime.utcnow() - self.last_activity
    
    def update_activity(self) -> None:
        """更新活動時間"""
        self.last_activity = datetime.utcnow()
        if self.status == SessionStatus.INACTIVE:
            self.status = SessionStatus.ACTIVE
    
    def extend_session(self, seconds: int) -> None:
        """延長會話時間"""
        max_extension = timedelta(hours=24)
        current_duration = self.expires_at - self.created_at
        
        if current_duration + timedelta(seconds=seconds) <= max_extension:
            self.expires_at += timedelta(seconds=seconds)
            self.ttl_seconds += seconds
        else:
            # 限制在最大時間內
            max_expires = self.created_at + max_extension
            self.expires_at = max_expires
            self.ttl_seconds = int((max_expires - self.created_at).total_seconds())
    
    def terminate(self, reason: Optional[str] = None) -> None:
        """終止會話"""
        self.status = SessionStatus.TERMINATED
        if reason:
            self.metadata["termination_reason"] = reason
        self.metadata["terminated_at"] = datetime.utcnow().isoformat()
    
    def add_message_stats(self, token_count: int) -> None:
        """添加訊息統計"""
        self.message_count += 1
        self.total_tokens += token_count
        self.update_activity()
    
    def set_preferred_model(self, model_name: str, settings: Optional[Dict[str, Any]] = None) -> None:
        """設定偏好的 AI 模型"""
        self.preferred_model = model_name
        if settings:
            self.model_settings = settings
    
    def add_tag(self, tag: str) -> None:
        """添加標籤"""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """移除標籤"""
        if tag in self.tags:
            self.tags.remove(tag)
    
    def to_redis_dict(self) -> Dict[str, Any]:
        """轉換為 Redis 儲存格式"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "employee_id": self.employee_id,
            "session_type": self.session_type.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "expires_at": self.expires_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "client_info": self.client_info,
            "ttl_seconds": self.ttl_seconds,
            "max_messages": self.max_messages,
            "message_count": self.message_count,
            "total_tokens": self.total_tokens,
            "preferred_model": self.preferred_model,
            "model_settings": self.model_settings,
            "tags": self.tags,
            "category": self.category,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_redis_dict(cls, data: Dict[str, Any]) -> "Session":
        """從 Redis 資料建立會話物件"""
        # 處理時間戳
        for time_field in ["created_at", "expires_at", "last_activity"]:
            if isinstance(data.get(time_field), str):
                data[time_field] = datetime.fromisoformat(data[time_field])
        
        # 處理列舉類型
        if isinstance(data.get("session_type"), str):
            data["session_type"] = SessionType(data["session_type"])
        
        if isinstance(data.get("status"), str):
            data["status"] = SessionStatus(data["status"])
        
        return cls(**data)
    
    def to_json(self) -> str:
        """轉換為 JSON 字串"""
        return json.dumps(self.to_redis_dict(), ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> "Session":
        """從 JSON 字串建立會話物件"""
        data = json.loads(json_str)
        return cls.from_redis_dict(data)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            SessionStatus: lambda v: v.value,
            SessionType: lambda v: v.value
        }
        
        schema_extra = {
            "example": {
                "session_id": "sess_550e8400-e29b-41d4-a716-446655440000",
                "user_id": "user_789",
                "employee_id": "emp_456",
                "session_type": "chat",
                "status": "active",
                "created_at": "2024-01-01T10:00:00Z",
                "expires_at": "2024-01-01T10:30:00Z",
                "last_activity": "2024-01-01T10:15:00Z",
                "ttl_seconds": 1800,
                "message_count": 5,
                "preferred_model": "chatgpt",
                "tags": ["bpm", "process_optimization"],
                "metadata": {
                    "source": "web_interface",
                    "language": "zh-TW"
                }
            }
        }

class SessionCreateRequest(BaseModel):
    """建立會話請求模型"""
    
    session_type: SessionType = Field(default=SessionType.CHAT, description="會話類型")
    ttl_seconds: int = Field(default=1800, ge=300, le=86400, description="生存時間（秒）")
    client_info: Optional[Dict[str, Any]] = Field(default={}, description="客戶端資訊")
    preferred_model: Optional[str] = Field(default=None, description="偏好的 AI 模型")
    tags: Optional[List[str]] = Field(default=[], description="會話標籤")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")
    
    @validator('ttl_seconds')
    def validate_ttl(cls, v):
        if v < 300:  # 最小 5 分鐘
            raise ValueError("會話時間不能少於 5 分鐘")
        if v > 86400:  # 最大 24 小時
            raise ValueError("會話時間不能超過 24 小時")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "session_type": "chat",
                "ttl_seconds": 3600,
                "client_info": {
                    "user_agent": "Mozilla/5.0...",
                    "ip_address": "*************"
                },
                "preferred_model": "chatgpt",
                "tags": ["bpm", "urgent"],
                "metadata": {
                    "department": "IT",
                    "priority": "high"
                }
            }
        }

class SessionResponse(BaseModel):
    """會話回應模型"""
    
    session_id: str = Field(..., description="會話 ID")
    user_id: str = Field(..., description="用戶 ID")
    status: str = Field(..., description="會話狀態")
    created_at: str = Field(..., description="建立時間")
    expires_at: str = Field(..., description="過期時間")
    last_activity: str = Field(..., description="最後活動時間")
    remaining_seconds: int = Field(..., description="剩餘秒數")
    message_count: int = Field(..., description="訊息數量")
    preferred_model: Optional[str] = Field(default=None, description="偏好的 AI 模型")
    tags: List[str] = Field(..., description="會話標籤")
    
    @classmethod
    def from_session(cls, session: Session) -> "SessionResponse":
        """從 Session 物件建立回應"""
        return cls(
            session_id=session.session_id,
            user_id=session.user_id,
            status=session.status.value,
            created_at=session.created_at.isoformat(),
            expires_at=session.expires_at.isoformat(),
            last_activity=session.last_activity.isoformat(),
            remaining_seconds=session.remaining_seconds,
            message_count=session.message_count,
            preferred_model=session.preferred_model,
            tags=session.tags
        )

class SessionListResponse(BaseModel):
    """會話列表回應模型"""
    
    sessions: List[SessionResponse] = Field(..., description="會話列表")
    total: int = Field(..., description="總會話數")
    active_count: int = Field(..., description="活躍會話數")
    
    class Config:
        schema_extra = {
            "example": {
                "sessions": [
                    {
                        "session_id": "sess_123",
                        "user_id": "user_456",
                        "status": "active",
                        "created_at": "2024-01-01T10:00:00Z",
                        "expires_at": "2024-01-01T10:30:00Z",
                        "remaining_seconds": 1200,
                        "message_count": 5
                    }
                ],
                "total": 10,
                "active_count": 3
            }
        }

class SessionUpdateRequest(BaseModel):
    """會話更新請求模型"""
    
    extend_seconds: Optional[int] = Field(default=None, ge=0, le=86400, description="延長秒數")
    preferred_model: Optional[str] = Field(default=None, description="偏好的 AI 模型")
    add_tags: Optional[List[str]] = Field(default=[], description="要添加的標籤")
    remove_tags: Optional[List[str]] = Field(default=[], description="要移除的標籤")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="更新的附加資料")
    
    class Config:
        schema_extra = {
            "example": {
                "extend_seconds": 1800,
                "preferred_model": "claude",
                "add_tags": ["important"],
                "remove_tags": ["test"],
                "metadata": {
                    "updated_reason": "user_request"
                }
            }
        }

class SessionStatsResponse(BaseModel):
    """會話統計回應模型"""
    
    total_sessions: int = Field(..., description="總會話數")
    active_sessions: int = Field(..., description="活躍會話數")
    expired_sessions: int = Field(..., description="過期會話數")
    terminated_sessions: int = Field(..., description="終止會話數")
    average_duration_minutes: float = Field(..., description="平均持續時間（分鐘）")
    total_messages: int = Field(..., description="總訊息數")
    total_tokens: int = Field(..., description="總 Token 數")
    most_used_models: List[Dict[str, Any]] = Field(..., description="最常用模型")
    
    class Config:
        schema_extra = {
            "example": {
                "total_sessions": 150,
                "active_sessions": 25,
                "expired_sessions": 100,
                "terminated_sessions": 25,
                "average_duration_minutes": 45.5,
                "total_messages": 2500,
                "total_tokens": 150000,
                "most_used_models": [
                    {"model": "chatgpt", "count": 80, "percentage": 53.3},
                    {"model": "claude", "count": 45, "percentage": 30.0},
                    {"model": "gemini", "count": 25, "percentage": 16.7}
                ]
            }
        }