# Implementation Plan

## Task Overview

BPM Chatbot 的實作將分為六個主要階段：專案初始化、認證系統建立、後端核心功能、前端介面開發、多模型整合、以及最終整合測試。每個任務都專注於特定檔案的建立或修改，確保系統的模組化和可測試性。

## Tasks

- [x] 1. 專案結構初始化
  - 建立基本專案目錄結構和設定檔
  - 目的：建立開發環境基礎
  - _Requirements: 架構需求_

- [x] 1.1 建立後端專案結構
  - 檔案：backend/app/__init__.py, backend/requirements.txt, backend/.env.example
  - 建立 FastAPI 應用程式基礎結構
  - 設定 Python 3.12 依賴項目
  - 目的：建立後端開發環境
  - _Requirements: 1.1, 1.2_

- [x] 1.2 建立前端專案結構  
  - 檔案：frontend/package.json, frontend/vite.config.ts, frontend/tsconfig.json
  - 初始化 Vue.js 3 + TypeScript + Vite 專案
  - 安裝 Azure AD SDK 和相關依賴
  - 目的：建立前端開發環境
  - _Requirements: 1.1, 3.1_

- [x] 1.3 設定 Docker 和部署配置
  - 檔案：docker-compose.yml, backend/Dockerfile, frontend/Dockerfile
  - 建立本地開發環境容器化設定
  - 包含 Redis 服務設定
  - 目的：統一開發和部署環境
  - _Requirements: 架構需求_

- [x] 2. 認證系統實作
  - 建立 Azure AD 整合和員工身份驗證
  - 目的：確保只有公司員工能存取系統
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 2.1 後端認證中介軟體
  - 檔案：backend/app/middleware/auth.py
  - 實作 AAD token 驗證中介軟體
  - 建立員工身份驗證邏輯
  - 目的：保護所有 API 端點
  - _Requirements: 2.1, 2.2_

- [x] 2.2 員工服務模組
  - 檔案：backend/app/services/employee_service.py
  - 實作員工身份驗證和權限檢查
  - 整合 Microsoft Graph API
  - 目的：驗證用戶是否為公司員工
  - _Requirements: 2.2_

- [x] 2.3 認證資料模型
  - 檔案：backend/app/models/auth.py, backend/app/models/employee.py
  - 定義認證相關的 Pydantic 模型
  - 包含 Employee, AuthRequest, AuthResponse 模型
  - 目的：標準化認證資料結構
  - _Requirements: 2.1, 2.2_

- [x] 2.4 前端 AAD 整合
  - 檔案：frontend/src/services/authService.ts
  - 實作 Azure AD 登入服務
  - 建立 token 管理和自動刷新機制
  - 目的：處理前端使用者認證
  - _Requirements: 2.1, 3.1_

- [x] 2.5 登入元件實作
  - 檔案：frontend/src/components/LoginComponent.vue
  - 建立 AAD 登入介面元件
  - 處理登入狀態和錯誤顯示
  - 目的：提供使用者登入介面
  - _Requirements: 2.1, 3.1_

- [x] 3. Redis 資料層實作
  - 建立資料存取和會話管理
  - 目的：提供持久化的用戶會話和聊天記錄
  - _Requirements: 4.1_

- [x] 3.1 Redis 客戶端服務
  - 檔案：backend/app/services/redis_client.py
  - 實作 Redis 連線和基本操作
  - 包含會話管理和聊天記錄存取
  - 目的：提供 Redis 資料存取介面
  - _Requirements: 4.1_

- [x] 3.2 會話管理服務
  - 檔案：backend/app/services/session_service.py
  - 實作用戶會話建立、更新、清理邏輯
  - 整合 Redis 進行會話持久化
  - 目的：管理用戶會話狀態
  - _Requirements: 4.1_

- [x] 3.3 聊天記錄模型
  - 檔案：backend/app/models/message.py, backend/app/models/session.py
  - 定義訊息和會話的資料模型
  - 包含 Redis 序列化/反序列化邏輯
  - 目的：標準化聊天資料結構
  - _Requirements: 3.3, 4.1_

- [x] 4. BPM 過濾系統
  - 實作 BPM 問題識別和過濾邏輯
  - 目的：確保只回答 BPM 相關問題
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 4.1 BPM 過濾服務
  - 檔案：backend/app/services/bpm_filter.py
  - 實作 BPM 相關性檢測演算法
  - 建立關鍵字和語義分析邏輯
  - 目的：過濾非 BPM 相關問題
  - _Requirements: 2.1, 2.2_

- [x] 4.2 過濾規則配置
  - 檔案：backend/app/config/bpm_keywords.py
  - 定義 BPM 相關關鍵字和規則
  - 建立可配置的過濾參數
  - 目的：提供靈活的過濾規則管理
  - _Requirements: 2.2_

- [x] 5. 多模型整合系統
  - 實作 ChatGPT、Claude、Gemini 模型路由
  - 目的：提供使用者選擇不同 AI 模型的能力
  - _Requirements: 1.1, 1.3_

- [x] 5.1 模型路由服務
  - 檔案：backend/app/services/model_router.py
  - 實作多模型路由和負載平衡邏輯
  - 建立模型健康檢查機制
  - 目的：統一管理多個 AI 模型存取
  - _Requirements: 1.1, 1.3_

- [x] 5.2 OpenAI 客戶端
  - 檔案：backend/app/clients/openai_client.py
  - 實作 ChatGPT API 整合
  - 包含錯誤處理和重試機制
  - 目的：提供 ChatGPT 模型存取
  - _Requirements: 1.1_

- [x] 5.3 Claude 客戶端
  - 檔案：backend/app/clients/claude_client.py
  - 實作 Claude API 整合
  - 包含 Anthropic API 呼叫邏輯
  - 目的：提供 Claude 模型存取
  - _Requirements: 1.1_

- [x] 5.4 Gemini 客戶端
  - 檔案：backend/app/clients/gemini_client.py
  - 實作 Google Gemini API 整合
  - 包含 Google AI Studio API 呼叫
  - 目的：提供 Gemini 模型存取
  - _Requirements: 1.1_

- [x] 5.5 模型資料模型
  - 檔案：backend/app/models/model.py
  - 定義模型類型、回應、使用統計等資料結構
  - 包含 ModelType, ModelResponse, ModelUsage 模型
  - 目的：標準化模型相關資料結構
  - _Requirements: 1.1, 1.3_

- [x] 6. AWS Lambda MCP 整合
  - 建立與 LightRAG 的 MCP 連接
  - 目的：透過 AWS Lambda 存取 BPM 知識庫
  - _Requirements: 1.2, 1.3_

- [x] 6.1 MCP 客戶端實作
  - 檔案：backend/app/services/mcp_client.py
  - 實作 AWS Lambda MCP server 連接
  - 建立 LightRAG 查詢介面
  - 目的：提供知識庫查詢功能
  - _Requirements: 1.2_

- [ ] 6.2 Lambda 函數配置
  - 檔案：lambda/mcp_server.py, lambda/requirements.txt
  - 建立 AWS Lambda MCP server 函數
  - 實作 LightRAG 整合邏輯
  - 目的：建立雲端知識庫服務
  - _Requirements: 1.2_

- [x] 7. 核心聊天功能
  - 實作主要的聊天邏輯和 API
  - 目的：提供完整的聊天服務
  - _Requirements: 1.1, 1.2, 1.3, 3.1, 3.2, 3.3_

- [x] 7.1 聊天服務實作
  - 檔案：backend/app/services/chat_service.py
  - 整合所有服務提供統一的聊天邏輯
  - 包含訊息處理、模型路由、結果格式化
  - 目的：提供核心聊天業務邏輯
  - _Leverage: model_router.py, bmp_filter.py, mcp_client.py, redis_client.py_
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 7.2 聊天控制器
  - 檔案：backend/app/controllers/chat_controller.py
  - 實作聊天相關的 HTTP API 端點
  - 包含 /api/chat/message, /api/chat/history 等端點
  - 目的：提供聊天功能的 REST API
  - _Leverage: chat_service.py, auth.py_
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 7.3 模型控制器
  - 檔案：backend/app/controllers/model_controller.py
  - 實作模型選擇和管理的 API 端點
  - 包含 /api/models/available, /api/models/select 等
  - 目的：提供模型管理功能
  - _Leverage: model_router.py_
  - _Requirements: 1.3_

- [-] 8. 前端聊天介面
  - 建立完整的聊天使用者介面
  - 目的：提供直觀的聊天體驗
  - _Requirements: 3.1, 3.2, 3.3, 5.1, 5.2, 5.3_

- [x] 8.1 Pinia 狀態管理
  - 檔案：frontend/src/stores/chatStore.ts, frontend/src/stores/authStore.ts
  - 建立聊天和認證的狀態管理
  - 包含訊息歷史、用戶狀態、模型選擇等
  - 目的：集中管理應用程式狀態
  - _Requirements: 3.1, 3.2, 3.3_

- [-] 8.2 聊天容器元件
  - 檔案：frontend/src/components/ChatContainer.vue
  - 建立主要的聊天介面容器
  - 整合所有聊天相關子元件
  - 目的：提供聊天功能的主要界面
  - _Leverage: authStore.ts, chatStore.ts_
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 8.3 訊息列表元件
  - 檔案：frontend/src/components/MessageList.vue
  - 實作訊息顯示和歷史管理
  - 包含訊息複製、滾動、時間戳等功能
  - 目的：顯示對話歷史
  - _Requirements: 3.3_

- [x] 8.4 訊息輸入元件
  - 檔案：frontend/src/components/MessageInput.vue
  - 建立訊息輸入和發送介面
  - 支援 Enter 發送、字數限制等功能
  - 目的：提供訊息輸入介面
  - _Requirements: 3.1, 3.2_

- [x] 8.5 模型選擇元件
  - 檔案：frontend/src/components/ModelSelector.vue
  - 實作 AI 模型選擇介面
  - 顯示模型可用性和效能資訊
  - 目的：讓使用者選擇 AI 模型
  - _Requirements: 1.3, 5.3_

- [x] 8.6 單一訊息元件
  - 檔案：frontend/src/components/Message.vue
  - 建立單一訊息的顯示元件
  - 包含使用者/助手訊息區分、複製功能
  - 目的：標準化訊息顯示格式
  - _Requirements: 3.3, 5.3_

- [ ] 9. API 服務整合
  - 建立前端與後端的 API 通訊
  - 目的：連接前後端功能
  - _Requirements: 所有需求_

- [x] 9.1 HTTP 客戶端服務
  - 檔案：frontend/src/services/apiClient.ts
  - 建立統一的 HTTP 請求處理
  - 包含認證 token 注入、錯誤處理
  - 目的：提供標準化的 API 通訊
  - _Leverage: authService.ts_
  - _Requirements: 2.1, 3.1_

- [x] 9.2 聊天 API 服務
  - 檔案：frontend/src/services/chatApi.ts
  - 實作聊天相關的 API 呼叫
  - 包含發送訊息、獲取歷史等功能
  - 目的：封裝聊天 API 邏輯
  - _Leverage: apiClient.ts_
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 9.3 模型 API 服務
  - 檔案：frontend/src/services/modelApi.ts
  - 實作模型相關的 API 呼叫
  - 包含獲取可用模型、選擇模型等
  - 目的：封裝模型管理 API
  - _Leverage: apiClient.ts_
  - _Requirements: 1.3_

- [ ] 10. 樣式和主題設計
  - 實作符合品牌的 UI 設計
  - 目的：提供美觀且一致的使用者介面
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 10.1 主題配置
  - 檔案：frontend/src/assets/styles/theme.css, frontend/src/assets/styles/variables.css
  - 定義 #008787 主色調和設計系統
  - 建立響應式設計變數
  - 目的：建立一致的視覺設計
  - _Requirements: 5.1, 5.2_

- [-] 10.2 元件樣式
  - 檔案：各元件的 <style> 區塊
  - 實作所有元件的樣式設計
  - 確保響應式和無障礙設計
  - 目的：提供美觀的元件外觀
  - _Leverage: theme.css, variables.css_
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 11. 路由和導航
  - 建立應用程式路由系統
  - 目的：提供頁面導航和路由保護
  - _Requirements: 3.1, 5.1_

- [x] 11.1 Vue Router 配置
  - 檔案：frontend/src/router/index.ts
  - 設定應用程式路由和導航守衛
  - 實作認證保護的路由
  - 目的：管理頁面導航和存取控制
  - _Leverage: authStore.ts_
  - _Requirements: 3.1_

- [x] 11.2 主要頁面元件
  - 檔案：frontend/src/views/LoginView.vue, frontend/src/views/ChatView.vue
  - 建立登入頁面和聊天頁面
  - 整合相關元件和功能
  - 目的：提供主要的使用者介面頁面
  - _Leverage: LoginComponent.vue, ChatContainer.vue_
  - _Requirements: 3.1, 5.1_

- [ ] 12. 測試實作
  - 建立完整的測試覆蓋
  - 目的：確保系統品質和可靠性
  - _Requirements: 所有需求_

- [-] 12.1 後端單元測試
  - 檔案：backend/tests/ 目錄下的測試檔案
  - 撰寫所有服務和控制器的單元測試
  - 包含認證、聊天、模型路由等測試
  - 目的：確保後端邏輯正確性
  - _Requirements: 所有後端需求_

- [ ] 12.2 前端單元測試
  - 檔案：frontend/src/tests/ 目錄下的測試檔案
  - 撰寫元件和服務的單元測試
  - 使用 Vitest 和 Vue Test Utils
  - 目的：確保前端功能正確性
  - _Requirements: 所有前端需求_

- [ ] 12.3 整合測試
  - 檔案：tests/integration/ 目錄下的測試檔案
  - 撰寫前後端整合和 API 測試
  - 測試完整的使用者流程
  - 目的：確保系統整體功能正常
  - _Requirements: 所有需求_

- [ ] 13. 部署和配置
  - 準備生產環境部署
  - 目的：讓系統可以在生產環境運行
  - _Requirements: 架構需求_

- [ ] 13.1 環境配置
  - 檔案：.env.production, docker-compose.prod.yml
  - 設定生產環境的環境變數和配置
  - 包含 Redis、Azure AD、API 金鑰等配置
  - 目的：準備生產環境設定
  - _Requirements: 架構需求_

- [ ] 13.2 監控和日誌
  - 檔案：backend/app/utils/logger.py, backend/app/middleware/logging.py
  - 實作應用程式監控和日誌記錄
  - 包含效能監控和錯誤追蹤
  - 目的：提供生產環境監控能力
  - _Requirements: 架構需求_

- [ ] 13.3 健康檢查端點
  - 檔案：backend/app/controllers/health_controller.py
  - 實作系統健康檢查 API
  - 檢查資料庫連線、外部 API 狀態等
  - 目的：提供系統健康狀態監控
  - _Requirements: 架構需求_