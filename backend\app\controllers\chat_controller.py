"""
聊天控制器
實作聊天相關的 HTTP API 端點，包含 /api/chat/message, /api/chat/history 等端點
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field, validator

from ..models.message import MessageCreateRequest, MessageResponse, MessageListResponse
from ..models.model import ModelType
from ..models.auth import Employee
from ..services.chat_service import get_chat_service, ChatServiceError
from ..middleware.auth import get_current_employee
from ..utils.response import APIResponse, create_error_response, create_success_response

# 設定日誌
logger = logging.getLogger(__name__)

# 建立路由器
router = APIRouter(prefix="/api/chat", tags=["chat"])

class ChatMessageRequest(BaseModel):
    """聊天訊息請求模型"""
    
    content: str = Field(..., min_length=1, max_length=10000, description="訊息內容")
    session_id: Optional[str] = Field(default=None, description="會話 ID")
    preferred_model: Optional[ModelType] = Field(default=None, description="偏好的 AI 模型")
    stream: bool = Field(default=False, description="是否串流回應")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="附加資料")
    
    @validator('content')
    def validate_content(cls, v):
        """驗證訊息內容"""
        if not v or not v.strip():
            raise ValueError("訊息內容不能為空")
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "content": "請幫我分析這個業務流程的優化方案",
                "session_id": "sess_123456",
                "preferred_model": "chatgpt",
                "stream": False,
                "metadata": {
                    "source": "web_interface",
                    "priority": "normal"
                }
            }
        }

class ChatMessageResponse(BaseModel):
    """聊天訊息回應模型"""
    
    request_id: str = Field(..., description="請求 ID")
    user_message: MessageResponse = Field(..., description="用戶訊息")
    assistant_message: MessageResponse = Field(..., description="助手回應")
    model_used: str = Field(..., description="使用的模型")
    processing_time_ms: float = Field(..., description="處理時間（毫秒）")
    ai_response_time_ms: float = Field(default=0.0, description="AI 回應時間（毫秒）")
    token_usage: Dict[str, int] = Field(default={}, description="Token 使用統計")
    cost: float = Field(default=0.0, description="成本")
    rejected: bool = Field(default=False, description="是否被拒絕")
    filter_result: Optional[Dict[str, Any]] = Field(default=None, description="過濾結果")
    timestamp: str = Field(..., description="時間戳")
    
    class Config:
        schema_extra = {
            "example": {
                "request_id": "req_123456",
                "user_message": {
                    "message_id": "msg_user_123",
                    "content": "請幫我分析業務流程",
                    "role": "user",
                    "timestamp": "2024-01-01T10:00:00Z"
                },
                "assistant_message": {
                    "message_id": "msg_assistant_123",
                    "content": "我很樂意協助您分析業務流程...",
                    "role": "assistant",
                    "timestamp": "2024-01-01T10:00:05Z"
                },
                "model_used": "chatgpt",
                "processing_time_ms": 3500.0,
                "ai_response_time_ms": 2800.0,
                "token_usage": {
                    "prompt_tokens": 150,
                    "completion_tokens": 200,
                    "total_tokens": 350
                },
                "cost": 0.0105,
                "rejected": False,
                "timestamp": "2024-01-01T10:00:05Z"
            }
        }

class ChatHistoryRequest(BaseModel):
    """聊天歷史請求模型"""
    
    session_id: str = Field(..., description="會話 ID")
    limit: int = Field(default=50, ge=1, le=100, description="限制數量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    
    class Config:
        schema_extra = {
            "example": {
                "session_id": "sess_123456",
                "limit": 20,
                "offset": 0
            }
        }

class SessionInfoResponse(BaseModel):
    """會話資訊回應模型"""
    
    session_id: str = Field(..., description="會話 ID")
    user_id: str = Field(..., description="用戶 ID")
    employee_id: str = Field(..., description="員工 ID")
    status: str = Field(..., description="會話狀態")
    created_at: str = Field(..., description="建立時間")
    last_activity: str = Field(..., description="最後活動時間")
    message_count: int = Field(..., description="訊息數量")
    preferred_model: Optional[str] = Field(default=None, description="偏好模型")
    remaining_seconds: int = Field(..., description="剩餘秒數")
    tags: List[str] = Field(default=[], description="標籤")
    metadata: Dict[str, Any] = Field(default={}, description="附加資料")

class ChatStatsResponse(BaseModel):
    """聊天統計回應模型"""
    
    total_messages: int = Field(..., description="總訊息數")
    successful_messages: int = Field(..., description="成功訊息數")
    failed_messages: int = Field(..., description="失敗訊息數")
    filtered_messages: int = Field(..., description="被過濾訊息數")
    success_rate: float = Field(..., description="成功率")
    filter_rate: float = Field(..., description="過濾率")
    model_stats: Dict[str, Any] = Field(..., description="模型統計")
    filter_stats: Dict[str, Any] = Field(default={}, description="過濾器統計")

@router.post("/message", response_model=APIResponse[ChatMessageResponse])
async def send_message(
    request: ChatMessageRequest,
    background_tasks: BackgroundTasks,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[ChatMessageResponse]:
    """
    發送聊天訊息
    
    - **content**: 訊息內容（必填）
    - **session_id**: 會話 ID（可選，會自動建立）
    - **preferred_model**: 偏好的 AI 模型（可選）
    - **stream**: 是否串流回應（目前不支援）
    - **metadata**: 附加資料（可選）
    """
    try:
        chat_service = get_chat_service()
        
        # 檢查串流請求（暫時不支援）
        if request.stream:
            raise HTTPException(
                status_code=400,
                detail="串流回應功能暫未實作，請使用非串流模式"
            )
        
        # 發送訊息
        result = await chat_service.send_message(
            user_id=current_employee.user_id,
            employee_id=current_employee.employee_id,
            content=request.content,
            session_id=request.session_id,
            preferred_model=request.preferred_model,
            stream=request.stream,
            metadata={
                **request.metadata,
                "employee_info": {
                    "display_name": current_employee.display_name,
                    "email": current_employee.email,
                    "department": current_employee.department
                },
                "request_timestamp": datetime.utcnow().isoformat()
            }
        )
        
        # 轉換回應格式
        response_data = ChatMessageResponse(**result)
        
        # 記錄使用統計（背景任務）
        background_tasks.add_task(
            _log_message_usage,
            current_employee.employee_id,
            result.get("model_used", "unknown"),
            result.get("token_usage", {}),
            result.get("cost", 0.0)
        )
        
        return create_success_response(
            data=response_data,
            message="訊息發送成功"
        )
        
    except ChatServiceError as e:
        logger.warning(f"聊天服務錯誤: {e}")
        
        # 根據錯誤類型返回適當的 HTTP 狀態碼
        status_code = 400
        if e.error_code in ["session_access_denied", "invalid_user"]:
            status_code = 403
        elif e.error_code in ["model_unavailable", "service_start_failed"]:
            status_code = 503
        
        raise HTTPException(status_code=status_code, detail=str(e))
        
    except Exception as e:
        logger.error(f"發送訊息失敗: {e}")
        raise HTTPException(status_code=500, detail="內部伺服器錯誤")

@router.get("/history", response_model=APIResponse[MessageListResponse])
async def get_chat_history(
    session_id: str,
    limit: int = 50,
    offset: int = 0,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[MessageListResponse]:
    """
    獲取聊天歷史
    
    - **session_id**: 會話 ID（必填）
    - **limit**: 限制數量（預設 50，最大 100）
    - **offset**: 偏移量（預設 0）
    """
    try:
        # 驗證參數
        if limit > 100:
            limit = 100
        if offset < 0:
            offset = 0
        
        chat_service = get_chat_service()
        
        # 獲取聊天歷史
        history = await chat_service.get_chat_history(
            user_id=current_employee.user_id,
            session_id=session_id,
            limit=limit,
            offset=offset
        )
        
        # 轉換為回應格式
        response_data = MessageListResponse(
            messages=history["messages"],
            total=history["total"],
            session_id=history["session_id"],
            has_more=history["has_more"],
            next_offset=offset + limit if history["has_more"] else None
        )
        
        return create_success_response(
            data=response_data,
            message="聊天歷史獲取成功"
        )
        
    except ChatServiceError as e:
        logger.warning(f"獲取聊天歷史錯誤: {e}")
        
        status_code = 400
        if e.error_code == "session_access_denied":
            status_code = 403
        
        raise HTTPException(status_code=status_code, detail=str(e))
        
    except Exception as e:
        logger.error(f"獲取聊天歷史失敗: {e}")
        raise HTTPException(status_code=500, detail="內部伺服器錯誤")

@router.get("/session/{session_id}", response_model=APIResponse[SessionInfoResponse])
async def get_session_info(
    session_id: str,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[SessionInfoResponse]:
    """
    獲取會話資訊
    
    - **session_id**: 會話 ID（必填）
    """
    try:
        chat_service = get_chat_service()
        
        # 獲取會話資訊
        session_info = await chat_service.get_session_info(
            user_id=current_employee.user_id,
            session_id=session_id
        )
        
        response_data = SessionInfoResponse(**session_info)
        
        return create_success_response(
            data=response_data,
            message="會話資訊獲取成功"
        )
        
    except ChatServiceError as e:
        logger.warning(f"獲取會話資訊錯誤: {e}")
        
        status_code = 400
        if e.error_code == "session_access_denied":
            status_code = 403
        
        raise HTTPException(status_code=status_code, detail=str(e))
        
    except Exception as e:
        logger.error(f"獲取會話資訊失敗: {e}")
        raise HTTPException(status_code=500, detail="內部伺服器錯誤")

@router.get("/stats", response_model=APIResponse[ChatStatsResponse])
async def get_chat_stats(
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[ChatStatsResponse]:
    """
    獲取聊天統計資訊
    
    需要管理員權限才能存取系統統計資訊
    """
    try:
        # 檢查權限（僅管理員可存取）
        if not _is_admin(current_employee):
            raise HTTPException(status_code=403, detail="權限不足，需要管理員權限")
        
        chat_service = get_chat_service()
        
        # 獲取統計資訊
        stats = chat_service.get_service_stats()
        
        response_data = ChatStatsResponse(**stats)
        
        return create_success_response(
            data=response_data,
            message="統計資訊獲取成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取統計資訊失敗: {e}")
        raise HTTPException(status_code=500, detail="內部伺服器錯誤")

@router.post("/stream")
async def send_message_stream(
    request: ChatMessageRequest,
    current_employee: Employee = Depends(get_current_employee)
) -> StreamingResponse:
    """
    發送聊天訊息（串流模式）
    
    返回 Server-Sent Events (SSE) 格式的串流回應
    """
    try:
        chat_service = get_chat_service()
        
        async def generate_stream():
            """生成串流回應"""
            try:
                # 設定串流模式
                request.stream = True
                
                async for chunk in chat_service.send_message(
                    user_id=current_employee.user_id,
                    employee_id=current_employee.employee_id,
                    content=request.content,
                    session_id=request.session_id,
                    preferred_model=request.preferred_model,
                    stream=True,
                    metadata={
                        **request.metadata,
                        "employee_info": {
                            "display_name": current_employee.display_name,
                            "email": current_employee.email
                        }
                    }
                ):
                    # 轉換為 SSE 格式
                    yield f"data: {chunk}\n\n"
                
                # 發送結束標記
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"串流回應錯誤: {e}")
                error_data = {
                    "error": str(e),
                    "type": "stream_error"
                }
                yield f"data: {error_data}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"初始化串流失敗: {e}")
        raise HTTPException(status_code=500, detail="串流初始化失敗")

@router.delete("/session/{session_id}")
async def delete_session(
    session_id: str,
    current_employee: Employee = Depends(get_current_employee)
) -> APIResponse[Dict[str, str]]:
    """
    刪除會話
    
    - **session_id**: 會話 ID（必填）
    """
    try:
        # 這裡可以添加會話刪除邏輯
        # 暫時返回成功回應
        return create_success_response(
            data={"session_id": session_id, "status": "deleted"},
            message="會話刪除成功"
        )
        
    except Exception as e:
        logger.error(f"刪除會話失敗: {e}")
        raise HTTPException(status_code=500, detail="刪除會話失敗")

async def _log_message_usage(
    employee_id: str,
    model_used: str,
    token_usage: Dict[str, int],
    cost: float
):
    """記錄訊息使用統計（背景任務）"""
    try:
        # 這裡可以添加使用統計記錄邏輯
        # 例如記錄到資料庫或發送到分析服務
        logger.info(
            f"使用統計 - 員工: {employee_id}, 模型: {model_used}, "
            f"Tokens: {token_usage.get('total_tokens', 0)}, 成本: ${cost:.4f}"
        )
    except Exception as e:
        logger.error(f"記錄使用統計失敗: {e}")

def _is_admin(employee: Employee) -> bool:
    """檢查是否為管理員"""
    # 這裡可以實作更複雜的權限檢查邏輯
    # 例如檢查 Azure AD 群組或特定角色
    admin_emails = [
        email.strip().lower() 
        for email in os.getenv("ADMIN_EMAILS", "").split(",")
        if email.strip()
    ]
    
    return employee.email.lower() in admin_emails

# 健康檢查端點
@router.get("/health")
async def health_check() -> APIResponse[Dict[str, Any]]:
    """
    聊天服務健康檢查
    """
    try:
        chat_service = get_chat_service()
        
        # 檢查各項服務狀態
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "chat_service": "ok",
                "redis": "ok",  # 可以添加實際的 Redis 連接檢查
                "ai_models": "ok"  # 可以添加實際的 AI 模型健康檢查
            }
        }
        
        return create_success_response(
            data=health_status,
            message="聊天服務運行正常"
        )
        
    except Exception as e:
        logger.error(f"健康檢查失敗: {e}")
        
        health_status = {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }
        
        return create_error_response(
            error="health_check_failed",
            message="聊天服務健康檢查失敗",
            data=health_status
        )