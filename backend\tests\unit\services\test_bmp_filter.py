"""
BPM 過濾服務單元測試
測試 BMP 相關性檢測演算法和關鍵字分析功能
"""

import pytest
from unittest.mock import Mock, patch, MagicMock

from backend.app.services.bpm_filter import BMPFilter, BMPFilterError
from backend.app.config.bpm_keywords import BMP_KEYWORDS, BMP_CATEGORIES


class TestBMPFilter:
    """BPM 過濾器測試類"""

    @pytest.fixture
    def bmp_filter(self):
        """創建 BMP 過濾器實例"""
        return BMPFilter()

    @pytest.fixture
    def bmp_related_messages(self):
        """BPM 相關訊息範例"""
        return [
            "如何優化業務流程？",
            "請問流程管理的最佳實務是什麼？",
            "組織架構重組應該注意哪些事項？",
            "流程自動化的實施步驟",
            "業務流程績效指標有哪些？",
            "如何進行流程文件化？",
            "流程改善的方法論",
            "BPM 工具的選擇標準",
            "流程監控和控制策略",
            "業務流程重新設計的原則"
        ]

    @pytest.fixture
    def non_bmp_messages(self):
        """非 BPM 相關訊息範例"""
        return [
            "今天天氣真好",
            "推薦一些好吃的餐廳",
            "如何學習 Python 程式語言？",
            "最新的電影有哪些？",
            "股票市場分析",
            "健身減重方法",
            "旅遊景點推薦",
            "遊戲攻略指南",
            "時尚穿搭建議",
            "料理食譜分享"
        ]

    def test_is_bmp_related_positive_cases(self, bmp_filter, bmp_related_messages):
        """測試 BPM 相關訊息的正確識別"""
        for message in bmp_related_messages:
            result = bmp_filter.is_bmp_related(message)
            assert result is True, f"訊息應被識別為 BPM 相關: {message}"

    def test_is_bmp_related_negative_cases(self, bmp_filter, non_bmp_messages):
        """測試非 BPM 相關訊息的正確識別"""
        for message in non_bmp_messages:
            result = bmp_filter.is_bmp_related(message)
            assert result is False, f"訊息應被識別為非 BPM 相關: {message}"

    def test_is_bmp_related_empty_message(self, bmp_filter):
        """測試空訊息的處理"""
        assert bmp_filter.is_bmp_related("") is False
        assert bmp_filter.is_bmp_related("   ") is False
        assert bmp_filter.is_bmp_related(None) is False

    def test_get_relevance_score_high_relevance(self, bmp_filter):
        """測試高相關性訊息的分數計算"""
        high_relevance_message = "業務流程管理和組織架構優化"
        score = bmp_filter.get_relevance_score(high_relevance_message)
        
        assert 0.0 <= score <= 1.0
        assert score >= 0.8  # 高相關性應該有高分數

    def test_get_relevance_score_low_relevance(self, bmp_filter):
        """測試低相關性訊息的分數計算"""
        low_relevance_message = "今天天氣真好，適合出門散步"
        score = bmp_filter.get_relevance_score(low_relevance_message)
        
        assert 0.0 <= score <= 1.0
        assert score <= 0.3  # 低相關性應該有低分數

    def test_get_relevance_score_medium_relevance(self, bmp_filter):
        """測試中等相關性訊息的分數計算"""
        medium_relevance_message = "公司管理制度需要改進"
        score = bmp_filter.get_relevance_score(medium_relevance_message)
        
        assert 0.0 <= score <= 1.0
        assert 0.3 < score < 0.8  # 中等相關性應該有中等分數

    def test_extract_keywords(self, bmp_filter):
        """測試關鍵字提取功能"""
        message = "請問業務流程優化的最佳實務和組織管理策略"
        keywords = bmp_filter.extract_keywords(message)
        
        assert isinstance(keywords, list)
        assert len(keywords) > 0
        
        # 應該包含 BPM 相關關鍵字
        bmp_keywords_found = any(
            keyword in BMP_KEYWORDS.get("process", []) or
            keyword in BMP_KEYWORDS.get("management", []) or
            keyword in BMP_KEYWORDS.get("organization", [])
            for keyword in keywords
        )
        assert bmp_keywords_found

    def test_suggest_keywords(self, bmp_filter):
        """測試關鍵字建議功能"""
        partial_message = "流程"
        suggestions = bmp_filter.suggest_keywords(partial_message)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        
        # 建議應該包含相關的 BPM 關鍵字
        assert any("流程" in suggestion for suggestion in suggestions)

    def test_categorize_message(self, bmp_filter):
        """測試訊息分類功能"""
        test_cases = [
            ("如何優化業務流程？", "process"),
            ("組織架構設計原則", "organization"),
            ("流程管理工具推薦", "management"),
            ("業務流程自動化實施", "automation"),
            ("流程績效監控方法", "monitoring")
        ]
        
        for message, expected_category in test_cases:
            category = bmp_filter.categorize_message(message)
            assert category in BMP_CATEGORIES
            # 可以根據具體實作調整這個斷言

    def test_analyze_message_comprehensive(self, bmp_filter):
        """測試綜合訊息分析功能"""
        message = "請問如何進行業務流程重新設計和組織架構優化？"
        
        analysis = bmp_filter.analyze_message(message)
        
        assert isinstance(analysis, dict)
        assert "is_bmp_related" in analysis
        assert "relevance_score" in analysis
        assert "keywords" in analysis
        assert "category" in analysis
        assert "suggestions" in analysis
        
        # 驗證分析結果
        assert analysis["is_bmp_related"] is True
        assert analysis["relevance_score"] > 0.7
        assert len(analysis["keywords"]) > 0

    def test_filter_with_threshold(self, bmp_filter):
        """測試使用閾值的過濾功能"""
        # 設定不同的閾值
        high_threshold = 0.8
        low_threshold = 0.3
        
        test_message = "流程管理優化"
        
        # 高閾值可能過濾掉邊界情況
        result_high = bmp_filter.is_bmp_related(test_message, threshold=high_threshold)
        result_low = bmp_filter.is_bmp_related(test_message, threshold=low_threshold)
        
        # 低閾值應該更容易通過
        assert isinstance(result_high, bool)
        assert isinstance(result_low, bool)

    def test_batch_filter_messages(self, bmp_filter, bmp_related_messages, non_bmp_messages):
        """測試批量過濾訊息"""
        all_messages = bmp_related_messages + non_bmp_messages
        
        results = bmp_filter.batch_filter(all_messages)
        
        assert isinstance(results, list)
        assert len(results) == len(all_messages)
        
        # 驗證結果格式
        for result in results:
            assert isinstance(result, dict)
            assert "message" in result
            assert "is_bmp_related" in result
            assert "relevance_score" in result

    def test_update_keywords_dynamic(self, bmp_filter):
        """測試動態更新關鍵字庫"""
        new_keywords = ["新流程", "數位轉型", "敏捷管理"]
        
        original_count = len(bmp_filter.get_all_keywords())
        bmp_filter.add_keywords("custom", new_keywords)
        updated_count = len(bmp_filter.get_all_keywords())
        
        assert updated_count > original_count
        
        # 測試新關鍵字是否生效
        test_message = "數位轉型的新流程設計"
        assert bmp_filter.is_bmp_related(test_message) is True

    def test_language_detection(self, bmp_filter):
        """測試多語言支援"""
        test_cases = [
            ("如何優化業務流程？", "zh"),  # 中文
            ("How to optimize business processes?", "en"),  # 英文
            ("ビジネスプロセスの最適化", "ja"),  # 日文（如果支援）
        ]
        
        for message, expected_lang in test_cases:
            detected_lang = bmp_filter.detect_language(message)
            if bmp_filter.supports_language(expected_lang):
                assert detected_lang == expected_lang

    def test_semantic_similarity(self, bmp_filter):
        """測試語義相似度計算"""
        reference_message = "業務流程優化"
        similar_messages = [
            "流程改善方法",
            "業務程序優化",
            "工作流程改進"
        ]
        
        for similar_msg in similar_messages:
            similarity = bmp_filter.calculate_semantic_similarity(
                reference_message, similar_msg
            )
            assert 0.0 <= similarity <= 1.0
            assert similarity > 0.5  # 相似訊息應該有較高相似度

    def test_context_aware_filtering(self, bmp_filter):
        """測試上下文感知過濾"""
        conversation_context = [
            "我們需要討論流程改善",
            "有什麼好的建議嗎？",
            "預算方面需要考慮什麼？"  # 在 BPM 上下文中，預算也相關
        ]
        
        # 最後一個訊息在有上下文的情況下應該被認為相關
        result_with_context = bmp_filter.is_bmp_related_with_context(
            conversation_context[-1], 
            conversation_context[:-1]
        )
        
        result_without_context = bmp_filter.is_bmp_related(conversation_context[-1])
        
        # 有上下文的情況下可能得到不同結果
        assert isinstance(result_with_context, bool)
        assert isinstance(result_without_context, bool)

    def test_performance_large_text(self, bmp_filter):
        """測試處理大文本的性能"""
        # 創建一個很長的文本
        long_text = """
        業務流程管理是現代企業運營中極為重要的管理方法和理念。
        它涉及到組織架構設計、流程優化、績效監控、持續改善等多個方面。
        """ * 1000  # 重複 1000 次創建大文本
        
        import time
        start_time = time.time()
        
        result = bmp_filter.is_bmp_related(long_text)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 應該在合理時間內完成處理
        assert processing_time < 5.0  # 5秒內完成
        assert isinstance(result, bool)

    def test_edge_cases(self, bmp_filter):
        """測試邊界情況"""
        edge_cases = [
            "",  # 空字串
            " ",  # 空格
            "a" * 10000,  # 超長重複字符
            "123456789",  # 純數字
            "!@#$%^&*()",  # 純符號
            "流程管理" * 100,  # 重複關鍵字
        ]
        
        for case in edge_cases:
            try:
                result = bmp_filter.is_bmp_related(case)
                assert isinstance(result, bool)
            except BMPFilterError:
                # 某些邊界情況可能拋出異常，這是可接受的
                pass

    def test_configuration_loading(self, bmp_filter):
        """測試配置載入功能"""
        # 測試載入自定義配置
        custom_config = {
            "threshold": 0.7,
            "max_keywords": 50,
            "enable_semantic": True,
            "language": "zh-TW"
        }
        
        bmp_filter.update_config(custom_config)
        
        config = bmp_filter.get_config()
        assert config["threshold"] == 0.7
        assert config["enable_semantic"] is True

    @pytest.mark.parametrize("message,expected", [
        ("流程管理系統", True),
        ("組織架構優化", True),
        ("業務流程分析", True),
        ("美食推薦", False),
        ("電影評論", False),
        ("程式開發", False),
    ])
    def test_parameterized_filtering(self, bmp_filter, message, expected):
        """使用參數化測試多個訊息"""
        result = bmp_filter.is_bmp_related(message)
        assert result == expected, f"訊息 '{message}' 的過濾結果不符預期"


class TestBMPFilterError:
    """BMP 過濾器錯誤測試類"""

    def test_bmp_filter_error_creation(self):
        """測試創建 BMP 過濾器錯誤物件"""
        error = BMPFilterError("過濾器錯誤", "FILTER_ERROR")
        
        assert str(error) == "過濾器錯誤"
        assert error.error_code == "FILTER_ERROR"

    def test_error_handling_invalid_input(self):
        """測試無效輸入的錯誤處理"""
        bmp_filter = BMPFilter()
        
        with pytest.raises(BMPFilterError):
            bmp_filter.is_bmp_related(None)

    def test_error_handling_configuration(self):
        """測試配置錯誤的處理"""
        bmp_filter = BMPFilter()
        
        with pytest.raises(BMPFilterError):
            # 嘗試設定無效配置
            bmp_filter.update_config({"threshold": 1.5})  # 超出有效範圍


class TestBMPKeywords:
    """BMP 關鍵字配置測試類"""

    def test_keywords_structure(self):
        """測試關鍵字結構的完整性"""
        assert isinstance(BMP_KEYWORDS, dict)
        assert len(BMP_KEYWORDS) > 0
        
        # 檢查每個類別都有關鍵字
        for category, keywords in BMP_KEYWORDS.items():
            assert isinstance(keywords, list)
            assert len(keywords) > 0
            assert all(isinstance(keyword, str) for keyword in keywords)

    def test_categories_completeness(self):
        """測試類別的完整性"""
        required_categories = ["process", "management", "organization", "automation"]
        
        for category in required_categories:
            assert category in BMP_KEYWORDS, f"缺少必要類別: {category}"

    def test_keywords_uniqueness(self):
        """測試關鍵字的唯一性"""
        all_keywords = []
        for keywords in BMP_KEYWORDS.values():
            all_keywords.extend(keywords)
        
        # 檢查是否有重複關鍵字
        unique_keywords = set(all_keywords)
        duplicates = len(all_keywords) - len(unique_keywords)
        
        # 允許少量重複，但不應過多
        assert duplicates < len(all_keywords) * 0.1, "關鍵字重複過多"