"""
健康檢查 API 端點
提供系統健康狀態和監控資訊
"""

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
import time
import psutil
import asyncio
from datetime import datetime

from app.config.settings import get_settings, Settings
from app.services.redis_client import get_redis_client

router = APIRouter(prefix="/health", tags=["health"])


class HealthStatus(BaseModel):
    """健康狀態模型"""
    status: str
    timestamp: datetime
    uptime: float
    version: str
    environment: str


class DetailedHealthStatus(BaseModel):
    """詳細健康狀態模型"""
    status: str
    timestamp: datetime
    uptime: float
    version: str
    environment: str
    services: Dict[str, Any]
    system: Dict[str, Any]
    dependencies: Dict[str, Any]


# 應用程式啟動時間
APP_START_TIME = time.time()


@router.get("/", response_model=HealthStatus)
async def health_check(settings: Settings = Depends(get_settings)):
    """
    基本健康檢查端點
    返回應用程式的基本健康狀態
    """
    return HealthStatus(
        status="healthy",
        timestamp=datetime.now(),
        uptime=time.time() - APP_START_TIME,
        version=settings.app_version,
        environment=settings.app_environment
    )


@router.get("/detailed", response_model=DetailedHealthStatus)
async def detailed_health_check(settings: Settings = Depends(get_settings)):
    """
    詳細健康檢查端點
    返回詳細的系統健康狀態和監控資訊
    """
    # 檢查各項服務狀態
    services_status = await check_services_health(settings)
    system_info = get_system_info()
    dependencies_status = await check_dependencies_health(settings)
    
    # 判斷整體狀態
    overall_status = "healthy"
    for service_status in services_status.values():
        if service_status.get("status") != "healthy":
            overall_status = "unhealthy"
            break
    
    for dep_status in dependencies_status.values():
        if dep_status.get("status") != "healthy":
            overall_status = "unhealthy"
            break
    
    return DetailedHealthStatus(
        status=overall_status,
        timestamp=datetime.now(),
        uptime=time.time() - APP_START_TIME,
        version=settings.app_version,
        environment=settings.app_environment,
        services=services_status,
        system=system_info,
        dependencies=dependencies_status
    )


@router.get("/ready")
async def readiness_check(settings: Settings = Depends(get_settings)):
    """
    準備就緒檢查端點
    檢查應用程式是否準備好接收請求
    """
    try:
        # 檢查關鍵服務
        redis_client = await get_redis_client()
        await redis_client.ping()
        
        return {
            "status": "ready",
            "timestamp": datetime.now(),
            "message": "應用程式已準備就緒"
        }
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"應用程式尚未準備就緒: {str(e)}"
        )


@router.get("/live")
async def liveness_check():
    """
    存活檢查端點
    檢查應用程式是否仍在運行
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(),
        "uptime": time.time() - APP_START_TIME
    }


async def check_services_health(settings: Settings) -> Dict[str, Any]:
    """檢查各項服務的健康狀態"""
    services = {}
    
    # 檢查 Redis
    try:
        redis_client = await get_redis_client()
        start_time = time.time()
        await redis_client.ping()
        response_time = time.time() - start_time
        
        services["redis"] = {
            "status": "healthy",
            "response_time": round(response_time * 1000, 2),  # 毫秒
            "url": settings.redis_url.split('@')[-1] if '@' in settings.redis_url else settings.redis_url
        }
    except Exception as e:
        services["redis"] = {
            "status": "unhealthy",
            "error": str(e),
            "url": settings.redis_url.split('@')[-1] if '@' in settings.redis_url else settings.redis_url
        }
    
    # 檢查 AI 服務
    services["ai_models"] = await check_ai_models_health(settings)
    
    return services


async def check_ai_models_health(settings: Settings) -> Dict[str, Any]:
    """檢查 AI 模型服務的健康狀態"""
    models_status = {}
    
    # 檢查 OpenAI
    if settings.openai_api_key:
        try:
            # 這裡可以加入實際的 OpenAI API 健康檢查
            models_status["openai"] = {
                "status": "configured",
                "model": settings.openai_model
            }
        except Exception as e:
            models_status["openai"] = {
                "status": "error",
                "error": str(e)
            }
    else:
        models_status["openai"] = {
            "status": "not_configured",
            "message": "API 金鑰未設定"
        }
    
    # 檢查 Anthropic
    if settings.anthropic_api_key:
        models_status["anthropic"] = {
            "status": "configured",
            "model": settings.anthropic_model
        }
    else:
        models_status["anthropic"] = {
            "status": "not_configured",
            "message": "API 金鑰未設定"
        }
    
    # 檢查 Google
    if settings.google_api_key:
        models_status["google"] = {
            "status": "configured",
            "model": settings.google_model
        }
    else:
        models_status["google"] = {
            "status": "not_configured",
            "message": "API 金鑰未設定"
        }
    
    return models_status


def get_system_info() -> Dict[str, Any]:
    """獲取系統資訊"""
    try:
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_usage": psutil.cpu_percent(interval=1),
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            "boot_time": psutil.boot_time()
        }
    except Exception as e:
        return {
            "error": f"無法獲取系統資訊: {str(e)}"
        }


async def check_dependencies_health(settings: Settings) -> Dict[str, Any]:
    """檢查外部依賴的健康狀態"""
    dependencies = {}
    
    # 檢查網路連線
    try:
        import aiohttp
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            # 檢查基本網路連線
            async with session.get('https://httpbin.org/status/200') as response:
                if response.status == 200:
                    dependencies["internet"] = {
                        "status": "healthy",
                        "message": "網路連線正常"
                    }
                else:
                    dependencies["internet"] = {
                        "status": "unhealthy",
                        "message": f"網路回應異常: {response.status}"
                    }
    except Exception as e:
        dependencies["internet"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # 檢查 DNS 解析
    try:
        import socket
        socket.gethostbyname('google.com')
        dependencies["dns"] = {
            "status": "healthy",
            "message": "DNS 解析正常"
        }
    except Exception as e:
        dependencies["dns"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    return dependencies


@router.get("/metrics")
async def get_metrics(settings: Settings = Depends(get_settings)):
    """
    獲取應用程式監控指標
    """
    if not settings.metrics_enabled:
        raise HTTPException(
            status_code=404,
            detail="監控指標功能未啟用"
        )
    
    try:
        system_info = get_system_info()
        uptime = time.time() - APP_START_TIME
        
        # 這裡可以添加更多的業務指標
        metrics = {
            "system": system_info,
            "application": {
                "uptime_seconds": uptime,
                "version": settings.app_version,
                "environment": settings.app_environment,
                "python_version": f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}.{psutil.sys.version_info.micro}"
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return metrics
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"無法獲取監控指標: {str(e)}"
        )