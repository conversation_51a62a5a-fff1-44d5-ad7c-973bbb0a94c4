{"version": 3, "file": "SilentIframeClient.mjs", "sources": ["../../src/interaction_client/SilentIframeClient.ts"], "sourcesContent": [null], "names": ["BrowserUtils.preconnect", "BrowserAuthErrorCodes.silentLogoutUnsupported", "ResponseHandler.deserializeResponse", "BrowserAuthErrorCodes.nativeConnectionNotEstablished"], "mappings": ";;;;;;;;;;;;;;AAAA;;;AAGG;AA0CG,MAAO,kBAAmB,SAAQ,yBAAyB,CAAA;IAI7D,WACI,CAAA,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,KAAY,EACZ,iBAAqC,EACrC,iBAAsC,EACtC,oBAA2C,EAC3C,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,oBAAoB,EACpB,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;KAC1C;AAED;;;AAGG;IACH,MAAM,YAAY,CACd,OAAyB,EAAA;AAEzB,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,8BAA8B,EAChD,OAAO,CAAC,aAAa,CACxB,CAAC;;QAEF,IACI,CAAC,OAAO,CAAC,SAAS;YAClB,CAAC,OAAO,CAAC,GAAG;AACZ,aAAC,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EACjD;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,qGAAqG,CACxG,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,YAAY,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QACpC,IAAI,YAAY,CAAC,MAAM,EAAE;AACrB,YAAA,IACI,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI;AACxC,gBAAA,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,UAAU,EAChD;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAgD,6CAAA,EAAA,YAAY,CAAC,MAAM,SAAS,WAAW,CAAC,IAAI,CAAA,CAAE,CACjG,CAAC;AACF,gBAAA,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC;AAC1C,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC;AAC1C,SAAA;;AAGD,QAAA,MAAM,aAAa,GAA4B,MAAM,WAAW,CAC5D,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;AACxC,QAAAA,UAAuB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEjD,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,IAAI,CAAC,KAAK,CACb,CAAC;AAEF,QAAA,IAAI,UAA+C,CAAC;QAEpD,IAAI;;AAEA,YAAA,UAAU,GAAG,MAAM,WAAW,CAC1B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;gBACE,sBAAsB;gBACtB,gBAAgB,EAAE,aAAa,CAAC,SAAS;gBACzC,wBAAwB,EAAE,aAAa,CAAC,iBAAiB;gBACzD,2BAA2B,EAAE,aAAa,CAAC,oBAAoB;gBAC/D,OAAO,EAAE,aAAa,CAAC,OAAO;AACjC,aAAA,CAAC,CAAC;AAEH,YAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,6BAA6B,EAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AAChC,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AAED,YAAA,IACI,CAAC,UAAU;AACX,gBAAA,EAAE,CAAC,YAAY,SAAS,CAAC;AACzB,gBAAA,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC,mBAAmB,EACtD;AACE,gBAAA,MAAM,CAAC,CAAC;AACX,aAAA;AAED,YAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC5B;gBACI,UAAU,EAAE,CAAC,CAAC,SAAS;AAC1B,aAAA,EACD,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,YAAA,MAAM,kBAAkB,GACpB,MAAM,WAAW,CACb,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;AAE5C,YAAA,OAAO,MAAM,WAAW,CACpB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,iBAAiB,CAAC,6BAA6B,EAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AACrC,SAAA;KACJ;AAED;;AAEG;IACH,MAAM,GAAA;;QAEF,OAAO,OAAO,CAAC,MAAM,CACjB,sBAAsB,CAClBC,uBAA6C,CAChD,CACJ,CAAC;KACL;AAED;;;;;AAKG;AACO,IAAA,MAAM,iBAAiB,CAC7B,UAAmC,EACnC,aAAsC,EAAA;AAEtC,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC;QAClD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,6BAA6B,EAC/C,aAAa,CAChB,CAAC;;AAGF,QAAA,MAAM,eAAe,GACjB,MAAM,WAAW,CACb,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD,iBAAiB,CAAC,2DAA2D,EAC7E,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,aAAa,CAAC,CAAC;;AAGrB,QAAA,MAAM,WAAW,GAAG,MAAM,WAAW,CACjC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAC1C,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;AACE,YAAA,GAAG,aAAa;YAChB,YAAY,EAAE,oBAAoB,CAAC,iBAAiB,CAChD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,oBAAoB,EACzB,aAAa,CAAC,oBAAoB,CACrC;AACJ,SAAA,CAAC,CAAC;;QAGH,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAC7C,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,eAAe,EACf,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAEF,QAAA,MAAM,SAAS,GAAG,MAAM,WAAW,CAC/B,mBAAmB,EACnB,iBAAiB,CAAC,gCAAgC,EAClD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,WAAW,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,aAAa,EACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CACvC,CAAC;QACF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;;QAErE,MAAM,cAAc,GAAG,MAAM,WAAW,CACpC,oBAAoB,EACpB,iBAAiB,CAAC,iCAAiC,EACnD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,SAAS,EACT,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,EACpC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,EAC3C,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,aAAa,EACb,YAAY,CACf,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,MAAM,CACvBC,mBAAmC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7C,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iDAAiD,CACpD,CAAC;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,MAAM,sBAAsB,CACxBC,8BAAoD,CACvD,CAAC;AACL,aAAA;YACD,MAAM,uBAAuB,GAAG,IAAI,uBAAuB,CACvD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,EACzB,YAAY,CAAC,SAAS,EACtB,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;AACF,YAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,aAAa,CAAC,iBAAiB,CACxD,IAAI,CAAC,aAAa,EAClB,aAAa,CAAC,KAAK,CACtB,CAAC;YACF,OAAO,WAAW,CACd,uBAAuB,CAAC,YAAY,CAAC,IAAI,CACrC,uBAAuB,CAC1B,EACD,iBAAiB,CAAC,mCAAmC,EACrD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;AACE,gBAAA,GAAG,aAAa;AAChB,gBAAA,KAAK,EAAE,gBAAgB;AACvB,gBAAA,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI;AACnD,aAAA,CAAC,CAAC;AACN,SAAA;;AAGD,QAAA,OAAO,WAAW,CACd,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAC9D,iBAAiB,CAAC,kBAAkB,EACpC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;KAClC;AACJ;;;;"}