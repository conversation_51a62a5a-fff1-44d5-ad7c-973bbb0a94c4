"""
認證 API 路由
處理用戶登入、登出和會話管理
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials

# 本地模組
from ..middleware.auth import get_current_user, get_optional_user, security, AuthenticatedUser
from ..services.employee_service import employee_service
from ..services.redis_service import redis_service
from ..models.auth import (
    AuthRequest, AuthResponse, AuthStatus,
    SessionRequest, SessionResponse,
    LogoutRequest, LogoutResponse,
    AuthErrorResponse
)

# 設定日誌
logger = logging.getLogger(__name__)

# 建立路由器
router = APIRouter(prefix="/auth", tags=["認證"])

@router.post("/login", response_model=AuthResponse)
async def login(
    request: AuthRequest,
    http_request: Request
):
    """
    用戶登入
    """
    try:
        # 驗證 Azure AD token
        from ..middleware.auth import token_validator
        token_payload = await token_validator.verify_token(request.token)
        
        # 建立認證用戶物件
        from ..middleware.auth import AuthenticatedUser
        user = AuthenticatedUser(token_payload)
        
        # 驗證員工身份
        employee = await employee_service.validate_employee(user.object_id)
        
        # 建立會話
        session_data = {
            "user_principal_name": user.user_principal_name,
            "display_name": user.name,
            "email": user.email,
            "tenant_id": user.tenant_id,
            "roles": user.roles,
            "groups": user.groups,
            "client_info": request.client_info or {},
            "employee_data": employee.to_dict() if hasattr(employee, 'to_dict') else {}
        }
        
        session_id = await redis_service.create_session(
            user.object_id,
            employee.object_id if hasattr(employee, 'object_id') else user.object_id,
            session_data,
            ttl_seconds=1800  # 30 分鐘
        )
        
        # 快取員工資料
        if hasattr(employee, 'to_dict'):
            await redis_service.cache_employee_data(
                employee.object_id,
                employee.to_dict()
            )
        
        # 檢查 BPM 權限
        bmp_permissions = await employee_service.check_bmp_permissions(employee)
        
        logger.info(f"用戶登入成功: {user.email}")
        
        return AuthResponse(
            status=AuthStatus.SUCCESS,
            message="登入成功",
            user_id=user.object_id,
            session_id=session_id,
            expires_at=(datetime.utcnow() + timedelta(seconds=1800)).isoformat(),
            permissions=list(bmp_permissions.keys())
        )
        
    except HTTPException as e:
        logger.warning(f"登入失敗: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"登入過程發生錯誤: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登入服務暫時無法使用"
        )

@router.post("/logout", response_model=LogoutResponse)
async def logout(
    request: LogoutRequest,
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    用戶登出
    """
    try:
        sessions_terminated = 0
        
        if request.all_sessions:
            # 登出所有會話
            sessions_terminated = await redis_service.delete_user_sessions(current_user.object_id)
        else:
            # 登出特定會話
            if request.session_id:
                success = await redis_service.delete_session(request.session_id)
                sessions_terminated = 1 if success else 0
            else:
                # 如果沒有指定會話 ID，登出所有會話
                sessions_terminated = await redis_service.delete_user_sessions(current_user.object_id)
        
        # 清除員工資料快取
        await redis_service.delete_cached_employee_data(current_user.object_id)
        
        logger.info(f"用戶登出: {current_user.email}, 終止會話數: {sessions_terminated}")
        
        return LogoutResponse(
            status="success",
            message=f"成功登出，終止了 {sessions_terminated} 個會話",
            sessions_terminated=sessions_terminated
        )
        
    except Exception as e:
        logger.error(f"登出失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出服務發生錯誤"
        )

@router.get("/session")
async def get_session_info(
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    獲取當前會話資訊
    """
    try:
        # 獲取用戶的會話列表
        session_ids = await redis_service.get_user_sessions(current_user.object_id)
        
        active_sessions = []
        for session_id in session_ids:
            session_info = await redis_service.get_session(session_id)
            if session_info:
                active_sessions.append({
                    "session_id": session_id,
                    "created_at": session_info["created_at"],
                    "expires_at": session_info["expires_at"],
                    "last_activity": session_info["last_activity"],
                    "is_active": session_info["is_active"]
                })
        
        return {
            "user_id": current_user.object_id,
            "email": current_user.email,
            "name": current_user.name,
            "active_sessions": active_sessions,
            "session_count": len(active_sessions)
        }
        
    except Exception as e:
        logger.error(f"獲取會話資訊失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="無法獲取會話資訊"
        )

@router.post("/refresh")
async def refresh_session(
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    重新整理會話
    """
    try:
        # 獲取用戶的活躍會話
        session_ids = await redis_service.get_user_sessions(current_user.object_id)
        
        refreshed_sessions = []
        for session_id in session_ids:
            success = await redis_service.update_session_activity(session_id)
            if success:
                refreshed_sessions.append(session_id)
        
        return {
            "message": "會話已重新整理",
            "refreshed_sessions": refreshed_sessions,
            "refresh_count": len(refreshed_sessions)
        }
        
    except Exception as e:
        logger.error(f"重新整理會話失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="會話重新整理失敗"
        )

@router.get("/status")
async def auth_status(
    user: Optional[AuthenticatedUser] = Depends(get_optional_user)
):
    """
    檢查認證狀態
    """
    if user:
        return {
            "authenticated": True,
            "user_id": user.object_id,
            "email": user.email,
            "name": user.name,
            "is_employee": user.is_employee
        }
    else:
        return {
            "authenticated": False
        }

@router.get("/permissions")
async def get_user_permissions(
    current_user: AuthenticatedUser = Depends(get_current_user)
):
    """
    獲取用戶權限
    """
    try:
        # 獲取員工資訊
        employee = await employee_service.get_employee_by_object_id(current_user.object_id)
        
        if not employee:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="找不到員工資訊"
            )
        
        # 獲取 BPM 權限
        bmp_permissions = await employee_service.check_bmp_permissions(employee)
        
        return {
            "user_id": current_user.object_id,
            "email": current_user.email,
            "department": employee.department,
            "job_title": employee.job_title,
            "groups": employee.groups,
            "roles": employee.roles,
            "permissions": employee.permissions,
            "bmp_permissions": bmp_permissions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取用戶權限失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="無法獲取用戶權限"
        )

@router.post("/validate-token")
async def validate_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    驗證 token 有效性
    """
    try:
        from ..middleware.auth import token_validator
        token_payload = await token_validator.verify_token(credentials.credentials)
        
        return {
            "valid": True,
            "token_payload": {
                "object_id": token_payload.get("oid"),
                "user_principal_name": token_payload.get("upn"),
                "name": token_payload.get("name"),
                "email": token_payload.get("email"),
                "expires_at": datetime.fromtimestamp(token_payload.get("exp", 0)).isoformat()
            }
        }
        
    except HTTPException as e:
        return {
            "valid": False,
            "error": e.detail
        }
    except Exception as e:
        logger.error(f"Token 驗證失敗: {e}")
        return {
            "valid": False,
            "error": "Token 驗證過程發生錯誤"
        }

# === 錯誤處理 ===

@router.exception_handler(HTTPException)
async def auth_exception_handler(request: Request, exc: HTTPException):
    """認證錯誤處理器"""
    
    logger.warning(f"認證錯誤: {exc.detail} - Path: {request.url.path}")
    
    return AuthErrorResponse(
        error_code=f"AUTH_{exc.status_code}",
        error_message=exc.detail,
        error_description="認證過程發生錯誤，請檢查登入狀態",
        timestamp=datetime.utcnow()
    )