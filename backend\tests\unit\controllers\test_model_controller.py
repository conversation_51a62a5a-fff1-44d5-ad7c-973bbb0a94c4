"""
模型控制器單元測試
測試 ModelController 的模型選擇和管理 API 端點功能
"""

import pytest
import json
from unittest.mock import AsyncMock, Mock, patch
from fastapi.testclient import TestClient
from datetime import datetime

from app.controllers.model_controller import ModelController, router
from app.services.model_router import ModelType, ModelHealth, ModelMetrics


class TestModelController:
    """模型控制器測試類"""

    @pytest.fixture
    def mock_model_router(self):
        """模擬模型路由器"""
        router_mock = AsyncMock()
        return router_mock

    @pytest.fixture
    def mock_auth_service(self):
        """模擬認證服務"""
        service = AsyncMock()
        service.verify_token.return_value = {
            "user_id": "test_user",
            "employee_id": "test_employee"
        }
        return service

    @pytest.fixture
    def model_controller(self, mock_model_router, mock_auth_service):
        """創建模型控制器實例"""
        with patch('app.controllers.model_controller.get_model_router', return_value=mock_model_router), \
             patch('app.controllers.model_controller.get_auth_service', return_value=mock_auth_service):
            return ModelController()

    @pytest.fixture
    def test_client(self, model_controller):
        """創建測試客戶端"""
        from fastapi import FastAPI
        app = FastAPI()
        app.include_router(router)
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """認證標頭"""
        return {"Authorization": "Bearer test_token"}

    @pytest.fixture
    def sample_models(self):
        """範例模型資料"""
        return [
            {
                "model_type": "chatgpt",
                "enabled": True,
                "status": "available",
                "health": {
                    "response_time_ms": 1200,
                    "success_rate": 0.98,
                    "concurrent_requests": 2,
                    "error_count": 1
                },
                "metrics": {
                    "requests_count": 150,
                    "success_count": 147,
                    "error_count": 3,
                    "average_response_time": 1250,
                    "success_rate": 0.98
                }
            },
            {
                "model_type": "claude",
                "enabled": True,
                "status": "available",
                "health": {
                    "response_time_ms": 1500,
                    "success_rate": 0.96,
                    "concurrent_requests": 1,
                    "error_count": 2
                },
                "metrics": {
                    "requests_count": 100,
                    "success_count": 96,
                    "error_count": 4,
                    "average_response_time": 1600,
                    "success_rate": 0.96
                }
            }
        ]

    def test_get_available_models_success(self, test_client, auth_headers, mock_model_router, sample_models):
        """測試成功獲取可用模型列表"""
        mock_model_router.get_available_models.return_value = sample_models
        
        response = test_client.get("/api/models/available", headers=auth_headers)
        
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["models"]) == 2
        assert response_data["models"][0]["model_type"] == "chatgpt"
        assert response_data["models"][1]["model_type"] == "claude"

    def test_get_available_models_unauthorized(self, test_client, mock_model_router):
        """測試未授權的模型列表請求"""
        response = test_client.get("/api/models/available")
        
        assert response.status_code == 401

    def test_get_available_models_include_unavailable(self, test_client, auth_headers, mock_model_router):
        """測試包含不可用模型的列表請求"""
        all_models = [
            {"model_type": "chatgpt", "enabled": True, "status": "available"},
            {"model_type": "claude", "enabled": True, "status": "available"},
            {"model_type": "gemini", "enabled": False, "status": "unavailable"}
        ]
        mock_model_router.get_all_models.return_value = all_models
        
        response = test_client.get(
            "/api/models/available",
            params={"include_unavailable": True},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["models"]) == 3

    def test_select_model_success(self, test_client, auth_headers, mock_model_router):
        """測試成功選擇模型"""
        selection_data = {
            "model_type": "chatgpt",
            "session_id": "test_session",
            "preferences": {
                "temperature": 0.7,
                "max_tokens": 1000
            }
        }
        
        mock_model_router.select_model.return_value = {
            "model_type": "chatgpt",
            "selected": True,
            "estimated_response_time": 1200
        }
        
        response = test_client.post(
            "/api/models/select",
            json=selection_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["model_type"] == "chatgpt"
        assert response_data["selected"] is True

    def test_select_model_invalid_type(self, test_client, auth_headers):
        """測試選擇無效的模型類型"""
        selection_data = {
            "model_type": "invalid_model",
            "session_id": "test_session"
        }
        
        response = test_client.post(
            "/api/models/select",
            json=selection_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error

    def test_select_model_unavailable(self, test_client, auth_headers, mock_model_router):
        """測試選擇不可用的模型"""
        selection_data = {
            "model_type": "gemini",
            "session_id": "test_session"
        }
        
        mock_model_router.select_model.side_effect = Exception("模型不可用")
        
        response = test_client.post(
            "/api/models/select",
            json=selection_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400

    def test_get_model_health_success(self, test_client, auth_headers, mock_model_router):
        """測試成功獲取模型健康狀態"""
        model_health = {
            "status": "healthy",
            "response_time_ms": 1200,
            "success_rate": 0.98,
            "concurrent_requests": 2,
            "error_count": 1,
            "last_check": datetime.now().isoformat()
        }
        
        mock_model_router.health_check.return_value = model_health
        
        response = test_client.get(
            "/api/models/health/chatgpt",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["status"] == "healthy"
        assert response_data["response_time_ms"] == 1200

    def test_get_all_models_health(self, test_client, auth_headers, mock_model_router):
        """測試獲取所有模型健康狀態"""
        all_health = {
            "chatgpt": {"status": "healthy", "response_time_ms": 1200},
            "claude": {"status": "healthy", "response_time_ms": 1500},
            "gemini": {"status": "unhealthy", "response_time_ms": 5000}
        }
        
        mock_model_router.health_check_all.return_value = all_health
        
        response = test_client.get("/api/models/health", headers=auth_headers)
        
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["health"]) == 3
        assert response_data["health"]["chatgpt"]["status"] == "healthy"

    def test_get_model_metrics_success(self, test_client, auth_headers, mock_model_router):
        """測試成功獲取模型使用統計"""
        metrics = {
            "requests_count": 150,
            "success_count": 147,
            "error_count": 3,
            "average_response_time": 1250,
            "success_rate": 0.98,
            "total_tokens": 75000,
            "average_tokens": 500
        }
        
        mock_model_router.get_model_metrics.return_value = metrics
        
        response = test_client.get(
            "/api/models/metrics/chatgpt",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["requests_count"] == 150
        assert response_data["success_rate"] == 0.98

    def test_get_model_metrics_with_timerange(self, test_client, auth_headers, mock_model_router):
        """測試獲取指定時間範圍的模型統計"""
        mock_model_router.get_model_metrics.return_value = {
            "requests_count": 50,
            "time_range": "last_hour"
        }
        
        response = test_client.get(
            "/api/models/metrics/chatgpt",
            params={"time_range": "hour"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        mock_model_router.get_model_metrics.assert_called_with(
            ModelType.CHATGPT,
            time_range="hour"
        )

    def test_benchmark_models_success(self, test_client, auth_headers, mock_model_router):
        """測試成功執行模型基準測試"""
        benchmark_request = {
            "test_messages": [
                "測試訊息1",
                "測試訊息2"
            ],
            "metrics": ["response_time", "accuracy", "cost"]
        }
        
        benchmark_results = {
            "chatgpt": {
                "average_response_time": 1200,
                "accuracy_score": 0.95,
                "cost_per_token": 0.002
            },
            "claude": {
                "average_response_time": 1500,
                "accuracy_score": 0.93,
                "cost_per_token": 0.003
            }
        }
        
        mock_model_router.benchmark_models.return_value = benchmark_results
        
        response = test_client.post(
            "/api/models/benchmark",
            json=benchmark_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert "chatgpt" in response_data["results"]
        assert "claude" in response_data["results"]

    def test_estimate_cost_success(self, test_client, auth_headers, mock_model_router):
        """測試成功估算使用成本"""
        cost_request = {
            "model_type": "chatgpt",
            "estimated_tokens": 1000,
            "expected_requests": 100
        }
        
        cost_estimate = {
            "model_type": "chatgpt",
            "estimated_cost": 2.50,
            "cost_breakdown": {
                "input_tokens": 1.00,
                "output_tokens": 1.50
            },
            "currency": "USD"
        }
        
        mock_model_router.estimate_cost.return_value = cost_estimate
        
        response = test_client.post(
            "/api/models/cost-estimate",
            json=cost_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["estimated_cost"] == 2.50
        assert response_data["currency"] == "USD"

    def test_compare_models_success(self, test_client, auth_headers, mock_model_router):
        """測試成功比較模型功能"""
        comparison_request = {
            "models": ["chatgpt", "claude"],
            "criteria": ["response_time", "accuracy", "cost"],
            "test_messages": ["測試訊息"]
        }
        
        comparison_results = {
            "comparison": {
                "chatgpt": {"score": 0.92, "rank": 1},
                "claude": {"score": 0.88, "rank": 2}
            },
            "criteria_weights": {
                "response_time": 0.4,
                "accuracy": 0.4,
                "cost": 0.2
            }
        }
        
        mock_model_router.compare_models.return_value = comparison_results
        
        response = test_client.post(
            "/api/models/compare",
            json=comparison_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert "comparison" in response_data
        assert response_data["comparison"]["chatgpt"]["rank"] == 1

    def test_update_model_config_success(self, test_client, auth_headers, mock_model_router):
        """測試成功更新模型配置"""
        config_update = {
            "enabled": True,
            "weight": 1.5,
            "max_requests_per_minute": 120,
            "timeout_seconds": 45
        }
        
        mock_model_router.update_model_config.return_value = True
        
        response = test_client.patch(
            "/api/models/config/chatgpt",
            json=config_update,
            headers=auth_headers
        )
        
        assert response.status_code == 200

    def test_update_model_config_unauthorized(self, test_client):
        """測試未授權的配置更新請求"""
        config_update = {"enabled": False}
        
        response = test_client.patch(
            "/api/models/config/chatgpt",
            json=config_update
        )
        
        assert response.status_code == 401

    def test_get_model_config(self, test_client, auth_headers, mock_model_router):
        """測試獲取模型配置"""
        model_config = {
            "model_type": "chatgpt",
            "enabled": True,
            "weight": 1.0,
            "max_requests_per_minute": 60,
            "timeout_seconds": 30
        }
        
        mock_model_router.get_model_config.return_value = model_config
        
        response = test_client.get(
            "/api/models/config/chatgpt",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["model_type"] == "chatgpt"
        assert response_data["enabled"] is True

    def test_reset_model_statistics(self, test_client, auth_headers, mock_model_router):
        """測試重置模型統計資料"""
        mock_model_router.reset_model_statistics.return_value = True
        
        response = test_client.post(
            "/api/models/reset-stats/chatgpt",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

    def test_warmup_models(self, test_client, auth_headers, mock_model_router):
        """測試模型預熱功能"""
        warmup_results = {
            "chatgpt": {"warmed_up": True, "time_taken": 2.5},
            "claude": {"warmed_up": True, "time_taken": 3.2}
        }
        
        mock_model_router.warmup_models.return_value = warmup_results
        
        response = test_client.post(
            "/api/models/warmup",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["results"]["chatgpt"]["warmed_up"] is True

    def test_get_load_balance_strategy(self, test_client, auth_headers, mock_model_router):
        """測試獲取負載平衡策略"""
        mock_model_router.get_load_balance_strategy.return_value = "weighted_round_robin"
        
        response = test_client.get(
            "/api/models/load-balance-strategy",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["strategy"] == "weighted_round_robin"

    def test_set_load_balance_strategy(self, test_client, auth_headers, mock_model_router):
        """測試設定負載平衡策略"""
        strategy_request = {"strategy": "health_based"}
        
        mock_model_router.set_load_balance_strategy.return_value = True
        
        response = test_client.put(
            "/api/models/load-balance-strategy",
            json=strategy_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200

    def test_get_model_usage_report(self, test_client, auth_headers, mock_model_router):
        """測試獲取模型使用報告"""
        usage_report = {
            "period": "last_30_days",
            "total_requests": 10000,
            "models": {
                "chatgpt": {"requests": 6000, "percentage": 60.0},
                "claude": {"requests": 4000, "percentage": 40.0}
            },
            "cost_analysis": {
                "total_cost": 250.50,
                "cost_by_model": {
                    "chatgpt": 150.30,
                    "claude": 100.20
                }
            }
        }
        
        mock_model_router.generate_usage_report.return_value = usage_report
        
        response = test_client.get(
            "/api/models/usage-report",
            params={"period": "30d"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total_requests"] == 10000
        assert "cost_analysis" in response_data

    def test_export_model_data(self, test_client, auth_headers, mock_model_router):
        """測試匯出模型資料"""
        export_data = {
            "models": ["chatgpt", "claude"],
            "format": "json",
            "data": {
                "configurations": {},
                "metrics": {},
                "health": {}
            }
        }
        
        mock_model_router.export_model_data.return_value = json.dumps(export_data)
        
        response = test_client.get(
            "/api/models/export",
            params={"format": "json", "include": "all"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

    def test_model_performance_trends(self, test_client, auth_headers, mock_model_router):
        """測試獲取模型效能趨勢"""
        trends_data = {
            "model_type": "chatgpt",
            "time_series": {
                "response_time": [
                    {"timestamp": "2023-01-01T00:00:00", "value": 1200},
                    {"timestamp": "2023-01-01T01:00:00", "value": 1150}
                ],
                "success_rate": [
                    {"timestamp": "2023-01-01T00:00:00", "value": 0.98},
                    {"timestamp": "2023-01-01T01:00:00", "value": 0.99}
                ]
            }
        }
        
        mock_model_router.get_performance_trends.return_value = trends_data
        
        response = test_client.get(
            "/api/models/trends/chatgpt",
            params={"period": "24h", "metrics": "response_time,success_rate"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert "time_series" in response_data
        assert len(response_data["time_series"]["response_time"]) == 2

    def test_admin_only_endpoints(self, test_client, auth_headers, mock_model_router):
        """測試管理員專用端點"""
        # 模擬非管理員用戶
        with patch('app.controllers.model_controller.check_admin_permission') as mock_check:
            mock_check.side_effect = Exception("權限不足")
            
            response = test_client.patch(
                "/api/models/config/chatgpt",
                json={"enabled": False},
                headers=auth_headers
            )
            
            assert response.status_code == 403

    def test_rate_limiting_model_endpoints(self, test_client, auth_headers):
        """測試模型端點的速率限制"""
        # 快速連續請求
        responses = []
        for _ in range(50):
            response = test_client.get(
                "/api/models/available",
                headers=auth_headers
            )
            responses.append(response)
        
        # 檢查是否觸發速率限制
        rate_limited = any(r.status_code == 429 for r in responses[-10:])
        # 根據實際的速率限制設定調整此斷言

    def test_model_selection_history(self, test_client, auth_headers, mock_model_router):
        """測試獲取模型選擇歷史"""
        selection_history = [
            {
                "timestamp": "2023-01-01T10:00:00",
                "model_type": "chatgpt",
                "session_id": "session1",
                "reason": "best_performance"
            },
            {
                "timestamp": "2023-01-01T10:05:00",
                "model_type": "claude",
                "session_id": "session1", 
                "reason": "fallback"
            }
        ]
        
        mock_model_router.get_selection_history.return_value = selection_history
        
        response = test_client.get(
            "/api/models/selection-history",
            params={"session_id": "session1", "limit": 10},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["history"]) == 2


class TestModelControllerEdgeCases:
    """模型控制器邊界情況測試"""

    def test_invalid_model_type_parameter(self, test_client, auth_headers):
        """測試無效的模型類型參數"""
        response = test_client.get(
            "/api/models/health/invalid_model",
            headers=auth_headers
        )
        
        assert response.status_code == 400

    def test_missing_required_parameters(self, test_client, auth_headers):
        """測試缺少必要參數的請求"""
        response = test_client.post(
            "/api/models/select",
            json={},  # 缺少必要欄位
            headers=auth_headers
        )
        
        assert response.status_code == 422

    def test_service_unavailable_handling(self, test_client, auth_headers, mock_model_router):
        """測試服務不可用時的處理"""
        mock_model_router.get_available_models.side_effect = Exception("服務暫時不可用")
        
        response = test_client.get("/api/models/available", headers=auth_headers)
        
        assert response.status_code == 503

    def test_concurrent_model_operations(self, test_client, auth_headers, mock_model_router):
        """測試並發模型操作"""
        import asyncio
        
        async def make_request():
            return test_client.get("/api/models/available", headers=auth_headers)
        
        # 模擬並發請求
        tasks = [make_request() for _ in range(20)]
        # 在實際測試中需要適當的異步處理
        
        # 驗證系統能夠處理並發請求
        # 具體實現依賴於測試框架的支援


class TestModelControllerSecurity:
    """模型控制器安全性測試"""

    def test_token_validation(self, test_client, mock_auth_service):
        """測試 Token 驗證"""
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        
        with patch('app.controllers.model_controller.get_auth_service') as mock_auth:
            mock_auth.return_value.verify_token.side_effect = Exception("無效的 Token")
            
            response = test_client.get(
                "/api/models/available",
                headers=invalid_headers
            )
            
            assert response.status_code == 401

    def test_permission_based_access(self, test_client, auth_headers, mock_auth_service):
        """測試基於權限的存取控制"""
        # 模擬沒有管理員權限的用戶
        with patch('app.controllers.model_controller.check_permission') as mock_check:
            mock_check.return_value = False
            
            response = test_client.patch(
                "/api/models/config/chatgpt",
                json={"enabled": False},
                headers=auth_headers
            )
            
            assert response.status_code == 403

    def test_input_sanitization(self, test_client, auth_headers):
        """測試輸入清理和驗證"""
        malicious_input = {
            "model_type": "<script>alert('xss')</script>",
            "session_id": "'; DROP TABLE models; --"
        }
        
        response = test_client.post(
            "/api/models/select",
            json=malicious_input,
            headers=auth_headers
        )
        
        # 應該被驗證層拒絕
        assert response.status_code in [400, 422]