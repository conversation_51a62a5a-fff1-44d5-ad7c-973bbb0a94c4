{"version": 3, "file": "PublicClientNext.mjs", "sources": ["../../src/app/PublicClientNext.ts"], "sourcesContent": [null], "names": ["ControllerFactory.createController"], "mappings": ";;;;;;AAAA;;;AAGG;AA+BH;;;;;;;;;;AAUG;MACU,gBAAgB,CAAA;AAQlB,IAAA,aAAa,6BAA6B,CAC7C,aAA4B,EAAA;QAE5B,MAAM,UAAU,GAAG,MAAMA,gBAAkC,CACvD,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,GAAG,CAAC;QACR,IAAI,UAAU,KAAK,IAAI,EAAE;YACrB,GAAG,GAAG,IAAI,gBAAgB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;AACH,YAAA,GAAG,GAAG,IAAI,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAC7C,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACd;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;IACH,WACI,CAAA,aAA4B,EAC5B,UAAwB,EAAA;AAExB,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC;YACpE,IAAI,CAAC,UAAU,GAAG,IAAI,iCAAiC,CACnD,gBAAgB,CACnB,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,MAAM,UAAU,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,UAAU,YAAY,iCAAiC,EAAE;YAC9D,MAAM,MAAM,GAAG,MAAMA,gBAAkC,CACnD,IAAI,CAAC,aAAa,CACrB,CAAC;YACF,IAAI,MAAM,KAAK,IAAI,EAAE;AACjB,gBAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;AAC5B,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;AACvC,SAAA;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC5B;AAED;;;;;;AAMG;IACH,MAAM,iBAAiB,CACnB,OAAqB,EAAA;QAErB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;KACrD;AAED;;;;;;;;AAQG;AACH,IAAA,oBAAoB,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;KACxD;AAED;;;;;AAKG;AACH,IAAA,kBAAkB,CACd,aAA4B,EAAA;QAE5B,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC5D;AAED;;;;;;;;;AASG;AACH,IAAA,kBAAkB,CACd,OAAiC,EAAA;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KACtD;AAED;;;AAGG;IACH,gBAAgB,CACZ,QAA+B,EAC/B,UAA6B,EAAA;QAE7B,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;KACjE;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,UAAkB,EAAA;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;KAC1D;AAED;;;;;AAKG;AACH,IAAA,sBAAsB,CAAC,QAAqC,EAAA;QACxD,OAAO,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;KAC3D;AAED;;;;;AAKG;AACH,IAAA,yBAAyB,CAAC,UAAkB,EAAA;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;KAChE;AAED;;AAEG;IACH,0BAA0B,GAAA;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC;KAChD;AAED;;AAEG;IACH,2BAA2B,GAAA;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC;KACjD;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,aAA4B,EAAA;QACnC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KACpD;AAED;;;;;;;AAOG;AACH,IAAA,kBAAkB,CAAC,aAAqB,EAAA;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC5D;AAED;;;;;;;AAOG;AACH,IAAA,mBAAmB,CAAC,OAAe,EAAA;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;KACvD;AAED;;;;;;;;AAQG;AACH,IAAA,oBAAoB,CAAC,QAAgB,EAAA;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;KACzD;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,aAA6B,EAAA;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KACxD;AAED;;;;;;AAMG;AACH,IAAA,qBAAqB,CACjB,IAAyB,EAAA;QAEzB,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACtD;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CACN,OAAkC,EAAA;QAElC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC9C;AAED;;;;;;;;AAQG;AACH,IAAA,aAAa,CAAC,OAAqC,EAAA;QAC/C,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;KACjD;AAED;;;;AAIG;AACH,IAAA,MAAM,CAAC,aAAiC,EAAA;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KAChD;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,aAAiC,EAAA;QAC5C,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KACxD;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,aAAiC,EAAA;QACzC,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;KACrD;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,SAAS,CAAC,OAAyB,EAAA;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAC7C;AAED;;AAEG;IACH,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;KAC1C;AAED;;AAEG;IACH,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;KACtC;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAc,EAAA;AACpB,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,OAA2B,EAAA;AACxC,QAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;KAC7C;AAED;;AAEG;IACH,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;KAC7C;AAED;;;;AAIG;IACH,wBAAwB,CAAC,GAAe,EAAE,OAAe,EAAA;QACrD,OAAO,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACjE;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,gBAAmC,EAAA;AACnD,QAAA,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;KACzD;AAED;;;AAGG;IACH,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;KAC7C;AAED;;;;;AAKG;AACH,IAAA,MAAM,YAAY,CACd,MAA4B,EAC5B,OAIkB,EAAA;QAElB,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACxD;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,aAAiC,EAAA;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KACpD;AACJ;;;;"}