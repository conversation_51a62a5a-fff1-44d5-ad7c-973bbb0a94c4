"""
MCP (Model Context Protocol) 服務模組
與 AWS Lambda MCP 伺服器通訊，存取 LightRAG 知識庫
支援 BPM 知識庫查詢、文件檢索和上下文增強
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import httpx
from fastapi import HTTPException, status
import asyncio
import hashlib
import backoff

# 設定日誌
logger = logging.getLogger(__name__)

class MCPService:
    """MCP 服務類別"""
    
    def __init__(self):
        # MCP 伺服器配置
        self.mcp_endpoint = os.getenv("MCP_ENDPOINT")
        self.mcp_api_key = os.getenv("MCP_API_KEY")
        self.mcp_timeout = int(os.getenv("MCP_TIMEOUT", "30"))
        
        if not self.mcp_endpoint:
            logger.warning("MCP_ENDPOINT 環境變數未設定")
        
        # LightRAG 配置
        self.lightrag_index = os.getenv("LIGHTRAG_INDEX", "bpm_knowledge")
        self.search_limit = int(os.getenv("LIGHTRAG_SEARCH_LIMIT", "10"))
        self.similarity_threshold = float(os.getenv("LIGHTRAG_SIMILARITY_THRESHOLD", "0.7"))
        
        # HTTP 客戶端配置
        self.client_config = {
            "timeout": httpx.Timeout(
                connect=5.0,  # 連接超時
                read=self.mcp_timeout,  # 讀取超時
                write=10.0,  # 寫入超時
                pool=30.0  # 連接池超時
            ),
            "limits": httpx.Limits(
                max_keepalive_connections=10,  # 最大保活連接數
                max_connections=20,  # 最大連接數
                keepalive_expiry=30.0  # 保活過期時間
            ),
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "BPM-Chatbot/1.0",
                "Accept": "application/json",
                "Connection": "keep-alive"
            },
            "follow_redirects": True,
            "verify": True
        }
        
        if self.mcp_api_key:
            self.client_config["headers"]["Authorization"] = f"Bearer {self.mcp_api_key}"
        
        # 快取配置
        self.enable_cache = os.getenv("MCP_ENABLE_CACHE", "true").lower() == "true"
        self.cache_ttl = int(os.getenv("MCP_CACHE_TTL", "300"))  # 5 minutes
        self._query_cache: Dict[str, Dict] = {}
        
        # 統計
        self.request_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "average_response_time": 0.0
        }
    
    @backoff.on_exception(
        backoff.expo,
        (httpx.RequestError, httpx.HTTPStatusError),
        max_tries=3,
        max_time=30
    )
    async def search_knowledge(
        self,
        query: str,
        limit: Optional[int] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        在 LightRAG 知識庫中搜尋相關內容
        
        Args:
            query: 搜尋查詢
            limit: 結果數量限制
            filters: 搜尋過濾條件
        
        Returns:
            搜尋結果字典
        """
        
        if not self.mcp_endpoint:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="MCP 服務未配置"
            )
        
        # 檢查快取
        cache_key = self._generate_cache_key(query, limit, filters)
        if self.enable_cache and cache_key in self._query_cache:
            cached_result = self._query_cache[cache_key]
            if self._is_cache_valid(cached_result):
                self.request_stats["cache_hits"] += 1
                logger.debug(f"快取命中: {query[:50]}...")
                return cached_result["data"]
        
        self.request_stats["total_requests"] += 1
        start_time = datetime.utcnow()
        
        try:
            # 建構 MCP 請求
            mcp_request = {
                "jsonrpc": "2.0",
                "id": f"search_{int(datetime.utcnow().timestamp())}",
                "method": "lightrag/search",
                "params": {
                    "index": self.lightrag_index,
                    "query": query,
                    "limit": limit or self.search_limit,
                    "similarity_threshold": self.similarity_threshold,
                    "filters": filters or {}
                }
            }
            
            # 發送請求到 MCP 伺服器
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    f"{self.mcp_endpoint}/invoke",
                    json=mcp_request
                )
                
                response.raise_for_status()
                mcp_response = response.json()
            
            # 處理 MCP 回應
            if "error" in mcp_response:
                logger.error(f"MCP 伺服器錯誤: {mcp_response['error']}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"知識庫查詢失敗: {mcp_response['error']['message']}"
                )
            
            result = mcp_response.get("result", {})
            
            # 處理搜尋結果
            search_result = {
                "query": query,
                "total_results": result.get("total", 0),
                "results": result.get("documents", []),
                "search_time_ms": result.get("search_time_ms", 0),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 後處理結果
            search_result = self._post_process_results(search_result)
            
            # 更新統計
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            self._update_stats(response_time, True)
            
            # 快取結果
            if self.enable_cache:
                self._cache_result(cache_key, search_result)
            
            logger.info(f"知識庫搜尋完成: {query[:50]}..., 結果數: {search_result['total_results']}")
            return search_result
            
        except httpx.RequestError as e:
            logger.error(f"MCP 請求失敗: {e}")
            self._update_stats(0, False)
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="知識庫服務暫時無法使用"
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"MCP HTTP 錯誤: {e.response.status_code} - {e.response.text}")
            self._update_stats(0, False)
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"知識庫服務錯誤: {e.response.status_code}"
            )
        except Exception as e:
            logger.error(f"知識庫搜尋失敗: {e}")
            self._update_stats(0, False)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="知識庫查詢過程發生錯誤"
            )
    
    @backoff.on_exception(
        backoff.expo,
        (httpx.RequestError, httpx.HTTPStatusError),
        max_tries=2,
        max_time=15
    )
    async def get_document(self, document_id: str) -> Dict[str, Any]:
        """
        獲取特定文件內容
        
        Args:
            document_id: 文件 ID
        
        Returns:
            文件內容字典
        """
        
        if not self.mcp_endpoint:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="MCP 服務未配置"
            )
        
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": f"get_doc_{int(datetime.utcnow().timestamp())}",
                "method": "lightrag/get_document",
                "params": {
                    "index": self.lightrag_index,
                    "document_id": document_id
                }
            }
            
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    f"{self.mcp_endpoint}/invoke",
                    json=mcp_request
                )
                
                response.raise_for_status()
                mcp_response = response.json()
            
            if "error" in mcp_response:
                logger.error(f"MCP 文件獲取錯誤: {mcp_response['error']}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文件不存在或無法存取"
                )
            
            return mcp_response.get("result", {})
            
        except Exception as e:
            logger.error(f"獲取文件失敗: {document_id} - {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="文件獲取失敗"
            )
    
    async def add_context_to_query(
        self,
        user_query: str,
        context_limit: int = 3
    ) -> Dict[str, Any]:
        """
        為用戶查詢添加相關上下文
        
        Args:
            user_query: 用戶查詢
            context_limit: 上下文結果數量限制
        
        Returns:
            包含原始查詢和相關上下文的字典
        """
        
        try:
            # 搜尋相關知識
            search_result = await self.search_knowledge(
                query=user_query,
                limit=context_limit
            )
            
            # 提取相關上下文
            contexts = []
            for doc in search_result.get("results", []):
                context = {
                    "title": doc.get("title", ""),
                    "content": doc.get("content", "")[:500],  # 限制內容長度
                    "relevance_score": doc.get("score", 0.0),
                    "source": doc.get("source", ""),
                    "metadata": doc.get("metadata", {})
                }
                contexts.append(context)
            
            return {
                "original_query": user_query,
                "contexts": contexts,
                "context_count": len(contexts),
                "total_available": search_result.get("total_results", 0),
                "search_timestamp": search_result.get("timestamp")
            }
            
        except Exception as e:
            logger.error(f"添加查詢上下文失敗: {e}")
            # 如果知識庫查詢失敗，返回僅包含原始查詢的結果
            return {
                "original_query": user_query,
                "contexts": [],
                "context_count": 0,
                "total_available": 0,
                "error": str(e)
            }
    
    @backoff.on_exception(
        backoff.expo,
        (httpx.RequestError, httpx.HTTPStatusError),
        max_tries=2,
        max_time=20
    )
    async def batch_search_knowledge(
        self,
        queries: List[str],
        limit_per_query: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        批量搜尋多個查詢
        
        Args:
            queries: 查詢列表
            limit_per_query: 每個查詢的結果限制
        
        Returns:
            包含所有查詢結果的字典
        """
        
        if not self.mcp_endpoint:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="MCP 服務未配置"
            )
        
        batch_results = {
            "queries": queries,
            "results": [],
            "total_queries": len(queries),
            "successful_queries": 0,
            "failed_queries": 0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 並行處理查詢（限制並發數）
        semaphore = asyncio.Semaphore(3)  # 最多3個並發查詢
        
        async def process_single_query(query: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    result = await self.search_knowledge(
                        query=query,
                        limit=limit_per_query
                    )
                    return {
                        "query": query,
                        "status": "success",
                        "data": result
                    }
                except Exception as e:
                    logger.error(f"批量查詢失敗 - 查詢: {query}, 錯誤: {e}")
                    return {
                        "query": query,
                        "status": "failed",
                        "error": str(e)
                    }
        
        # 執行所有查詢
        tasks = [process_single_query(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 統計結果
        for result in results:
            if isinstance(result, dict):
                batch_results["results"].append(result)
                if result.get("status") == "success":
                    batch_results["successful_queries"] += 1
                else:
                    batch_results["failed_queries"] += 1
            else:
                # 處理異常情況
                batch_results["results"].append({
                    "query": "unknown",
                    "status": "error",
                    "error": str(result)
                })
                batch_results["failed_queries"] += 1
        
        batch_results["success_rate"] = (
            batch_results["successful_queries"] / batch_results["total_queries"]
            if batch_results["total_queries"] > 0 else 0
        )
        
        logger.info(
            f"批量查詢完成: 總數={batch_results['total_queries']}, "
            f"成功={batch_results['successful_queries']}, "
            f"失敗={batch_results['failed_queries']}"
        )
        
        return batch_results
    
    @backoff.on_exception(
        backoff.expo,
        (httpx.RequestError, httpx.HTTPStatusError),
        max_tries=2,
        max_time=10
    )
    async def health_check(self) -> Dict[str, Any]:
        """
        檢查 MCP 服務健康狀態
        
        Returns:
            健康檢查結果
        """
        
        if not self.mcp_endpoint:
            return {
                "status": "unavailable",
                "message": "MCP 端點未配置",
                "timestamp": datetime.utcnow().isoformat()
            }
        
        try:
            start_time = datetime.utcnow()
            
            # 發送健康檢查請求
            mcp_request = {
                "jsonrpc": "2.0",
                "id": "health_check",
                "method": "health"
            }
            
            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    f"{self.mcp_endpoint}/health",
                    json=mcp_request
                )
                
                response.raise_for_status()
                
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "endpoint": self.mcp_endpoint,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"MCP 健康檢查失敗: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "endpoint": self.mcp_endpoint,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def _generate_cache_key(
        self,
        query: str,
        limit: Optional[int],
        filters: Optional[Dict[str, Any]]
    ) -> str:
        """生成快取鍵"""
        cache_data = {
            "query": query,
            "limit": limit,
            "filters": filters or {}
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cached_result: Dict) -> bool:
        """檢查快取是否有效"""
        if "cached_at" not in cached_result:
            return False
        
        cached_time = datetime.fromisoformat(cached_result["cached_at"])
        now = datetime.utcnow()
        
        return (now - cached_time).total_seconds() < self.cache_ttl
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """快取結果"""
        cached_result = {
            "data": result,
            "cached_at": datetime.utcnow().isoformat()
        }
        
        self._query_cache[cache_key] = cached_result
        
        # 限制快取大小
        if len(self._query_cache) > 100:
            # 移除最舊的快取項目
            oldest_key = min(
                self._query_cache.keys(),
                key=lambda k: self._query_cache[k]["cached_at"]
            )
            del self._query_cache[oldest_key]
    
    def _post_process_results(self, search_result: Dict[str, Any]) -> Dict[str, Any]:
        """後處理搜尋結果"""
        results = search_result.get("results", [])
        
        # 排序結果（按相關性分數）
        results.sort(key=lambda x: x.get("score", 0.0), reverse=True)
        
        # 過濾低相關性結果
        filtered_results = [
            result for result in results
            if result.get("score", 0.0) >= self.similarity_threshold
        ]
        
        search_result["results"] = filtered_results
        search_result["filtered_count"] = len(filtered_results)
        
        return search_result
    
    def _update_stats(self, response_time: float, success: bool) -> None:
        """更新統計資訊"""
        if success:
            self.request_stats["successful_requests"] += 1
        else:
            self.request_stats["failed_requests"] += 1
        
        # 更新平均回應時間
        total_requests = self.request_stats["total_requests"]
        if total_requests > 0:
            current_avg = self.request_stats["average_response_time"]
            new_avg = ((current_avg * (total_requests - 1)) + response_time) / total_requests
            self.request_stats["average_response_time"] = new_avg
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取服務統計"""
        total_requests = self.request_stats["total_requests"]
        
        return {
            **self.request_stats,
            "success_rate": (
                self.request_stats["successful_requests"] / total_requests
                if total_requests > 0 else 0
            ),
            "cache_hit_rate": (
                self.request_stats["cache_hits"] / total_requests
                if total_requests > 0 else 0
            ),
            "cache_size": len(self._query_cache),
            "configuration": {
                "endpoint": self.mcp_endpoint,
                "lightrag_index": self.lightrag_index,
                "search_limit": self.search_limit,
                "similarity_threshold": self.similarity_threshold,
                "cache_enabled": self.enable_cache,
                "cache_ttl": self.cache_ttl
            }
        }
    
    def clear_cache(self) -> None:
        """清除快取"""
        self._query_cache.clear()
        logger.info("MCP 查詢快取已清除")
    
    def reset_stats(self) -> None:
        """重置統計"""
        self.request_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "average_response_time": 0.0
        }
        logger.info("MCP 統計資訊已重置")

# 建立全域服務實例
mcp_service = MCPService()