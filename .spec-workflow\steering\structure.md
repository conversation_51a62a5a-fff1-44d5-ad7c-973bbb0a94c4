# Structure Steering

## Project Organization

### Directory Structure
```
will_chatbot/
├── README.md                    # 專案總覽和快速開始
├── docker-compose.yml           # 本地開發環境
├── docker-compose.prod.yml      # 生產環境設定
├── .env.example                 # 環境變數範例
├── .spec-workflow/              # Spec 工作流程文件
│   ├── steering/
│   │   ├── product.md
│   │   ├── tech.md
│   │   └── structure.md
│   └── specs/
│       └── bmp-chatbot/
│           ├── requirements.md
│           ├── design.md
│           └── tasks.md
├── backend/                     # Python FastAPI 後端
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── requirements-dev.txt     # 開發依賴
│   ├── .env.example
│   ├── pytest.ini              # pytest 設定
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py              # FastAPI 應用程式入口
│   │   ├── config/
│   │   │   ├── __init__.py
│   │   │   ├── settings.py      # 應用程式設定
│   │   │   └── bmp_keywords.py  # BMP 關鍵字配置
│   │   ├── controllers/         # API 端點控制器
│   │   │   ├── __init__.py
│   │   │   ├── auth_controller.py
│   │   │   ├── chat_controller.py
│   │   │   ├── model_controller.py
│   │   │   └── health_controller.py
│   │   ├── services/            # 業務邏輯服務
│   │   │   ├── __init__.py
│   │   │   ├── auth_service.py
│   │   │   ├── employee_service.py
│   │   │   ├── chat_service.py
│   │   │   ├── bmp_filter.py
│   │   │   ├── model_router.py
│   │   │   ├── session_service.py
│   │   │   └── redis_client.py
│   │   ├── clients/             # 外部服務客戶端
│   │   │   ├── __init__.py
│   │   │   ├── openai_client.py
│   │   │   ├── claude_client.py
│   │   │   ├── gemini_client.py
│   │   │   └── mcp_client.py
│   │   ├── models/              # 資料模型定義
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── employee.py
│   │   │   ├── message.py
│   │   │   ├── session.py
│   │   │   └── model.py
│   │   ├── middleware/          # HTTP 中介軟體
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── cors.py
│   │   │   └── logging.py
│   │   └── utils/               # 工具函數
│   │       ├── __init__.py
│   │       ├── logger.py
│   │       ├── exceptions.py
│   │       └── helpers.py
│   ├── tests/                   # 測試檔案
│   │   ├── __init__.py
│   │   ├── conftest.py          # pytest 共用設定
│   │   ├── unit/                # 單元測試
│   │   │   ├── controllers/
│   │   │   ├── services/
│   │   │   └── clients/
│   │   ├── integration/         # 整合測試
│   │   │   ├── test_api_endpoints.py
│   │   │   └── test_external_services.py
│   │   └── e2e/                 # 端對端測試
│   │       └── test_chat_flow.py
│   └── scripts/                 # 部署和維護腳本
│       ├── setup.py
│       ├── migrate.py
│       └── health_check.py
├── frontend/                    # Vue.js 前端
│   ├── Dockerfile
│   ├── package.json
│   ├── package-lock.json
│   ├── vite.config.ts           # Vite 建置設定
│   ├── tsconfig.json            # TypeScript 設定
│   ├── .env.example
│   ├── index.html
│   ├── public/                  # 靜態資源
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── src/
│   │   ├── main.ts              # 應用程式入口
│   │   ├── App.vue              # 根元件
│   │   ├── components/          # Vue 元件
│   │   │   ├── auth/
│   │   │   │   └── LoginComponent.vue
│   │   │   ├── chat/
│   │   │   │   ├── ChatContainer.vue
│   │   │   │   ├── MessageList.vue
│   │   │   │   ├── MessageInput.vue
│   │   │   │   └── Message.vue
│   │   │   ├── ui/
│   │   │   │   ├── ModelSelector.vue
│   │   │   │   ├── LoadingSpinner.vue
│   │   │   │   └── ErrorMessage.vue
│   │   │   └── layout/
│   │   │       ├── Header.vue
│   │   │       └── Footer.vue
│   │   ├── views/               # 頁面元件
│   │   │   ├── LoginView.vue
│   │   │   └── ChatView.vue
│   │   ├── stores/              # Pinia 狀態管理
│   │   │   ├── index.ts
│   │   │   ├── authStore.ts
│   │   │   └── chatStore.ts
│   │   ├── services/            # API 服務
│   │   │   ├── authService.ts
│   │   │   ├── apiClient.ts
│   │   │   ├── chatApi.ts
│   │   │   └── modelApi.ts
│   │   ├── types/               # TypeScript 類型定義
│   │   │   ├── auth.ts
│   │   │   ├── chat.ts
│   │   │   ├── api.ts
│   │   │   └── model.ts
│   │   ├── utils/               # 工具函數
│   │   │   ├── constants.ts
│   │   │   ├── helpers.ts
│   │   │   ├── errorHandler.ts
│   │   │   └── formatters.ts
│   │   ├── assets/              # 靜態資源
│   │   │   ├── images/
│   │   │   └── styles/
│   │   │       ├── variables.css
│   │   │       ├── theme.css
│   │   │       ├── global.css
│   │   │       └── components.css
│   │   └── router/              # Vue Router 設定
│   │       └── index.ts
│   ├── tests/                   # 前端測試
│   │   ├── setup.ts             # 測試環境設定
│   │   ├── unit/
│   │   │   ├── components/
│   │   │   └── services/
│   │   └── e2e/
│   │       └── chat.spec.ts
│   └── docs/                    # 前端文件
│       ├── COMPONENTS.md
│       └── DEPLOYMENT.md
├── lambda/                      # AWS Lambda 函數
│   ├── mcp_server.py           # MCP Server 實作
│   ├── requirements.txt
│   ├── serverless.yml          # Serverless Framework 設定
│   └── tests/
│       └── test_mcp_server.py
├── docs/                       # 專案文件
│   ├── ARCHITECTURE.md         # 系統架構說明
│   ├── API.md                  # API 文件
│   ├── DEPLOYMENT.md           # 部署指南
│   ├── TESTING.md              # 測試指南
│   └── CONTRIBUTING.md         # 貢獻指南
└── scripts/                    # 專案腳本
    ├── setup-dev.sh            # 開發環境設置
    ├── deploy.sh               # 部署腳本
    ├── backup.sh               # 資料備份
    └── test-all.sh             # 全面測試
```

### File Naming Conventions

#### General Rules
- **目錄**: 小寫，使用連字號分隔 (kebab-case)
  - ✅ `chat-service`, `user-auth`
  - ❌ `ChatService`, `user_auth`

- **檔案名稱**: 根據類型使用不同命名規則
  - **Python**: snake_case
    - ✅ `chat_service.py`, `auth_controller.py`
    - ❌ `ChatService.py`, `authController.py`
  - **TypeScript**: camelCase 或 PascalCase
    - ✅ `authService.ts`, `ChatContainer.vue`
    - ❌ `auth_service.ts`, `chat_container.vue`

#### Specific Naming Patterns

**Backend Python Files**
```
controllers/    -> {feature}_controller.py
services/       -> {feature}_service.py
clients/        -> {provider}_client.py
models/         -> {entity}.py
middleware/     -> {purpose}.py
utils/          -> {category}.py
tests/          -> test_{module}.py
```

**Frontend TypeScript Files**
```
components/     -> {FeatureName}.vue
services/       -> {feature}Service.ts
stores/         -> {feature}Store.ts
types/          -> {category}.ts
utils/          -> {category}.ts
views/          -> {PageName}View.vue
```

**Configuration Files**
```
Docker相關      -> Dockerfile, docker-compose.yml
環境設定        -> .env, .env.example, .env.{environment}
建置設定        -> vite.config.ts, tsconfig.json
套件管理        -> package.json, requirements.txt
```

### Module Organization

#### Backend Module Structure
```python
# 每個模組都應該有清楚的責任邊界

# controllers/ - HTTP 請求處理
class ChatController:
    """處理聊天相關的 HTTP API 請求"""
    def __init__(self, chat_service: ChatService):
        self.chat_service = chat_service
    
    async def send_message(self, request: ChatRequest) -> ChatResponse:
        return await self.chat_service.process_message(request)

# services/ - 業務邏輯處理  
class ChatService:
    """聊天核心業務邏輯"""
    def __init__(self, bmp_filter: BmpFilter, model_router: ModelRouter):
        self.bmp_filter = bmp_filter
        self.model_router = model_router
    
    async def process_message(self, request: ChatRequest) -> ChatResponse:
        # 實作業務邏輯
        pass

# clients/ - 外部服務整合
class OpenAIClient:
    """OpenAI API 客戶端封裝"""
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    async def chat_completion(self, messages: List[dict]) -> str:
        # API 調用邏輯
        pass
```

#### Frontend Module Structure
```typescript
// services/ - API 和業務邏輯
export class ChatService {
  constructor(private apiClient: ApiClient) {}
  
  async sendMessage(message: string, model: ModelType): Promise<ChatResponse> {
    return this.apiClient.post('/api/chat/message', { message, model })
  }
}

// stores/ - 狀態管理
export const useChatStore = defineStore('chat', () => {
  const messages = ref<Message[]>([])
  const isLoading = ref(false)
  
  const addMessage = (message: Message) => {
    messages.value.push(message)
  }
  
  return { messages, isLoading, addMessage }
})

// components/ - UI 元件
<template>
  <div class="chat-container">
    <!-- 元件內容 -->
  </div>
</template>

<script setup lang="ts">
import { useChatStore } from '@/stores/chatStore'
import type { Message } from '@/types/chat'

// 元件邏輯
</script>
```

### Configuration Management

#### Environment Variables
```bash
# .env.example - 範例環境變數檔案
# App Configuration
APP_NAME="BPM Chatbot"
DEBUG=false
LOG_LEVEL=INFO

# Database
REDIS_URL=redis://localhost:6379/0
REDIS_TTL_SESSION=1800
REDIS_TTL_CHAT=604800

# Authentication  
AAD_TENANT_ID=your-tenant-id
AAD_CLIENT_ID=your-client-id
AAD_CLIENT_SECRET=your-client-secret

# AI Services
OPENAI_API_KEY=your-openai-key
CLAUDE_API_KEY=your-claude-key
GEMINI_API_KEY=your-gemini-key

# AWS Lambda MCP
AWS_LAMBDA_FUNCTION_NAME=bmp-chatbot-mcp
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Performance
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT=30
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

#### Settings Management
```python
# backend/app/config/settings.py
from pydantic_settings import BaseSettings
from typing import List, Optional

class Settings(BaseSettings):
    # 分組管理設定
    class AppConfig:
        app_name: str = "BPM Chatbot"
        debug: bool = False
        log_level: str = "INFO"
    
    class DatabaseConfig:
        redis_url: str
        redis_ttl_session: int = 1800
        redis_ttl_chat: int = 604800
    
    class AuthConfig:
        aad_tenant_id: str
        aad_client_id: str
        aad_client_secret: str
        jwt_secret_key: str
    
    class AIConfig:
        openai_api_key: str
        claude_api_key: str
        gemini_api_key: str
        default_model: str = "chatgpt"
    
    # 載入所有設定
    app: AppConfig
    database: DatabaseConfig  
    auth: AuthConfig
    ai: AIConfig
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        # 支援巢狀設定
        env_nested_delimiter = "__"

# 使用方式
settings = Settings()
print(settings.app.app_name)  # 存取巢狀設定
```

## Development Workflow

### Development Process

#### Feature Development Flow
```
需求分析 → 設計確認 → 實作開發 → 測試驗證 → 部署上線

需求分析:
  - 閱讀需求文件
  - 確認技術可行性
  - 估算開發時間

設計確認:
  - 檢視設計文件
  - 確認 API 介面
  - 規劃實作順序

實作開發:
  - 按任務清單逐步實作
  - 遵循編碼規範
  - 撰寫單元測試

測試驗證:
  - 執行自動化測試
  - 手動功能測試
  - 整合測試驗證

部署上線:
  - 部署到測試環境
  - 驗收測試通過
  - 部署到生產環境
```

#### Code Review Process

#### Review Checklist
```markdown
## Code Review Checklist

### 功能性
- [ ] 功能是否符合需求規格？
- [ ] 是否有遺漏的邊界情況處理？
- [ ] 錯誤處理是否完整？

### 代碼品質  
- [ ] 代碼是否易讀易懂？
- [ ] 命名是否清晰有意義？
- [ ] 是否遵循專案的編碼規範？
- [ ] 是否有重複代碼可以重構？

### 安全性
- [ ] 是否有安全漏洞？
- [ ] 輸入驗證是否充足？
- [ ] 敏感資訊是否妥善處理？

### 效能
- [ ] 是否有效能瓶頸？
- [ ] 資料庫查詢是否最佳化？
- [ ] 是否有不必要的 API 調用？

### 測試
- [ ] 是否有足夠的測試覆蓋？
- [ ] 測試案例是否合理？
- [ ] 是否有整合測試？

### 文件
- [ ] 是否需要更新文件？
- [ ] API 變更是否有記錄？
- [ ] 複雜邏輯是否有註解？
```

#### Review Process
1. **自我檢查**: 提交前先自己 review 一遍
2. **同儕審查**: 至少一位團隊成員審查
3. **自動檢查**: CI/CD 管道自動測試和檢查
4. **整合測試**: 確保與現有功能的相容性
5. **部署準備**: 確認是否需要額外的部署步驟

### Testing Workflow

#### Testing Strategy
```bash
# 測試金字塔策略
Unit Tests (70%)     # 快速，獨立，大量
├── Service層測試
├── Utils函數測試  
└── Model驗證測試

Integration Tests (20%)  # 中速，依賴少，適量
├── API端點測試
├── 資料庫整合測試
└── 外部服務模擬測試

E2E Tests (10%)      # 慢速，完整流程，少量
├── 使用者登入流程
├── 完整聊天流程
└── 錯誤處理流程
```

#### Test Execution
```bash
# 本地測試
npm run test:unit     # 前端單元測試
npm run test:e2e      # 前端 E2E 測試
pytest tests/unit/    # 後端單元測試
pytest tests/integration/  # 後端整合測試

# 自動化測試管道
scripts/test-all.sh
├── Install dependencies
├── Run linting
├── Run unit tests
├── Run integration tests
├── Generate coverage report
└── Run security scan
```

### Deployment Process

#### Environment Stages
```yaml
# 部署流程
Development → Staging → Production

Development:
  - 本地開發環境
  - 用於功能開發測試
  - 資料可以隨時重置

Staging:  
  - 預生產環境
  - 與生產環境相同設定
  - 用於最終驗收測試

Production:
  - 正式生產環境
  - 嚴格的部署檢查清單
  - 監控和回滾機制
```

#### Deployment Checklist
```markdown
## 部署前檢查清單

### 代碼準備
- [ ] 所有測試通過
- [ ] Code review 完成
- [ ] 版本號已更新
- [ ] CHANGELOG 已更新

### 環境設定
- [ ] 環境變數已設定
- [ ] 資料庫遷移已準備
- [ ] 外部服務連線已確認

### 監控準備
- [ ] 日誌系統正常運作
- [ ] 監控儀表板已設定
- [ ] 警報規則已配置

### 回滾準備
- [ ] 回滾計劃已準備
- [ ] 資料備份已完成
- [ ] 團隊成員待命
```

## Documentation Structure

### Documentation Hierarchy
```
docs/
├── README.md                    # 專案概覽，快速開始
├── ARCHITECTURE.md              # 系統架構詳細說明
├── API.md                       # API 端點完整文件
├── DEPLOYMENT.md                # 部署指南和設定
├── TESTING.md                   # 測試策略和執行方式
├── CONTRIBUTING.md              # 貢獻指南和開發規範
├── TROUBLESHOOTING.md           # 常見問題和解決方案
├── SECURITY.md                  # 安全考量和最佳實踐
└── CHANGELOG.md                 # 版本更新記錄

# 各模組內部文件
backend/
├── README.md                    # 後端設置和開發指南
└── docs/
    ├── API_DESIGN.md           # API 設計原則
    └── DATABASE_SCHEMA.md      # 資料結構說明

frontend/  
├── README.md                    # 前端設置和開發指南
└── docs/
    ├── COMPONENTS.md           # 元件使用說明
    └── STATE_MANAGEMENT.md     # 狀態管理架構
```

### Documentation Standards

#### API Documentation
```python
# 使用 FastAPI 自動生成文件
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI(
    title="BPM Chatbot API",
    description="智能 BPM 問答系統 API",
    version="1.0.0",
    docs_url="/docs",      # Swagger UI
    redoc_url="/redoc"     # ReDoc
)

class ChatRequest(BaseModel):
    """聊天請求模型
    
    Attributes:
        message: 使用者訊息內容
        model: 選擇的 AI 模型
        session_id: 會話 ID
    """
    message: str = Field(..., description="使用者訊息", example="BPM 簽核流程是什麼？")
    model: ModelType = Field(..., description="AI 模型類型")
    session_id: str = Field(..., description="會話識別碼")

@app.post("/api/chat/message", 
         summary="發送聊天訊息",
         description="處理使用者 BPM 相關問題並回傳 AI 生成的答案")
async def send_message(request: ChatRequest) -> ChatResponse:
    """處理聊天訊息
    
    這個端點會：
    1. 驗證使用者身份
    2. 檢查問題是否與 BPM 相關
    3. 路由到選定的 AI 模型
    4. 回傳格式化的答案
    
    Args:
        request: 包含訊息內容和設定的請求物件
        
    Returns:
        ChatResponse: 包含 AI 回應和元資料的回應物件
        
    Raises:
        HTTPException: 當認證失敗或問題不相關時
    """
    pass
```

#### Code Documentation
```python
def filter_bmp_relevance(message: str) -> bool:
    """檢查訊息是否與 BPM 相關
    
    使用關鍵字匹配和語意分析來判斷使用者問題
    是否與 BPM (Business Process Management) 相關。
    
    Algorithm:
        1. 檢查 BPM 相關關鍵字 (簽核、流程、規章等)
        2. 使用 TF-IDF 進行語意相似度計算
        3. 結合規則和機器學習結果做最終判斷
    
    Args:
        message: 使用者輸入的訊息內容
        
    Returns:
        bool: True 表示與 BPM 相關，False 表示無關
        
    Example:
        >>> filter_bmp_relevance("BPM 簽核流程是什麼？")
        True
        >>> filter_bmp_relevance("今天天氣如何？")
        False
    """
    pass
```

## Team Conventions

### Communication Guidelines

#### Meeting Structure
```markdown
## 定期會議

### Daily Standup (每日，15分鐘)
- **時間**: 每天上午 9:30
- **參與者**: 開發團隊
- **格式**: 
  - 昨天完成了什麼？
  - 今天計劃做什麼？
  - 有什麼阻礙需要協助？

### Sprint Planning (每兩週，2小時)
- **時間**: 每兩週一上午 10:00
- **參與者**: 全團隊 + Stakeholders
- **目標**: 規劃下個 Sprint 的任務和目標

### Sprint Review (每兩週，1小時)  
- **時間**: 每兩週五下午 4:00
- **參與者**: 全團隊 + Stakeholders
- **目標**: 展示完成的功能，收集回饋

### Retrospective (每兩週，1小時)
- **時間**: Sprint Review 後
- **參與者**: 開發團隊
- **目標**: 檢討流程，持續改進
```

#### Communication Channels
```markdown
## 溝通管道

### 即時通訊
- **Teams/Slack**: 日常討論，快速問答
- **Channel 分組**:
  - #general: 一般討論
  - #development: 技術討論
  - #testing: 測試相關
  - #deployment: 部署和維運

### 非同步溝通
- **Email**: 正式通知，重要決策
- **Issue Tracking**: Bug 報告，功能請求
- **Code Comments**: 代碼審查討論

### 文件協作
- **Confluence/Notion**: 需求文件，設計文件
- **內部 Wiki**: 技術文件，操作手冊
```

### Decision Making Process

#### Decision Types
```markdown
## 決策類型和流程

### Type 1: 可逆決策 (開發層級)
- **負責人**: 開發者個人
- **流程**: 討論 → 決定 → 實作
- **範例**: 變數命名，小功能實作方式

### Type 2: 難逆決策 (架構層級)  
- **負責人**: 技術主管 + 團隊
- **流程**: 研究 → 提案 → 討論 → 投票 → 決定
- **範例**: 技術棧選擇，資料庫設計

### Type 3: 策略決策 (產品層級)
- **負責人**: 產品經理 + Stakeholders
- **流程**: 分析 → 提案 → 審查 → 決定 → 溝通
- **範例**: 功能優先順序，產品方向
```

#### Decision Documentation
```markdown
## 決策記錄範本 (ADR - Architecture Decision Record)

### ADR-001: 選擇 Vue.js 作為前端框架

**Status**: Accepted
**Date**: 2024-01-15
**Deciders**: 技術團隊

**Context**:
需要選擇前端框架來建構 BPM Chatbot 使用者介面。

**Decision**: 
選擇 Vue.js 3 作為前端框架。

**Rationale**:
- 團隊已有 Vue.js 經驗
- 生態系統成熟，社群活躍  
- TypeScript 支援良好
- 學習曲線相對平緩
- 適合中小型專案

**Consequences**:
- **Positive**: 快速開發，團隊熟悉
- **Negative**: 相較 React 職位市場較小
- **Risks**: Vue.js 3 仍在演進中

**Alternatives Considered**:
- React: 更大的生態系統，但團隊經驗不足
- Angular: 功能完整，但過於複雜
```

### Knowledge Sharing

#### Knowledge Management
```markdown
## 知識分享機制

### Tech Talks (每月)
- **目的**: 分享新技術，經驗交流
- **格式**: 20 分鐘簡報 + 10 分鐘討論
- **主題範例**:
  - "Redis 快取策略最佳實踐"
  - "Vue 3 Composition API 實戰"
  - "FastAPI 效能優化技巧"

### Code Review Learning
- **目的**: 透過代碼審查學習
- **實踐**:
  - 詳細的審查說明
  - 建設性的審查意見
  - 錯誤和學習的分享

### Documentation First
- **原則**: 重要決策先寫文件
- **實踐**:
  - 新功能先寫設計文件
  - 複雜邏輯必須有註解
  - 維護 FAQ 和 Troubleshooting
```

#### Onboarding Process
```markdown
## 新成員入職流程

### Day 1: 環境設置
- [ ] 開發環境安裝 (IDE, Docker, Node.js, Python)
- [ ] 代碼庫存取權限
- [ ] 通訊工具帳號建立
- [ ] 閱讀專案 README

### Week 1: 熟悉專案
- [ ] 閱讀 Steering Documents
- [ ] 了解系統架構
- [ ] 執行本地測試環境
- [ ] 完成一個小的 Bug Fix

### Week 2: 參與開發
- [ ] 參加所有定期會議
- [ ] 負責一個小功能開發
- [ ] 進行第一次 Code Review
- [ ] 提供新人視角的回饋

### Month 1: 獨立貢獻
- [ ] 獨立完成功能開發
- [ ] 能夠 Review 其他人的代碼
- [ ] 更新文件或改進流程
- [ ] 分享學習心得
```

## File Organization Standards

### Import and Export Conventions

#### Python Imports
```python
# 標準庫導入
import os
import sys
from datetime import datetime
from typing import List, Dict, Optional

# 第三方庫導入
import fastapi
from pydantic import BaseModel
from redis import Redis

# 本地模組導入
from app.config.settings import settings
from app.models.auth import Employee
from app.services.chat_service import ChatService
```

#### TypeScript Imports
```typescript
// 第三方庫導入
import { ref, reactive, computed } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'

// 本地模組導入
import type { ChatMessage, ModelType } from '@/types/chat'
import { useChatStore } from '@/stores/chatStore'
import ApiClient from '@/services/apiClient'
```

### Backup and Recovery

#### Data Backup Strategy
```bash
# 定期備份腳本
scripts/backup.sh
├── Redis 資料備份
├── 配置檔案備份
├── 日誌檔案歸檔
└── 環境設定備份

# 備份頻率
- 每日: Redis 資料
- 每週: 完整系統備份
- 每月: 長期歸檔備份
```

#### Recovery Procedures
```markdown
## 災難恢復程序

### Level 1: 服務中斷
- **症狀**: API 無回應，前端無法載入
- **處理**: 重啟服務，檢查日誌
- **時間**: 5-10 分鐘

### Level 2: 資料損壞
- **症狀**: Redis 資料遺失，會話無效
- **處理**: 從備份恢復資料
- **時間**: 15-30 分鐘

### Level 3: 系統損壞
- **症狀**: 完整系統無法運作
- **處理**: 從完整備份重建系統
- **時間**: 1-2 小時
```

這個更新版的 structure steering 移除了所有 git 相關的內容，專注於專案組織、開發流程和團隊協作，但不依賴版本控制系統。現在您可以繼續完成 spec 的任務階段了！