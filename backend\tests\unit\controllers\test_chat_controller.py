"""
聊天控制器單元測試
測試 ChatController 的 HTTP API 端點功能
"""

import pytest
import json
from unittest.mock import AsyncMock, Mock, patch
from fastapi import HTTPException, status
from fastapi.testclient import TestClient
from datetime import datetime
import uuid

from app.controllers.chat_controller import Cha<PERSON><PERSON><PERSON>roller, router
from app.models.message import MessageCreateRequest, MessageResponse
from app.models.session import Session


class TestChatController:
    """聊天控制器測試類"""

    @pytest.fixture
    def mock_chat_service(self):
        """模擬聊天服務"""
        service = AsyncMock()
        return service

    @pytest.fixture
    def mock_auth_service(self):
        """模擬認證服務"""
        service = AsyncMock()
        service.verify_token.return_value = {
            "user_id": "test_user",
            "employee_id": "test_employee"
        }
        return service

    @pytest.fixture
    def chat_controller(self, mock_chat_service, mock_auth_service):
        """創建聊天控制器實例"""
        with patch('app.controllers.chat_controller.get_chat_service', return_value=mock_chat_service), \
             patch('app.controllers.chat_controller.get_auth_service', return_value=mock_auth_service):
            return ChatController()

    @pytest.fixture
    def test_client(self, chat_controller):
        """創建測試客戶端"""
        from fastapi import FastAPI
        app = FastAPI()
        app.include_router(router)
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """認證標頭"""
        return {"Authorization": "Bearer test_token"}

    def test_send_message_success(self, test_client, auth_headers, mock_chat_service):
        """測試成功發送訊息"""
        # 準備測試資料
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "測試訊息",
            "role": "user"
        }
        
        mock_response = MessageResponse(
            message_id=str(uuid.uuid4()),
            session_id=message_data["session_id"],
            content=message_data["content"],
            role="user",
            timestamp=datetime.now(),
            status="completed"
        )
        
        mock_chat_service.create_message.return_value = mock_response
        
        # 發送請求
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        # 驗證回應
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["content"] == message_data["content"]
        assert response_data["role"] == message_data["role"]

    def test_send_message_unauthorized(self, test_client):
        """測試未授權的訊息發送請求"""
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "測試訊息",
            "role": "user"
        }
        
        # 沒有提供認證標頭
        response = test_client.post("/api/chat/message", json=message_data)
        
        assert response.status_code == 401

    def test_send_message_invalid_data(self, test_client, auth_headers):
        """測試無效資料的訊息發送"""
        invalid_data = {
            "content": "",  # 空內容
            "role": "invalid_role"  # 無效角色
        }
        
        response = test_client.post(
            "/api/chat/message",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error

    def test_send_message_service_error(self, test_client, auth_headers, mock_chat_service):
        """測試服務錯誤處理"""
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "測試訊息",
            "role": "user"
        }
        
        # 模擬服務錯誤
        mock_chat_service.create_message.side_effect = Exception("服務錯誤")
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        assert response.status_code == 500

    def test_get_message_history_success(self, test_client, auth_headers, mock_chat_service):
        """測試成功獲取訊息歷史"""
        session_id = str(uuid.uuid4())
        
        mock_messages = [
            {
                "message_id": str(uuid.uuid4()),
                "content": "訊息1",
                "role": "user",
                "timestamp": datetime.now().isoformat()
            },
            {
                "message_id": str(uuid.uuid4()),
                "content": "回應1",
                "role": "assistant",
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        mock_chat_service.get_message_history.return_value = mock_messages
        
        response = test_client.get(
            f"/api/chat/history/{session_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["messages"]) == 2

    def test_get_message_history_with_pagination(self, test_client, auth_headers, mock_chat_service):
        """測試帶分頁的訊息歷史獲取"""
        session_id = str(uuid.uuid4())
        
        mock_chat_service.get_message_history.return_value = []
        
        response = test_client.get(
            f"/api/chat/history/{session_id}",
            params={"limit": 20, "offset": 10},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        mock_chat_service.get_message_history.assert_called_with(
            session_id=session_id,
            limit=20,
            offset=10
        )

    def test_delete_message_success(self, test_client, auth_headers, mock_chat_service):
        """測試成功刪除訊息"""
        message_id = str(uuid.uuid4())
        
        mock_chat_service.delete_message.return_value = True
        
        response = test_client.delete(
            f"/api/chat/message/{message_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 204

    def test_delete_message_not_found(self, test_client, auth_headers, mock_chat_service):
        """測試刪除不存在的訊息"""
        message_id = str(uuid.uuid4())
        
        mock_chat_service.delete_message.side_effect = Exception("訊息不存在")
        
        response = test_client.delete(
            f"/api/chat/message/{message_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    def test_search_messages_success(self, test_client, auth_headers, mock_chat_service):
        """測試成功搜尋訊息"""
        session_id = str(uuid.uuid4())
        
        mock_results = [
            {
                "message_id": str(uuid.uuid4()),
                "content": "包含關鍵字的訊息",
                "role": "user",
                "relevance_score": 0.95
            }
        ]
        
        mock_chat_service.search_messages.return_value = mock_results
        
        response = test_client.get(
            f"/api/chat/search/{session_id}",
            params={"query": "關鍵字", "limit": 10},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["results"]) == 1

    def test_export_chat_history_json(self, test_client, auth_headers, mock_chat_service):
        """測試匯出 JSON 格式的聊天記錄"""
        session_id = str(uuid.uuid4())
        
        mock_export_data = json.dumps({
            "session_id": session_id,
            "messages": [
                {"content": "測試訊息", "role": "user"}
            ]
        })
        
        mock_chat_service.export_chat_history.return_value = mock_export_data
        
        response = test_client.get(
            f"/api/chat/export/{session_id}",
            params={"format": "json"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

    def test_export_chat_history_csv(self, test_client, auth_headers, mock_chat_service):
        """測試匯出 CSV 格式的聊天記錄"""
        session_id = str(uuid.uuid4())
        
        mock_csv_data = "timestamp,role,content\n2023-01-01,user,測試訊息"
        mock_chat_service.export_chat_history.return_value = mock_csv_data
        
        response = test_client.get(
            f"/api/chat/export/{session_id}",
            params={"format": "csv"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"

    def test_stream_chat_response(self, test_client, auth_headers, mock_chat_service):
        """測試串流聊天回應"""
        # 模擬串流回應
        async def mock_stream():
            yield {"content": "第一"}
            yield {"content": "部分"}
            yield {"content": "回應"}
        
        mock_chat_service.stream_chat_response.return_value = mock_stream()
        
        response = test_client.post(
            "/api/chat/stream",
            json={"message": "測試訊息", "session_id": str(uuid.uuid4())},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream"

    def test_get_chat_statistics(self, test_client, auth_headers, mock_chat_service):
        """測試獲取聊天統計"""
        session_id = str(uuid.uuid4())
        
        mock_stats = {
            "total_messages": 100,
            "user_messages": 50,
            "assistant_messages": 50,
            "average_response_time": 1500,
            "success_rate": 0.98
        }
        
        mock_chat_service.get_chat_statistics.return_value = mock_stats
        
        response = test_client.get(
            f"/api/chat/stats/{session_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total_messages"] == 100
        assert response_data["success_rate"] == 0.98

    def test_rate_limiting(self, test_client, auth_headers):
        """測試速率限制功能"""
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "測試訊息",
            "role": "user"
        }
        
        # 快速發送多個請求
        responses = []
        for _ in range(100):  # 超過速率限制
            response = test_client.post(
                "/api/chat/message",
                json=message_data,
                headers=auth_headers
            )
            responses.append(response)
        
        # 檢查是否有請求被限制
        rate_limited = any(r.status_code == 429 for r in responses[-10:])
        # 這個測試可能需要根據實際的速率限制實作進行調整

    def test_message_validation_length(self, test_client, auth_headers):
        """測試訊息長度驗證"""
        # 測試過長的訊息
        long_message = "x" * 20000  # 超過預設限制
        
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": long_message,
            "role": "user"
        }
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # 驗證錯誤

    def test_session_validation(self, test_client, auth_headers, mock_chat_service):
        """測試會話驗證"""
        # 測試無效的會話 ID
        message_data = {
            "session_id": "invalid_session_id",
            "content": "測試訊息",
            "role": "user"
        }
        
        mock_chat_service.create_message.side_effect = Exception("會話不存在")
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400

    def test_content_filtering(self, test_client, auth_headers, mock_chat_service):
        """測試內容過濾"""
        # 測試非 BPM 相關內容
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "今天天氣真好",  # 非 BPM 相關
            "role": "user"
        }
        
        mock_chat_service.create_message.side_effect = Exception("非 BPM 相關問題")
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, test_client, auth_headers, mock_chat_service):
        """測試並發請求處理"""
        import asyncio
        
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "併發測試訊息",
            "role": "user"
        }
        
        mock_chat_service.create_message.return_value = MessageResponse(
            message_id=str(uuid.uuid4()),
            session_id=message_data["session_id"],
            content=message_data["content"],
            role="user",
            timestamp=datetime.now(),
            status="completed"
        )
        
        # 同時發送多個請求
        async def send_request():
            response = test_client.post(
                "/api/chat/message",
                json=message_data,
                headers=auth_headers
            )
            return response
        
        tasks = [send_request() for _ in range(10)]
        responses = await asyncio.gather(*tasks)
        
        # 所有請求都應該成功處理
        success_count = sum(1 for r in responses if r.status_code in [200, 201])
        assert success_count > 0

    def test_error_response_format(self, test_client, auth_headers, mock_chat_service):
        """測試錯誤回應格式"""
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "測試訊息",
            "role": "user"
        }
        
        mock_chat_service.create_message.side_effect = Exception("測試錯誤")
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        # 檢查錯誤回應格式
        assert response.status_code >= 400
        if response.content:
            error_data = response.json()
            # 根據實際的錯誤回應格式進行驗證
            assert "error" in error_data or "detail" in error_data

    def test_health_check(self, test_client):
        """測試健康檢查端點"""
        response = test_client.get("/api/chat/health")
        
        assert response.status_code == 200
        response_data = response.json()
        assert "status" in response_data

    def test_cors_headers(self, test_client, auth_headers):
        """測試 CORS 標頭"""
        response = test_client.options(
            "/api/chat/message",
            headers=auth_headers
        )
        
        # 檢查 CORS 相關標頭
        assert "Access-Control-Allow-Origin" in response.headers

    def test_request_logging(self, test_client, auth_headers, mock_chat_service):
        """測試請求日誌記錄"""
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": "測試訊息",
            "role": "user"
        }
        
        with patch('app.controllers.chat_controller.logger') as mock_logger:
            response = test_client.post(
                "/api/chat/message",
                json=message_data,
                headers=auth_headers
            )
            
            # 驗證日誌記錄被呼叫
            assert mock_logger.info.called or mock_logger.debug.called


class TestChatControllerMiddleware:
    """聊天控制器中間件測試類"""

    def test_authentication_middleware(self, test_client):
        """測試認證中間件"""
        # 沒有認證標頭的請求應該被拒絕
        response = test_client.post(
            "/api/chat/message",
            json={"content": "測試", "role": "user"}
        )
        
        assert response.status_code == 401

    def test_request_id_middleware(self, test_client, auth_headers):
        """測試請求 ID 中間件"""
        response = test_client.post(
            "/api/chat/message",
            json={"session_id": str(uuid.uuid4()), "content": "測試", "role": "user"},
            headers=auth_headers
        )
        
        # 檢查回應標頭是否包含請求 ID
        assert "X-Request-ID" in response.headers or "Request-ID" in response.headers

    def test_compression_middleware(self, test_client, auth_headers):
        """測試壓縮中間件"""
        # 發送大量資料的請求
        large_content = "x" * 5000
        
        response = test_client.post(
            "/api/chat/message",
            json={"session_id": str(uuid.uuid4()), "content": large_content, "role": "user"},
            headers={**auth_headers, "Accept-Encoding": "gzip"}
        )
        
        # 檢查是否啟用了壓縮
        if response.headers.get("Content-Encoding"):
            assert "gzip" in response.headers.get("Content-Encoding", "")


class TestChatControllerSecurity:
    """聊天控制器安全性測試類"""

    def test_sql_injection_protection(self, test_client, auth_headers):
        """測試 SQL 注入防護"""
        malicious_content = "'; DROP TABLE messages; --"
        
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": malicious_content,
            "role": "user"
        }
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        # 應該正常處理而不是崩潰
        assert response.status_code in [200, 201, 400, 422]

    def test_xss_protection(self, test_client, auth_headers):
        """測試 XSS 攻擊防護"""
        xss_content = "<script>alert('XSS')</script>"
        
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": xss_content,
            "role": "user"
        }
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        # 內容應該被適當處理或拒絕
        assert response.status_code in [200, 201, 400, 422]

    def test_request_size_limit(self, test_client, auth_headers):
        """測試請求大小限制"""
        # 發送超大請求
        huge_content = "x" * 1000000  # 1MB
        
        message_data = {
            "session_id": str(uuid.uuid4()),
            "content": huge_content,
            "role": "user"
        }
        
        response = test_client.post(
            "/api/chat/message",
            json=message_data,
            headers=auth_headers
        )
        
        # 應該被拒絕
        assert response.status_code in [413, 422]  # Payload too large or validation error