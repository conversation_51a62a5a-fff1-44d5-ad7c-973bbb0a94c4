{"version": 3, "file": "BaseOperatingContext.mjs", "sources": ["../../src/operatingcontext/BaseOperatingContext.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AAeH;;;;;;AAMG;MACmB,oBAAoB,CAAA;AAM5B,IAAA,OAAO,cAAc,CAAC,KAAe,EAAE,OAAe,EAAA;AAC5D,QAAA,QAAQ,KAAK;YACT,KAAK,QAAQ,CAAC,KAAK;;AAEf,gBAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACvB,OAAO;YACX,KAAK,QAAQ,CAAC,IAAI;;AAEd,gBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtB,OAAO;YACX,KAAK,QAAQ,CAAC,OAAO;;AAEjB,gBAAA,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACvB,OAAO;YACX,KAAK,QAAQ,CAAC,OAAO;;AAEjB,gBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtB,OAAO;AACX,YAAA;;AAEI,gBAAA,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrB,OAAO;AACd,SAAA;KACJ;AAED,IAAA,WAAA,CAAY,MAAqB,EAAA;AAC7B;;;;AAIG;AACH,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;QACxD,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAElE,QAAA,IAAI,cAAmC,CAAC;QACxC,IAAI;AACA,YAAA,cAAc,GAAG,MAAM,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;;AAEhE,SAAA;QAAC,OAAO,CAAC,EAAE,GAAE;QAEd,MAAM,WAAW,GAAG,cAAc,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,cAAc;cAC9B,OAAO,CAAC,iBAAiB,CAAC;cAC1B,WAAW,EAAE,CAAC;AAEpB,QAAA,MAAM,iBAAiB,GACnB,aAAa,KAAK,MAAM;AACpB,cAAE,IAAI;cACJ,aAAa,KAAK,OAAO;AAC3B,kBAAE,KAAK;kBACL,SAAS,CAAC;AACpB,QAAA,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;AAE9D,QAAA,MAAM,QAAQ,GACV,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;AACtD,cAAE,QAAQ,CAAC,WAAW,CAAC;cACrB,SAAS,CAAC;AACpB,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,aAAa,CAAC,cAAc,GAAG,oBAAoB,CAAC,cAAc,CAAC;AACnE,YAAA,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACrC,SAAA;QACD,IAAI,iBAAiB,KAAK,SAAS,EAAE;AACjC,YAAA,aAAa,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AACvD,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KAC1B;AAiBD;;;AAGG;IACH,SAAS,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AAED;;;AAGG;IACH,SAAS,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IAED,WAAW,GAAA;QACP,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IAED,oBAAoB,GAAA;QAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAClC;AACJ;;;;"}