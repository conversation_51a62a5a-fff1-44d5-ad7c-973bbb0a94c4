{"version": 3, "file": "BrowserUtils.mjs", "sources": ["../../src/utils/BrowserUtils.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.blockIframeReload", "BrowserAuthErrorCodes.redirectInIframe", "BrowserAuthErrorCodes.blockNestedPopups", "BrowserAuthErrorCodes.nonBrowserEnvironment", "BrowserAuthErrorCodes.uninitializedPublicClientApplication", "BrowserConfigurationAuthErrorCodes.inMemRedirectUnavailable", "BrowserCrypto.createNewGuid"], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAeH;;AAEG;AACG,SAAU,SAAS,CAAC,aAAqB,EAAA;;AAE3C,IAAA,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;IACjC,IAAI,OAAO,aAAa,CAAC,OAAO,CAAC,YAAY,KAAK,UAAU,EAAE;;AAE1D,QAAA,aAAa,CAAC,OAAO,CAAC,YAAY,CAC9B,IAAI,EACJ,EAAE,EACF,CAAA,EAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAA,EAAG,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAA,EAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAA,CAAE,CACvG,CAAC;AACL,KAAA;AACL,CAAC;AAED;;AAEG;AACG,SAAU,WAAW,CAAC,GAAW,EAAA;IACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,IAAA,QAAQ,CAAC,KAAK,EAAE,CAAC;IACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACzE,CAAC;AAED;;AAEG;SACa,UAAU,GAAA;AACtB,IAAA,OAAO,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AACpC,CAAC;AAED;;AAEG;SACa,SAAS,GAAA;AACrB,IAAA,QACI,OAAO,MAAM,KAAK,WAAW;QAC7B,CAAC,CAAC,MAAM,CAAC,MAAM;QACf,MAAM,CAAC,MAAM,KAAK,MAAM;AACxB,QAAA,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;AAC/B,QAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAG,EAAA,gBAAgB,CAAC,iBAAiB,CAAG,CAAA,CAAA,CAAC,KAAK,CAAC,EACrE;AACN,CAAC;AAED;AAEA;;AAEG;SACa,aAAa,GAAA;AACzB,IAAA,OAAO,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ;UACjD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAChD,EAAE,CAAC;AACb,CAAC;AAED;;AAEG;SACa,WAAW,GAAA;IACvB,MAAM,UAAU,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvD,IAAA,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;IACpD,OAAO,CAAA,EAAG,aAAa,CAAC,QAAQ,KAAK,aAAa,CAAC,eAAe,CAAA,CAAA,CAAG,CAAC;AAC1E,CAAC;AAED;;;AAGG;SACa,0BAA0B,GAAA;AACtC,IAAA,MAAM,cAAc,GAAG,SAAS,CAAC,2BAA2B,CACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CACvB,CAAC;;AAEF,IAAA,IAAI,cAAc,IAAI,UAAU,EAAE,EAAE;AAChC,QAAA,MAAM,sBAAsB,CAACA,iBAAuC,CAAC,CAAC;AACzE,KAAA;AACL,CAAC;AAED;;;;AAIG;AACG,SAAU,qBAAqB,CAAC,qBAA8B,EAAA;AAChE,IAAA,IAAI,UAAU,EAAE,IAAI,CAAC,qBAAqB,EAAE;;AAExC,QAAA,MAAM,sBAAsB,CAACC,gBAAsC,CAAC,CAAC;AACxE,KAAA;AACL,CAAC;AAED;;AAEG;SACa,yBAAyB,GAAA;;IAErC,IAAI,SAAS,EAAE,EAAE;AACb,QAAA,MAAM,sBAAsB,CAACC,iBAAuC,CAAC,CAAC;AACzE,KAAA;AACL,CAAC;AAED;;;AAGG;SACa,0BAA0B,GAAA;AACtC,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,QAAA,MAAM,sBAAsB,CACxBC,qBAA2C,CAC9C,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;AAGG;AACG,SAAU,6BAA6B,CAAC,WAAoB,EAAA;IAC9D,IAAI,CAAC,WAAW,EAAE;AACd,QAAA,MAAM,sBAAsB,CACxBC,oCAA0D,CAC7D,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;AAGG;AACG,SAAU,cAAc,CAAC,WAAoB,EAAA;;AAE/C,IAAA,0BAA0B,EAAE,CAAC;;AAG7B,IAAA,0BAA0B,EAAE,CAAC;;AAG7B,IAAA,yBAAyB,EAAE,CAAC;;IAG5B,6BAA6B,CAAC,WAAW,CAAC,CAAC;AAC/C,CAAC;AAED;;;;AAIG;AACa,SAAA,sBAAsB,CAClC,WAAoB,EACpB,MAA4B,EAAA;IAE5B,cAAc,CAAC,WAAW,CAAC,CAAC;AAC5B,IAAA,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;;IAE3D,IACI,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,oBAAoB,CAAC,aAAa;AACjE,QAAA,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EACtC;AACE,QAAA,MAAM,mCAAmC,CACrCC,wBAA2D,CAC9D,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;AAIG;AACG,SAAU,UAAU,CAAC,SAAiB,EAAA;IACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5C,IAAA,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC;IACxB,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;AACtC,IAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;AAGhC,IAAA,MAAM,CAAC,UAAU,CAAC,MAAK;QACnB,IAAI;AACA,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACnC,SAAA;AAAC,QAAA,MAAM,GAAE;AACd,KAAC,EAAE,KAAK,CAAC,CAAC;AACd,CAAC;AAED;;;AAGG;SACa,UAAU,GAAA;AACtB,IAAA,OAAOC,aAA2B,EAAE,CAAC;AACzC;;;;"}