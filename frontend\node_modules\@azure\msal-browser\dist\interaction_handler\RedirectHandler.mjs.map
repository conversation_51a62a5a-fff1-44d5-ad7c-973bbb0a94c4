{"version": 3, "file": "RedirectHandler.mjs", "sources": ["../../src/interaction_handler/RedirectHandler.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.emptyNavigateUri", "BrowserAuthErrorCodes.userCancelled"], "mappings": ";;;;;;;AAAA;;;AAGG;MAgCU,eAAe,CAAA;IAOxB,WACI,CAAA,cAAuC,EACvC,WAAgC,EAChC,eAA+C,EAC/C,MAAc,EACd,iBAAqC,EAAA;AAErC,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;AACjC,QAAA,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;AAClC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AACvC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;AAGG;AACH,IAAA,MAAM,mBAAmB,CACrB,UAAkB,EAClB,MAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;;AAElE,QAAA,IAAI,UAAU,EAAE;;YAEZ,IAAI,MAAM,CAAC,iBAAiB,EAAE;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gFAAgF,CACnF,CAAC;AACF,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,UAAU,EAC7B,MAAM,CAAC,iBAAiB,EACxB,IAAI,CACP,CAAC;AACL,aAAA;;AAGD,YAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,cAAc,EACjC,IAAI,CAAC,eAAe,CAAC,aAAa,EAClC,IAAI,CACP,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAqD,kDAAA,EAAA,UAAU,CAAE,CAAA,CACpE,CAAC;AACF,YAAA,MAAM,iBAAiB,GAAsB;gBACzC,KAAK,EAAE,KAAK,CAAC,oBAAoB;gBACjC,OAAO,EAAE,MAAM,CAAC,eAAe;AAC/B,gBAAA,SAAS,EAAE,KAAK;aACnB,CAAC;;AAGF,YAAA,IAAI,OAAO,MAAM,CAAC,kBAAkB,KAAK,UAAU,EAAE;AACjD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2EAA2E,CAC9E,CAAC;gBACF,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;;gBAGvD,IAAI,QAAQ,KAAK,KAAK,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0FAA0F,CAC7F,CAAC;oBACF,MAAM,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAC1C,UAAU,EACV,iBAAiB,CACpB,CAAC;oBACF,OAAO;AACV,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,6FAA6F,CAChG,CAAC;oBACF,OAAO;AACV,iBAAA;AACJ,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wEAAwE,CAC3E,CAAC;gBACF,MAAM,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAC1C,UAAU,EACV,iBAAiB,CACpB,CAAC;gBACF,OAAO;AACV,aAAA;AACJ,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,4DAA4D,CAC/D,CAAC;AACF,YAAA,MAAM,sBAAsB,CACxBA,gBAAsC,CACzC,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,MAAM,kBAAkB,CACpB,QAAyC,EACzC,KAAa,EAAA;AAEb,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;;AAGjE,QAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;;QAGpD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,aAAa,EAClC,cAAc,CACjB,CAAC;AACL,SAAA;AAED,QAAA,IAAI,gBAAgB,CAAC;QACrB,IAAI;YACA,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,sBAAsB,CACrD,QAAQ,EACR,YAAY,CACf,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IACI,CAAC,YAAY,WAAW;AACxB,gBAAA,CAAC,CAAC,QAAQ,KAAKC,aAAmC,EACpD;;AAEE,gBAAA,MAAM,sBAAsB,CACxBA,aAAmC,CACtC,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,CAAC,CAAC;AACX,aAAA;AACJ,SAAA;;QAGD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;;QAGpE,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;;QAGlD,IAAI,gBAAgB,CAAC,wBAAwB,EAAE;YAC3C,MAAM,WAAW,CACb,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EACrD,iBAAiB,CAAC,4BAA4B,EAC9C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,eAAe,CAAC,aAAa,CACrC,CACG,gBAAgB,CAAC,wBAAwB,EACzC,IAAI,CAAC,eAAe,CAAC,aAAa,CACrC,CAAC;AACL,SAAA;AAED,QAAA,gBAAgB,CAAC,KAAK,GAAG,WAAW,IAAI,SAAS,CAAC;AAClD,QAAA,gBAAgB,CAAC,KAAK,GAAG,YAAY,CAAC;;QAGtC,IAAI,gBAAgB,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAClE,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACjD,YAAA,IAAI,aAAa,EAAE;AACf,gBAAA,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,aAAa,CAAC;AACtD,aAAA;AACJ,SAAA;;AAGD,QAAA,MAAM,aAAa,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CACrD,IAAI,CAAC,eAAe,EACpB,gBAAgB,CACnB,CAAyB,CAAC;AAE3B,QAAA,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC/C,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;AAEG;IACO,mBAAmB,GAAA;;AAEzB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACvD,kBAAkB,CAAC,cAAc,EACjC,IAAI,CACP,CAAC;AACF,QAAA,IAAI,aAAa,EAAE;YACf,IAAI;AACA,gBAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAkB,CAAC;AACrD,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CACxB,sCAAsC,CACzC,CAAC;gBACF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAC3B,CAAyC,sCAAA,EAAA,aAAa,CAAE,CAAA,CAC3D,CAAC;AACL,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AACJ;;;;"}