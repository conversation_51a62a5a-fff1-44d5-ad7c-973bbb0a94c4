{"version": 3, "file": "BridgeProxy.mjs", "sources": ["../../src/naa/BridgeProxy.ts"], "sourcesContent": [null], "names": ["BrowserCrypto.createNewGuid"], "mappings": ";;;;;;;AAAA;;;AAGG;AA2BH;;;;AAIG;MACU,WAAW,CAAA;AAOpB;;;;;AAKG;IACO,aAAa,6BAA6B,GAAA;QAChD,IAAI,MAAM,KAAK,SAAS,EAAE;AACtB,YAAA,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC1C,SAAA;AACD,QAAA,IAAI,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE;AAC1C,YAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC9D,SAAA;QAED,IAAI;YACA,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,CACvC,SAAS,EACT,CAAC,QAA4B,KAAI;AAC7B,gBAAA,MAAM,eAAe,GACjB,OAAO,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC5D,MAAM,gBAAgB,GAClB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAChC,MAAM,OAAO,GAAG,WAAW,CAAC,cAAc,CAAC,IAAI,CAC3C,CAAC,OAAO,KACJ,OAAO,CAAC,SAAS,KAAK,gBAAgB,CAAC,SAAS,CACvD,CAAC;gBACF,IAAI,OAAO,KAAK,SAAS,EAAE;AACvB,oBAAA,WAAW,CAAC,cAAc,CAAC,MAAM,CAC7B,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAC3C,CAAC,CACJ,CAAC;oBACF,IAAI,gBAAgB,CAAC,OAAO,EAAE;AAC1B,wBAAA,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACrC,qBAAA;AAAM,yBAAA;AACH,wBAAA,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC1C,qBAAA;AACJ,iBAAA;AACL,aAAC,CACJ,CAAC;YAEF,MAAM,cAAc,GAAG,MAAM,IAAI,OAAO,CACpC,CAAC,OAAO,EAAE,MAAM,KAAI;gBAChB,MAAM,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AAE3D,gBAAA,MAAM,OAAO,GAAkB;oBAC3B,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,oBAAA,OAAO,EAAE,OAAO;AAChB,oBAAA,MAAM,EAAE,MAAM;iBACjB,CAAC;AACF,gBAAA,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,gBAAA,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAClC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAC1B,CAAC;AACN,aAAC,CACJ,CAAC;YAEF,OAAO,WAAW,CAAC,2BAA2B,CAC1C,cAAc,CAAC,WAAW,CAC7B,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACZ,YAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ;AAED;;;;AAIG;AACI,IAAA,mBAAmB,CAAC,OAAqB,EAAA;QAC5C,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;KAClD;AAED;;;;AAIG;AACI,IAAA,cAAc,CAAC,OAAqB,EAAA;QACvC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;KAC7C;AAEO,IAAA,MAAM,QAAQ,CAClB,WAA0B,EAC1B,OAAqB,EAAA;QAErB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;AAC/C,YAAA,WAAW,EAAE,OAAO;AACvB,SAAA,CAAC,CAAC;QACH,OAAO;YACH,KAAK,EAAE,WAAW,CAAC,2BAA2B,CAAC,MAAM,CAAC,KAAK,CAAC;YAC5D,OAAO,EAAE,WAAW,CAAC,2BAA2B,CAAC,MAAM,CAAC,OAAO,CAAC;SACnE,CAAC;KACL;IAEM,mBAAmB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;KACpC;IAEM,iBAAiB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC3D;AAEO,IAAA,OAAO,YAAY,CACvB,MAAqB,EACrB,aAA8C,EAAA;QAE9C,OAAO;AACH,YAAA,WAAW,EAAE,sBAAsB;AACnC,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,SAAS,EAAEA,aAA2B,EAAE;AACxC,YAAA,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;YACpB,aAAa,EAAE,gBAAgB,CAAC,QAAQ;AACxC,YAAA,oBAAoB,EAAE,OAAO;AAC7B,YAAA,GAAG,aAAa;SACnB,CAAC;KACL;AAED;;;;AAIG;IACK,WAAW,CACf,MAAqB,EACrB,aAA8C,EAAA;QAE9C,MAAM,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAEhE,MAAM,OAAO,GAAG,IAAI,OAAO,CACvB,CAAC,OAAO,EAAE,MAAM,KAAI;AAChB,YAAA,MAAM,OAAO,GAAkB;gBAC3B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,gBAAA,OAAO,EAAE,OAAO;AAChB,gBAAA,MAAM,EAAE,MAAM;aACjB,CAAC;AACF,YAAA,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,YAAA,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACpE,SAAC,CACJ,CAAC;AAEF,QAAA,OAAO,OAAO,CAAC;KAClB;IAEO,OAAO,2BAA2B,CAAI,KAAoB,EAAA;QAC9D,IAAI,KAAK,KAAK,SAAS,EAAE;AACrB,YAAA,MAAM,WAAW,GAAgB;gBAC7B,MAAM,EAAE,gBAAgB,CAAC,wBAAwB;aACpD,CAAC;AACF,YAAA,MAAM,WAAW,CAAC;AACrB,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED;;;;;AAKG;AACH,IAAA,WAAA,CACI,OAAe,EACf,UAAkB,EAClB,cAA+B,EAC/B,YAAiC,EAAA;AAEjC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACrC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC;AAED;;;AAGG;IACI,aAAa,MAAM,GAAA;AACtB,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,6BAA6B,EAAE,CAAC;AACnE,QAAA,OAAO,IAAI,WAAW,CAClB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,UAAU,EACnB,QAAQ,CAAC,cAAc,EACvB,QAAQ,CAAC,YAAY,CACxB,CAAC;KACL;;AAjMM,WAAc,CAAA,cAAA,GAAoB,EAAE;;;;"}