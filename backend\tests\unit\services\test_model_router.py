"""
模型路由服務單元測試
測試 ModelRouter 的負載平衡、健康檢查和模型路由功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from datetime import datetime, timedelta
import uuid

from app.services.model_router import (
    ModelRouter, ModelType, ModelStatus, LoadBalanceStrategy,
    ModelConfig, ModelHealth, ModelMetrics, ModelRouterError
)


class TestModelRouter:
    """模型路由器測試類"""

    @pytest.fixture
    def model_router(self, mock_model_config, mock_redis):
        """創建模型路由器實例"""
        with patch('app.services.model_router.get_redis_client', return_value=mock_redis):
            router = ModelRouter()
            
            # 設定模型配置
            for model_type, config in mock_model_config.items():
                router.register_model(model_type, config)
            
            return router

    @pytest.fixture
    def mock_ai_clients(self, mock_openai_client, mock_claude_client, mock_gemini_client):
        """模擬 AI 客戶端字典"""
        return {
            ModelType.CHATGPT: mock_openai_client,
            ModelType.CLAUDE: mock_claude_client,
            ModelType.GEMINI: mock_gemini_client
        }

    def test_register_model(self, model_router, mock_model_config):
        """測試註冊模型"""
        config = mock_model_config[ModelType.CHATGPT]
        client = AsyncMock()
        
        model_router.register_client(ModelType.CHATGPT, client)
        
        assert ModelType.CHATGPT in model_router._clients
        assert model_router._clients[ModelType.CHATGPT] == client

    def test_register_duplicate_model(self, model_router):
        """測試註冊重複模型"""
        client1 = AsyncMock()
        client2 = AsyncMock()
        
        model_router.register_client(ModelType.CHATGPT, client1)
        
        # 註冊相同模型應該覆蓋
        model_router.register_client(ModelType.CHATGPT, client2)
        
        assert model_router._clients[ModelType.CHATGPT] == client2

    @pytest.mark.asyncio
    async def test_get_available_models(self, model_router, mock_ai_clients):
        """測試獲取可用模型列表"""
        # 註冊客戶端
        for model_type, client in mock_ai_clients.items():
            model_router.register_client(model_type, client)
        
        available_models = await model_router.get_available_models()
        
        # ChatGPT 和 Claude 應該可用，Gemini 不可用（根據 fixture 設定）
        assert ModelType.CHATGPT in [m.model_type for m in available_models]
        assert ModelType.CLAUDE in [m.model_type for m in available_models]

    @pytest.mark.asyncio
    async def test_select_model_round_robin(self, model_router, mock_ai_clients):
        """測試輪詢負載平衡策略"""
        # 註冊健康的客戶端
        for model_type in [ModelType.CHATGPT, ModelType.CLAUDE]:
            model_router.register_client(model_type, mock_ai_clients[model_type])
        
        model_router.set_load_balance_strategy(LoadBalanceStrategy.ROUND_ROBIN)
        
        # 連續選擇模型，應該輪詢
        selected_models = []
        for _ in range(4):
            model = await model_router.select_model()
            selected_models.append(model.model_type)
        
        # 驗證輪詢行為
        assert len(set(selected_models)) > 1  # 應該選擇不同的模型

    @pytest.mark.asyncio
    async def test_select_model_weighted(self, model_router, mock_ai_clients):
        """測試加權輪詢負載平衡策略"""
        for model_type in [ModelType.CHATGPT, ModelType.CLAUDE]:
            model_router.register_client(model_type, mock_ai_clients[model_type])
        
        model_router.set_load_balance_strategy(LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN)
        
        # 多次選擇，統計分布
        model_counts = {}
        for _ in range(100):
            model = await model_router.select_model()
            model_counts[model.model_type] = model_counts.get(model.model_type, 0) + 1
        
        # ChatGPT (weight=1.0) 應該被選擇更多次於 Claude (weight=0.8)
        if ModelType.CHATGPT in model_counts and ModelType.CLAUDE in model_counts:
            assert model_counts[ModelType.CHATGPT] >= model_counts[ModelType.CLAUDE]

    @pytest.mark.asyncio
    async def test_select_model_health_based(self, model_router, mock_ai_clients, mock_model_health):
        """測試基於健康狀態的模型選擇"""
        for model_type in [ModelType.CHATGPT, ModelType.CLAUDE]:
            model_router.register_client(model_type, mock_ai_clients[model_type])
        
        # 設定健康狀態
        with patch.object(model_router, '_get_model_health') as mock_health:
            mock_health.side_effect = lambda mt: mock_model_health[mt]
            
            model_router.set_load_balance_strategy(LoadBalanceStrategy.HEALTH_BASED)
            
            selected_model = await model_router.select_model()
            
            # 應該選擇更健康的模型
            assert selected_model is not None

    @pytest.mark.asyncio
    async def test_select_model_no_healthy_models(self, model_router):
        """測試沒有健康模型時的選擇"""
        # 不註冊任何健康的客戶端
        
        with pytest.raises(ModelRouterError) as exc_info:
            await model_router.select_model()
        
        assert "沒有可用的模型" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_route_request(self, model_router, mock_ai_clients):
        """測試路由請求到特定模型"""
        client = mock_ai_clients[ModelType.CHATGPT]
        model_router.register_client(ModelType.CHATGPT, client)
        
        request = {
            "messages": [{"role": "user", "content": "測試訊息"}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        response = await model_router.route_request(ModelType.CHATGPT, request)
        
        assert response is not None
        client.chat_completion.assert_called_once()

    @pytest.mark.asyncio
    async def test_route_request_model_not_found(self, model_router):
        """測試路由到不存在的模型"""
        request = {"messages": [{"role": "user", "content": "測試"}]}
        
        with pytest.raises(ModelRouterError) as exc_info:
            await model_router.route_request(ModelType.CHATGPT, request)
        
        assert "模型客戶端未註冊" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_health_check_all_models(self, model_router, mock_ai_clients):
        """測試所有模型的健康檢查"""
        for model_type, client in mock_ai_clients.items():
            model_router.register_client(model_type, client)
        
        health_results = await model_router.health_check_all()
        
        assert len(health_results) == len(mock_ai_clients)
        
        # 驗證健康檢查結果格式
        for model_type, health in health_results.items():
            assert "status" in health
            assert "response_time" in health

    @pytest.mark.asyncio
    async def test_health_check_single_model(self, model_router, mock_openai_client):
        """測試單一模型健康檢查"""
        model_router.register_client(ModelType.CHATGPT, mock_openai_client)
        
        health = await model_router.health_check(ModelType.CHATGPT)
        
        assert health is not None
        assert health.status == "healthy"
        assert health.response_time_ms > 0

    @pytest.mark.asyncio
    async def test_health_check_unhealthy_model(self, model_router, mock_gemini_client):
        """測試不健康模型的健康檢查"""
        model_router.register_client(ModelType.GEMINI, mock_gemini_client)
        
        health = await model_router.health_check(ModelType.GEMINI)
        
        assert health.status == "unhealthy"

    @pytest.mark.asyncio
    async def test_get_model_metrics(self, model_router, mock_redis):
        """測試獲取模型使用統計"""
        # 模擬統計資料
        metrics_data = {
            "requests_count": "150",
            "success_count": "147",
            "error_count": "3",
            "total_response_time": "180000",
            "average_response_time": "1200"
        }
        
        mock_redis.hgetall.return_value = {
            k.encode(): v.encode() for k, v in metrics_data.items()
        }
        
        metrics = await model_router.get_model_metrics(ModelType.CHATGPT)
        
        assert metrics.requests_count == 150
        assert metrics.success_count == 147
        assert metrics.error_count == 3
        assert metrics.average_response_time == 1200

    @pytest.mark.asyncio
    async def test_update_model_metrics(self, model_router, mock_redis):
        """測試更新模型統計"""
        mock_redis.hincrby.return_value = 1
        mock_redis.hincrbyfloat.return_value = 1500.0
        
        await model_router.update_model_metrics(
            ModelType.CHATGPT,
            success=True,
            response_time=1200,
            tokens_used=50
        )
        
        # 驗證 Redis 更新呼叫
        assert mock_redis.hincrby.called
        assert mock_redis.hincrbyfloat.called

    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, model_router, mock_openai_client):
        """測試斷路器功能"""
        model_router.register_client(ModelType.CHATGPT, mock_openai_client)
        
        # 模擬連續失敗
        mock_openai_client.chat_completion.side_effect = Exception("連線失敗")
        
        # 觸發斷路器
        for _ in range(5):  # 假設閾值為 5 次失敗
            try:
                await model_router.route_request(
                    ModelType.CHATGPT,
                    {"messages": [{"role": "user", "content": "測試"}]}
                )
            except:
                pass
        
        # 檢查模型狀態應該變為不可用
        health = await model_router.health_check(ModelType.CHATGPT)
        # 根據實際實作，可能需要調整這個斷言
        assert health.error_count >= 5

    @pytest.mark.asyncio
    async def test_model_failover(self, model_router, mock_ai_clients):
        """測試模型容錯轉移"""
        # 註冊多個模型
        for model_type in [ModelType.CHATGPT, ModelType.CLAUDE]:
            model_router.register_client(model_type, mock_ai_clients[model_type])
        
        # 讓 ChatGPT 失敗
        mock_ai_clients[ModelType.CHATGPT].chat_completion.side_effect = Exception("服務不可用")
        
        # 設定容錯轉移策略
        model_router.enable_failover = True
        
        request = {"messages": [{"role": "user", "content": "測試"}]}
        
        # 應該自動轉移到 Claude
        response = await model_router.route_request_with_failover(request)
        
        assert response is not None
        # Claude 應該被呼叫
        mock_ai_clients[ModelType.CLAUDE].chat_completion.assert_called()

    @pytest.mark.asyncio
    async def test_rate_limiting(self, model_router, mock_openai_client):
        """測試速率限制功能"""
        model_router.register_client(ModelType.CHATGPT, mock_openai_client)
        
        # 模擬快速連續請求
        request = {"messages": [{"role": "user", "content": "測試"}]}
        
        # 設定較低的速率限制進行測試
        with patch.object(model_router, '_check_rate_limit') as mock_rate_limit:
            mock_rate_limit.side_effect = [True, True, False]  # 第三次請求被限制
            
            # 前兩次應該成功
            await model_router.route_request(ModelType.CHATGPT, request)
            await model_router.route_request(ModelType.CHATGPT, request)
            
            # 第三次應該被限制
            with pytest.raises(ModelRouterError) as exc_info:
                await model_router.route_request(ModelType.CHATGPT, request)
            
            assert "速率限制" in str(exc_info.value) or "請求過於頻繁" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_concurrent_requests_limit(self, model_router, mock_openai_client):
        """測試並發請求限制"""
        model_router.register_client(ModelType.CHATGPT, mock_openai_client)
        
        # 模擬長時間運行的請求
        async def long_running_request(*args, **kwargs):
            await asyncio.sleep(0.1)
            return {"content": "回應"}
        
        mock_openai_client.chat_completion.side_effect = long_running_request
        
        request = {"messages": [{"role": "user", "content": "測試"}]}
        
        # 發送多個並發請求
        tasks = [
            model_router.route_request(ModelType.CHATGPT, request)
            for _ in range(15)  # 超過預設並發限制 10
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 部分請求應該因並發限制而失敗
        errors = [r for r in results if isinstance(r, Exception)]
        successes = [r for r in results if not isinstance(r, Exception)]
        
        # 應該有成功和失敗的請求
        assert len(successes) > 0
        # 並發限制可能導致部分請求失敗或延遲

    @pytest.mark.asyncio
    async def test_model_configuration_update(self, model_router, mock_model_config):
        """測試動態更新模型配置"""
        original_config = mock_model_config[ModelType.CHATGPT]
        
        # 更新配置
        new_config = ModelConfig(
            model_type=ModelType.CHATGPT,
            enabled=True,
            weight=2.0,  # 改變權重
            max_requests_per_minute=120,  # 增加速率限制
            timeout_seconds=60  # 增加超時時間
        )
        
        await model_router.update_model_config(ModelType.CHATGPT, new_config)
        
        updated_config = model_router.get_model_config(ModelType.CHATGPT)
        assert updated_config.weight == 2.0
        assert updated_config.max_requests_per_minute == 120

    def test_load_balance_strategy_setting(self, model_router):
        """測試設定負載平衡策略"""
        strategies = [
            LoadBalanceStrategy.ROUND_ROBIN,
            LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN,
            LoadBalanceStrategy.RANDOM,
            LoadBalanceStrategy.HEALTH_BASED
        ]
        
        for strategy in strategies:
            model_router.set_load_balance_strategy(strategy)
            assert model_router.load_balance_strategy == strategy

    @pytest.mark.asyncio
    async def test_model_warmup(self, model_router, mock_ai_clients):
        """測試模型預熱功能"""
        for model_type, client in mock_ai_clients.items():
            model_router.register_client(model_type, client)
        
        # 執行模型預熱
        warmup_results = await model_router.warmup_models()
        
        # 所有健康的模型都應該被預熱
        assert len(warmup_results) > 0
        
        # 檢查每個模型都被呼叫了預熱請求
        for model_type, client in mock_ai_clients.items():
            if client.is_healthy.return_value:
                # 健康的模型應該接收到預熱請求
                assert client.chat_completion.called

    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_performance_load_balancing(self, model_router, mock_ai_clients):
        """測試負載平衡的性能"""
        # 註冊多個客戶端
        for model_type in [ModelType.CHATGPT, ModelType.CLAUDE]:
            model_router.register_client(model_type, mock_ai_clients[model_type])
        
        # 大量並發請求
        request = {"messages": [{"role": "user", "content": "性能測試"}]}
        
        start_time = datetime.now()
        
        # 執行大量請求
        tasks = [
            model_router.select_model()
            for _ in range(1000)
        ]
        
        results = await asyncio.gather(*tasks)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 驗證性能要求
        assert len(results) == 1000
        assert processing_time < 10.0  # 10秒內完成 1000 次模型選擇


class TestModelHealth:
    """模型健康狀態測試類"""

    def test_model_health_creation(self):
        """測試創建模型健康狀態物件"""
        health = ModelHealth(
            status=ModelStatus.HEALTHY,
            response_time_ms=1200,
            success_rate=0.98,
            concurrent_requests=3,
            error_count=1,
            last_check=datetime.now()
        )
        
        assert health.status == ModelStatus.HEALTHY
        assert health.response_time_ms == 1200
        assert health.success_rate == 0.98

    def test_model_health_score_calculation(self):
        """測試健康分數計算"""
        health = ModelHealth(
            status=ModelStatus.HEALTHY,
            response_time_ms=1200,
            success_rate=0.98,
            concurrent_requests=3,
            error_count=1
        )
        
        score = health.calculate_health_score()
        assert 0 <= score <= 1.0
        assert score > 0.9  # 健康模型應該有高分數


class TestModelRouterError:
    """模型路由器錯誤測試類"""

    def test_model_router_error_creation(self):
        """測試創建模型路由器錯誤物件"""
        error = ModelRouterError("測試錯誤", "ROUTER_ERROR")
        
        assert str(error) == "測試錯誤"
        assert error.error_code == "ROUTER_ERROR"