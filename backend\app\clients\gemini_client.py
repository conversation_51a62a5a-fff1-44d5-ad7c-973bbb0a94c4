"""
Gemini 客戶端
實作 Google Gemini API 整合，包含錯誤處理和重試機制
"""

import os
import logging
import asyncio
import json
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold, GenerationConfig
from google.api_core import exceptions as google_exceptions
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# 設定日誌
logger = logging.getLogger(__name__)

class GeminiConfig:
    """Gemini 配置類"""
    
    def __init__(self):
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model = os.getenv("GEMINI_MODEL", "gemini-pro")
        self.max_tokens = int(os.getenv("GEMINI_MAX_TOKENS", "2000"))
        self.temperature = float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
        self.top_p = float(os.getenv("GEMINI_TOP_P", "1.0"))
        self.top_k = int(os.getenv("GEMINI_TOP_K", "40"))
        
        # 請求配置
        self.timeout = float(os.getenv("GEMINI_TIMEOUT", "30.0"))
        self.max_retries = int(os.getenv("GEMINI_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("GEMINI_RETRY_DELAY", "1.0"))
        
        # 速率限制
        self.max_requests_per_minute = int(os.getenv("GEMINI_MAX_RPM", "60"))
        self.max_tokens_per_minute = int(os.getenv("GEMINI_MAX_TPM", "32000"))
        
        # 安全設定
        self.enable_safety_filters = os.getenv("GEMINI_ENABLE_SAFETY", "true").lower() == "true"
        
        # 驗證 API Key
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY 環境變數未設定")
        
        # 配置 Gemini
        genai.configure(api_key=self.api_key)
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "top_k": self.top_k,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "enable_safety_filters": self.enable_safety_filters
        }

class GeminiError(Exception):
    """Gemini 客戶端錯誤"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code

class GeminiClient:
    """Gemini 客戶端類"""
    
    def __init__(self, config: Optional[GeminiConfig] = None):
        self.config = config or GeminiConfig()
        
        # 初始化模型
        self.model = genai.GenerativeModel(
            model_name=self.config.model,
            generation_config=GenerationConfig(
                max_output_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                top_k=self.config.top_k
            ),
            safety_settings=self._get_safety_settings() if self.config.enable_safety_filters else None
        )
        
        # 統計資料
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "last_request_time": None
        }
        
        logger.info(f"Gemini 客戶端初始化完成，模型: {self.config.model}")
    
    def _get_safety_settings(self) -> Dict[HarmCategory, HarmBlockThreshold]:
        """獲取安全設定"""
        return {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        }
    
    async def health_check(self) -> bool:
        """健康檢查"""
        try:
            # 發送簡單的測試請求
            start_time = datetime.utcnow()
            
            response = await asyncio.to_thread(
                self.model.generate_content, 
                "Hello",
                generation_config=GenerationConfig(max_output_tokens=10, temperature=0)
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 更新統計
            self.stats["last_request_time"] = end_time
            
            logger.debug(f"Gemini 健康檢查成功，回應時間: {response_time:.1f}ms")
            return True
            
        except Exception as e:
            logger.warning(f"Gemini 健康檢查失敗: {e}")
            return False
    
    @retry(
        retry=retry_if_exception_type((google_exceptions.ResourceExhausted, google_exceptions.DeadlineExceeded, google_exceptions.InternalServerError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成回應
        
        Args:
            messages: 對話訊息列表
            user_id: 用戶 ID
            session_id: 會話 ID
            **kwargs: 其他參數
        
        Returns:
            回應結果字典
        """
        start_time = datetime.utcnow()
        self.stats["requests_count"] += 1
        
        try:
            # 轉換訊息格式為單一提示
            prompt = self._convert_messages_to_prompt(messages)
            
            # 準備生成配置
            generation_config = GenerationConfig(
                max_output_tokens=kwargs.get("max_tokens", self.config.max_tokens),
                temperature=kwargs.get("temperature", self.config.temperature),
                top_p=kwargs.get("top_p", self.config.top_p),
                top_k=kwargs.get("top_k", self.config.top_k)
            )
            
            # 如果啟用串流模式
            if kwargs.get("stream", False):
                return await self._generate_stream_response(prompt, generation_config, start_time)
            
            # 發送請求
            logger.debug(f"發送 Gemini 請求，用戶: {user_id}, 會話: {session_id}")
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=generation_config
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 檢查回應是否被安全過濾器阻擋
            if not response.candidates:
                raise GeminiError("回應被安全過濾器阻擋", "safety_filtered", 400)
            
            # 解析回應
            candidate = response.candidates[0]
            content = candidate.content.parts[0].text if candidate.content.parts else ""
            finish_reason = candidate.finish_reason.name if candidate.finish_reason else "STOP"
            
            # 統計資訊（Gemini 不提供詳細的 token 使用統計）
            prompt_tokens = self._estimate_tokens(prompt)
            completion_tokens = self._estimate_tokens(content)
            total_tokens = prompt_tokens + completion_tokens
            
            # 計算成本
            cost = self._calculate_cost(prompt_tokens, completion_tokens, self.config.model)
            
            # 更新統計
            self._update_stats(True, response_time, total_tokens, cost)
            
            result = {
                "content": content,
                "finish_reason": finish_reason,
                "model": self.config.model,
                "usage": {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens
                },
                "response_time_ms": response_time,
                "cost_usd": cost,
                "timestamp": end_time.isoformat(),
                "safety_ratings": self._extract_safety_ratings(candidate),
                "metadata": {
                    "user_id": user_id,
                    "session_id": session_id,
                    "request_params": self._sanitize_params({
                        "model": self.config.model,
                        "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                        "generation_config": generation_config._asdict()
                    })
                }
            }
            
            logger.debug(f"Gemini 回應成功，Token: {total_tokens}, 時間: {response_time:.1f}ms, 成本: ${cost:.4f}")
            return result
            
        except google_exceptions.ResourceExhausted as e:
            error_msg = f"Gemini 配額耗盡: {e}"
            logger.warning(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "quota_exceeded", 429)
            
        except google_exceptions.DeadlineExceeded as e:
            error_msg = f"Gemini 請求超時: {e}"
            logger.warning(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "timeout", 408)
            
        except google_exceptions.InvalidArgument as e:
            error_msg = f"Gemini 請求無效: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "invalid_request", 400)
            
        except google_exceptions.Unauthenticated as e:
            error_msg = f"Gemini 認證失敗: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "authentication_failed", 401)
            
        except google_exceptions.PermissionDenied as e:
            error_msg = f"Gemini 權限被拒: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "permission_denied", 403)
            
        except google_exceptions.NotFound as e:
            error_msg = f"Gemini 資源未找到: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "not_found", 404)
            
        except google_exceptions.InternalServerError as e:
            error_msg = f"Gemini 伺服器錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "internal_server_error", 500)
            
        except Exception as e:
            error_msg = f"Gemini 未知錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "unknown_error", 500)
    
    async def _generate_stream_response(
        self, 
        prompt: str,
        generation_config: GenerationConfig,
        start_time: datetime
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成串流回應"""
        try:
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=generation_config,
                stream=True
            )
            
            for chunk in response:
                if chunk.candidates and chunk.candidates[0].content.parts:
                    content = chunk.candidates[0].content.parts[0].text
                    finish_reason = chunk.candidates[0].finish_reason
                    
                    yield {
                        "content": content,
                        "finish_reason": finish_reason.name if finish_reason else None,
                        "is_final": finish_reason is not None
                    }
            
            # 最終統計更新
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            self._update_stats(True, response_time, 0, 0.0)  # 串流模式無法精確計算 token
            
        except Exception as e:
            error_msg = f"Gemini 串流錯誤: {e}"
            logger.error(error_msg)
            self._update_stats(False, (datetime.utcnow() - start_time).total_seconds() * 1000)
            raise GeminiError(error_msg, "stream_error", 500)
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """轉換訊息列表為單一提示"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"系統指示: {content}")
            elif role == "user":
                prompt_parts.append(f"用戶: {content}")
            elif role == "assistant":
                prompt_parts.append(f"助手: {content}")
        
        return "\n\n".join(prompt_parts)
    
    def _estimate_tokens(self, text: str) -> int:
        """估算 token 數量（粗略估算）"""
        # 粗略估算：中文字符約 1 token，英文單詞約 1.3 token
        chinese_chars = len([c for c in text if ord(c) > 127])
        english_words = len(text.replace(" ", "").replace(chr(c) for c in range(128, 65536))) // 4
        
        return chinese_chars + int(english_words * 1.3)
    
    def _extract_safety_ratings(self, candidate) -> List[Dict[str, Any]]:
        """提取安全評級"""
        safety_ratings = []
        
        if hasattr(candidate, 'safety_ratings') and candidate.safety_ratings:
            for rating in candidate.safety_ratings:
                safety_ratings.append({
                    "category": rating.category.name,
                    "probability": rating.probability.name
                })
        
        return safety_ratings
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int, model: str) -> float:
        """計算成本（基於 2024 年定價）"""
        # Gemini 定價（每 1K tokens）
        pricing = {
            "gemini-pro": {"input": 0.0005, "output": 0.0015},
            "gemini-pro-vision": {"input": 0.0025, "output": 0.0075},
            "gemini-ultra": {"input": 0.01, "output": 0.03}  # 預估價格
        }
        
        # 預設使用 Gemini Pro 定價
        model_pricing = pricing.get(model, pricing["gemini-pro"])
        
        input_cost = (input_tokens / 1000) * model_pricing["input"]
        output_cost = (output_tokens / 1000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def _update_stats(self, success: bool, response_time: float, tokens: int = 0, cost: float = 0.0):
        """更新統計資料"""
        if success:
            self.stats["success_count"] += 1
            self.stats["total_tokens"] += tokens
            self.stats["total_cost"] += cost
        else:
            self.stats["error_count"] += 1
        
        # 更新平均回應時間
        total_requests = self.stats["success_count"] + self.stats["error_count"]
        if total_requests > 0:
            current_avg = self.stats["average_response_time"]
            self.stats["average_response_time"] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
    
    def _sanitize_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """清理參數（移除敏感資訊）"""
        sanitized = params.copy()
        
        # 截斷過長的提示內容
        if "prompt" in sanitized and len(sanitized["prompt"]) > 100:
            sanitized["prompt"] = sanitized["prompt"][:100] + "..."
        
        return sanitized
    
    async def analyze_image(self, image_data: bytes, prompt: str = "描述這張圖片") -> Dict[str, Any]:
        """圖片分析（需要 Gemini Pro Vision）"""
        try:
            start_time = datetime.utcnow()
            
            # 檢查是否支援視覺功能
            if "vision" not in self.config.model:
                raise GeminiError("當前模型不支援圖片分析", "unsupported_feature", 400)
            
            # 創建視覺模型
            vision_model = genai.GenerativeModel(
                model_name="gemini-pro-vision"
            )
            
            # 準備圖片部分
            image_part = {
                "mime_type": "image/jpeg",  # 假設是 JPEG 格式
                "data": image_data
            }
            
            response = await asyncio.to_thread(
                vision_model.generate_content,
                [prompt, image_part]
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 解析回應
            content = response.candidates[0].content.parts[0].text if response.candidates else ""
            
            return {
                "description": content,
                "model": "gemini-pro-vision",
                "response_time_ms": response_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"Gemini 圖片分析失敗: {e}"
            logger.error(error_msg)
            raise GeminiError(error_msg, "image_analysis_error", 500)
    
    async def count_tokens(self, text: str) -> Dict[str, Any]:
        """計算 token 數量"""
        try:
            start_time = datetime.utcnow()
            
            # 使用 Gemini 的 token 計算功能
            token_count = await asyncio.to_thread(
                self.model.count_tokens,
                text
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            return {
                "token_count": token_count.total_tokens,
                "text_length": len(text),
                "response_time_ms": response_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            # 如果 API 不支援，使用估算方法
            estimated_tokens = self._estimate_tokens(text)
            
            return {
                "token_count": estimated_tokens,
                "text_length": len(text),
                "estimated": True,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取統計資料"""
        success_rate = 0.0
        if self.stats["requests_count"] > 0:
            success_rate = self.stats["success_count"] / self.stats["requests_count"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "error_rate": 1.0 - success_rate,
            "average_tokens_per_request": (
                self.stats["total_tokens"] / self.stats["success_count"]
                if self.stats["success_count"] > 0 else 0
            ),
            "average_cost_per_request": (
                self.stats["total_cost"] / self.stats["success_count"]
                if self.stats["success_count"] > 0 else 0
            )
        }
    
    def reset_stats(self):
        """重置統計資料"""
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "last_request_time": None
        }
    
    def get_available_models(self) -> List[str]:
        """獲取可用模型列表"""
        return [
            "gemini-pro",
            "gemini-pro-vision",
            "gemini-ultra"
        ]
    
    def validate_message_format(self, messages: List[Dict[str, str]]) -> bool:
        """驗證訊息格式"""
        if not isinstance(messages, list) or len(messages) == 0:
            return False
        
        valid_roles = {"system", "user", "assistant"}
        
        for message in messages:
            if not isinstance(message, dict):
                return False
            
            if "role" not in message or "content" not in message:
                return False
            
            if message["role"] not in valid_roles:
                return False
            
            if not isinstance(message["content"], str) or len(message["content"].strip()) == 0:
                return False
        
        return True

# 全域客戶端實例（懶加載）
_gemini_client: Optional[GeminiClient] = None

def get_gemini_client() -> GeminiClient:
    """獲取 Gemini 客戶端實例"""
    global _gemini_client
    if _gemini_client is None:
        _gemini_client = GeminiClient()
    return _gemini_client